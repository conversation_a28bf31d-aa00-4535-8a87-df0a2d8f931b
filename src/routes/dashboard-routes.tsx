import React from 'react';
import { Route, Router, Switch } from 'wouter';
import FullLoader from '~/components/library/loader/full-loader';
import AppointmentCalendar from '~/screens/appointment/appointmentCalender';
import ViewPA from '~/screens/appointment/viewPA';
import AttendeesList from '~/screens/classes/attendees-list';
import ClassesListing from '~/screens/classes/classes-listing';
import CoursesListing from '~/screens/courses/courses-listing';
import GymUserProfile from '~/screens/gym/gymUserProfile';
import PaymentScreen from '~/screens/payment pages/payment';
import PointOfSales from '~/screens/pointOfSale/pos-screen';
import AddAppointmentService from '~/screens/services-and-products/addAppointmentService';
import AddClass from '~/screens/services-and-products/addClass';
import AddAppointmentType from '~/screens/services-and-products/appAppointmentType';
import AppointmentsTypes from '~/screens/services-and-products/appointments-types';
import BookingTypes from '~/screens/services-and-products/booking-type';
import Classes from '~/screens/services-and-products/classes';
import Courses from '~/screens/services-and-products/courses';
import Bookings from '~/screens/bookings/bookingListing';
import CreatePricing from '~/screens/services/createPricing';
import PricingListing from '~/screens/services/pricingListing';
import SettingScreen from '~/screens/settings/setting-screen';
import CreateStaffPayRate from '~/screens/staff/create-staff-pay-rate';
import StaffOnboard from '~/screens/staff/staff-onboard';
import TestScreen from '~/screens/test-screen';
import UserListing from '~/screens/user/userListing';
import UserProfile from '~/screens/user/userProfile';
import classesPriceList from '~/screens/classes/classes-price-list';
import CourseCardList from '~/screens/courses/course-card-listing';
import SettingNew from '~/screens/settings/setting-new';
import OrderListing from '~/screens/order/order-listing';
import OrderDetail from '~/screens/order/order-detail';
import BundlePricing from '~/screens/services/bundlePricing';
import announcementsListing from '~/screens/announcements/announcements-listing';
import CreateAnnouncement from '~/screens/announcements/create-announcement';
import ProductListing from '~/screens/merchandise/product-listing';
import CreateProduct from '~/screens/merchandise/create-product';
import StoresListing from '~/screens/merchandise/stores/stores-listing';
import StoreDetails from '~/screens/merchandise/stores/store-details';
import CategoryListing from '~/screens/merchandise/category/categoryListing';
import AddNewCategory from '~/screens/merchandise/category/addNewCategory';
import AttributeListings from '~/screens/merchandise/attribute/attributeListing';
import SubAttributeListing from '~/screens/merchandise/attribute/subAttributeListing';
import AddSubAttribute from '~/screens/merchandise/attribute/addSubAttribute';
import PaymentMethodListing from '~/screens/payment-method-listing';
import ClientOnboarding from '~/screens/client-onboarding';
import SuperAdminPaymentMethodListing from '~/screens/superadmin-payment-methods';
import RolePermissions from '~/screens/super-admin/roles-permissions';
import EditPolicy from '~/screens/super-admin/edit-policy';
import EditPermissions from '~/screens/super-admin/edit-permissions';
import OrganizationListing from '~/screens/organization/organizationLisitng';
import AttributeTypeListing from '~/screens/attributes/attributeTypelist';

// Lazy loaded components
import CourseAttendeesList from '~/screens/courses/course-attendese-list';
import Reports from '~/screens/reports/reports';
import OrderConfirmation from '~/screens/payment pages/payment components/order-confirmation';
import PoliciesList from '~/screens/super-admin/policies-list';
import DiscountListing from '~/screens/services/discountListing';
import ClassAttendeesList from '~/screens/classes/class-attendese-list';
const ClassPayRates = React.lazy(() => import('~/screens/pay-rates'));
import WavierForm from '~/screens/customers/wavier/wavierForm';
import CreateCourseDetails from '~/screens/courses/create-course-details';
import RevenueCategory from '~/screens/settings/revenue-category';
import LeadSms from '~/screens/customers/lead-sms';
import LeadDetails from '~/screens/customers/lead-details';
import StoreDiscountListing from '~/screens/merchandise/stores/storeDiscountListing';
import Dashboard from '~/screens/home/<USER>';
const SetupChecklist = React.lazy(
    () => import('~/screens/home/<USER>')
);
const CourseCustomerList = React.lazy(
    () => import('~/screens/courses/courses-customer')
);

const GymLocation = React.lazy(() => import('~/screens/home/<USER>'));
const CreateOrganization = React.lazy(
    () => import('~/screens/organization/create-organization')
);
const OrganizationProfile = React.lazy(
    () => import('~/screens/organization/organization-profile')
);
const CreateAttribute = React.lazy(
    () => import('~/screens/attributes/createAttribute')
);

const AmenitiesListing = React.lazy(
    () => import('~/screens/amenities/amenitiesListing')
);
const CreateAmenities = React.lazy(
    () => import('~/screens/amenities/createAmenities')
);
const AttributeListing = React.lazy(
    () => import('~/screens/attributes/attributeListing')
);
const CustomerListing = React.lazy(
    () => import('~/screens/customers/customerListing')
);
const ClientTabNavigation = React.lazy(
    () => import('~/screens/customers/clientListingTab')
);
const CreateGym = React.lazy(() => import('~/screens/gym/createGym'));
const GymListing = React.lazy(() => import('~/screens/gym/gymListing'));
const SessionListing = React.lazy(
    () => import('~/screens/sessions/sessionListing')
);
const CreateTrainer = React.lazy(
    () => import('~/screens/trainers/createTrainer')
);
const StaffListing = React.lazy(
    () => import('~/screens/trainers/staff-listing')
);
const StaffDetails = React.lazy(() => import('~/screens/staff/staff-details'));
const RoomListing = React.lazy(() => import('~/screens/rooms/roomsListing'));
const CreateRoom = React.lazy(() => import('~/screens/rooms/createRoom'));
const FeatureTabListing = React.lazy(
    () => import('~/screens/feature-tab/featureTabListing')
);
const CreateFeatureTab = React.lazy(
    () => import('~/screens/feature-tab/createFeatureTab')
);
const MembershipListing = React.lazy(
    () => import('~/screens/membership/membershipListing')
);
const CreateMembership = React.lazy(
    () => import('~/screens/membership/createMembership')
);
const RouteNotFound = React.lazy(() => import('~/screens/404'));

const Routes: React.FC = () => {
    return (
        <Router>
            <React.Suspense fallback={<FullLoader state={true} />}>
                <Switch>
                    <Route path="/facilities" component={GymListing} />
                    <Route path="/customers" component={ClientTabNavigation} />
                    <Route
                        path="/facility/create-facility/:id"
                        component={CreateGym}
                    />
                    <Route path="/staffs" component={StaffListing} />
                    <Route
                        path="/trainers/create-trainer"
                        component={CreateTrainer}
                    />
                    <Route path="/sessions" component={SessionListing} />
                    <Route path="/attribute" component={AttributeListing} />
                    <Route path="/amenities" component={AmenitiesListing} />
                    <Route path="/staff-details/:id" component={StaffDetails} />
                    <Route
                        path="/amenities/create-amenities/:id"
                        component={CreateAmenities}
                    />
                    <Route
                        path="/attribute-type-list"
                        component={AttributeTypeListing}
                    />
                    <Route path="/dashboard" component={Dashboard} />
                    <Route path="/setup-checklist" component={SetupChecklist} />
                    <Route
                        path="/facilities-location"
                        component={GymLocation}
                    />
                    <Route
                        path="/attribute/create-attribute/:id"
                        component={CreateAttribute}
                    />
                    <Route
                        path="/class-pay-rates/:staffId"
                        component={ClassPayRates}
                    />
                    <Route
                        path="/create-pay-rates"
                        component={CreateStaffPayRate}
                    />
                    <Route
                        path="/user-profile/:id"
                        component={GymUserProfile}
                    />
                    <Route
                        path="/organization"
                        component={OrganizationListing}
                    />
                    <Route
                        path="/organization-profile"
                        component={OrganizationProfile}
                    />
                    <Route
                        path="/create-organization/:id"
                        component={CreateOrganization}
                    />
                    <Route
                        path="/organizations"
                        component={OrganizationListing}
                    />
                    <Route
                        path="/organization-profile/:id"
                        component={OrganizationProfile}
                    />
                    <Route
                        path="/create-organization/:id"
                        component={CreateOrganization}
                    />
                    <Route path="/Staff-onboard" component={StaffOnboard} />
                    <Route path="/setting-old" component={SettingScreen} />
                    <Route path="/setting" component={SettingNew} />
                    <Route path="/setting/room" component={RoomListing} />
                    <Route
                        path="/setting/room/create-room/:id"
                        component={CreateRoom}
                    />
                    <Route
                        path="/setting/feature-tab"
                        component={FeatureTabListing}
                    />
                    <Route
                        path="/setting/feature-tab/create-feature/:id"
                        component={CreateFeatureTab}
                    />
                    <Route
                        path="/appointments"
                        component={AppointmentCalendar}
                    />
                    <Route path="/classes" component={Classes} />
                    <Route path="/add-class/:id" component={AddClass} />
                    <Route
                        path="/add-appointment-service/:id"
                        component={AddAppointmentService}
                    />
                    <Route
                        path="/add-appointment-type/:id/:subItemId"
                        component={AddAppointmentType}
                    />
                    <Route
                        path="/Appointments-types"
                        component={AppointmentsTypes}
                    />
                    <Route path="/courses" component={Courses} />
                    <Route path="/bookings" component={Bookings} />
                    <Route path="/pricing" component={PricingListing} />
                    <Route
                        path="/discount-management"
                        component={DiscountListing}
                    />
                    <Route
                        path="/create-pricing/:id"
                        component={CreatePricing}
                    />
                    <Route
                        path="/create-bundle-pricing/:id"
                        component={BundlePricing}
                    />
                    <Route path="/classes-listing" component={ClassesListing} />
                    <Route
                        path="/class-attendees/:id"
                        component={ClassAttendeesList}
                    />
                    <Route
                        path="/classes-price-listing"
                        component={classesPriceList}
                    />
                    <Route
                        path="/course-attendees/:id"
                        component={CourseAttendeesList}
                    />
                    <Route
                        path="/courses-customer-list/:id"
                        component={CourseCustomerList}
                    />
                    <Route path="/courses-listing" component={CoursesListing} />
                    <Route
                        path="/courses-card-listing/:id"
                        component={CourseCardList}
                    />
                    <Route path="/point-of-sales" component={PointOfSales} />
                    <Route
                        path="/order-confirmation/:orderId"
                        component={OrderConfirmation}
                    />
                    <Route
                        path="/point-of-sales/:userId/:clientId"
                        component={PointOfSales}
                    />
                    <Route path="/booking-types" component={BookingTypes} />
                    <Route path="/payment" component={PaymentScreen} />
                    <Route path="/view-appointment/:id" component={ViewPA} />
                    <Route path="/edit-appointment/:id" component={ViewPA} />
                    <Route path="/order-listing" component={OrderListing} />
                    <Route path="/order-detail" component={OrderDetail} />
                    <Route
                        path="/admin/payment-methods"
                        component={SuperAdminPaymentMethodListing}
                    />
                    <Route
                        path="/roles-super-admin"
                        component={RolePermissions}
                    />
                    <Route path="/edit-policy/:id" component={EditPolicy} />
                    <Route
                        path="/edit-permissions/:id"
                        component={EditPermissions}
                    />
                    <Route path="/policies" component={PoliciesList} />
                    <Route path="/test" component={TestScreen} />
                    {/* 404 Route */}
                    <Route
                        path="/setting/membership"
                        component={MembershipListing}
                    />
                    <Route
                        path="/setting/revenue-category"
                        component={RevenueCategory}
                    />
                    <Route
                        path="/setting/announcements"
                        component={announcementsListing}
                    />
                    <Route
                        path="/setting/create-announcements"
                        component={CreateAnnouncement}
                    />
                    <Route
                        path="/setting/update-announcements/:id"
                        component={CreateAnnouncement}
                    />
                    <Route
                        path="/payment-methods"
                        component={PaymentMethodListing}
                    />
                    <Route
                        path="/setting/membership/create-membership/:id"
                        component={CreateMembership}
                    />
                    <Route path="/product-listing" component={ProductListing} />
                    <Route
                        path="/create-product/:id"
                        component={CreateProduct}
                    />
                    <Route path="/store-listing" component={StoresListing} />
                    <Route
                        path="/store-detail/:storeId"
                        component={StoreDetails}
                    />
                    <Route
                        path="/store-detail/discount-management/:storeId"
                        component={StoreDiscountListing}
                    />
                    <Route path="/categories" component={CategoryListing} />
                    <Route
                        path="/add-categories/:id"
                        component={AddNewCategory}
                    />
                    <Route
                        path="/attribute-listing"
                        component={AttributeListings}
                    />
                    <Route
                        path="/attributes/sub-attribute-list/:id"
                        component={SubAttributeListing}
                    />
                    <Route
                        path="/attributes/sub-attribute-list/add-sub-attribute/:id/:attribute_id?"
                        component={AddSubAttribute}
                    />
                    <Route path="/reports" component={Reports} />
                    <Route path="/wavier-lead/:id" component={WavierForm} />
                    <Route
                        path="/course-details/:id"
                        component={CreateCourseDetails}
                    />
                    <Route path="/lead-sms" component={LeadSms} />
                    <Route path="/lead-details" component={LeadDetails} />
                    <Route
                        path="/client-onboarding"
                        component={ClientOnboarding}
                    />
                    <Route component={RouteNotFound} />
                </Switch>
            </React.Suspense>
        </Router>
    );
};

export default Routes;
