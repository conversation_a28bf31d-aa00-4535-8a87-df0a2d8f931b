import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ACTIVE_SERVICE_CATEGORY_LIST,
    CREATE_DISCOUNT,
    DISCOUNT_LIST,
    PRICING_LIST,
    SERVICE_CATEGORY_LIST,
    UPDATE_DISCOUNT,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import {
    deleteApi,
    getApi,
    patchApi,
    postApi,
    putApi,
} from '~/services/api-services';

// Get pricing packages by serviceId
export const PricingListForDiscounts: any = createAsyncThunk(
    'pricing-list-for-discounts',
    async (reqData: any) => {
        try {
            const response: any = await getApi(
                `${PRICING_LIST}?pageSize=1000&serviceId=${
                    reqData.serviceId
                }&itemType=${reqData.itemType ?? 'service'}`
            );
            // console.log('PricingListForDiscounts', response);
            return response;
        } catch (error: any) {
            console.log('Error PricingListForDiscounts API', error);
        }
    }
);
// Get pricing packages by promotionId
export const PricingListForDiscountId: any = createAsyncThunk(
    'pricing-list-for-discount-id',
    async (reqData: any) => {
        try {
            const response: any = await getApi(
                `${PRICING_LIST}?promotionId=${reqData.promotionId}&itemType=${reqData.itemType}`
            );
            // console.log('PricingListForDiscountId', response);
            return response;
        } catch (error: any) {
            console.log('Error PricingListForDiscountId API', error);
        }
    }
);
// Get DISCOUNTS LIST
export const DiscountList: any = createAsyncThunk(
    'discount-list',
    async (reqData: any) => {
        try {
            const { page, perPage, itemType, includedPricingIds } = reqData;
            const response: any = await getApi(
                `${DISCOUNT_LIST}?page=${page}&perPage=${perPage}&orderBy=updatedAt&orderDirection=desc&itemType=${
                    itemType ?? 'service'
                }&includedPricingIds=${includedPricingIds ?? ''}`
            );
            // console.log('DiscountList', response);
            return response;
        } catch (error: any) {
            console.log('Error DiscountList API', error);
            Alertify.error(error.message[0]);
        }
    }
);
// Get DISCOUNTS LIST WITH PRICING ID
export const DiscountListWithPricingId: any = createAsyncThunk(
    'discount-list',
    async (reqData: any) => {
        try {
            const { page, perPage, pricingId } = reqData;
            const response: any = await getApi(
                `${DISCOUNT_LIST}?page=${page}&perPage=${perPage}&orderBy=updatedAt&orderDirection=desc&pricingId=${pricingId}`
            );
            // console.log('DiscountList', response);
            return response;
        } catch (error: any) {
            console.log('Error DiscountList API', error);
            Alertify.error(error.message[0]);
        }
    }
);
// Create Discount
export const CreateDiscount: any = createAsyncThunk(
    'create-discount',
    async (reqData: any) => {
        try {
            const response: any = await postApi(CREATE_DISCOUNT, reqData);
            // console.log('CreateDiscount', response);
            return response;
        } catch (error: any) {
            console.log('Error CreateDiscount API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Get DISCOUNTS details
export const DiscountDetails: any = createAsyncThunk(
    'discount-details',
    async (reqData: any) => {
        try {
            const { discountId } = reqData;
            const response: any = await getApi(`/promotions/${discountId}/get`);
            // console.log('DiscountDetails', response);
            return response;
        } catch (error: any) {
            console.log('Error DiscountDetails API', error);
            // Alertify.error(error.message[0]);
        }
    }
);

// Get All Service Category List
export const AllServiceCategoryListForDiscounts: any = createAsyncThunk(
    'AllServiceCategoryListForDiscounts',
    async () => {
        try {
            const response = await postApi(ACTIVE_SERVICE_CATEGORY_LIST);
            return response;
        } catch (error: any) {
            console.log(
                'Error fetch AllServiceCategoryListForDiscounts API ',
                error
            );
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Update Discount
export const UpdateDiscount: any = createAsyncThunk(
    'update-discount',
    async (reqData: any) => {
        try {
            const { id, ...restData } = reqData;
            const response: any = await putApi(
                `${UPDATE_DISCOUNT}/${id}/update`,
                restData.formData
            );
            console.log('UpdateDiscount', response);
            return response;
        } catch (error: any) {
            console.log('Error UpdateDiscount API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
// Update Discount Status
export const UpdateDiscountStatus: any = createAsyncThunk(
    'update-discount-status',
    async (reqData: any) => {
        try {
            const { discountId, ...restData } = reqData;
            const response: any = await patchApi(
                `${UPDATE_DISCOUNT}/${discountId}/status`,
                restData
            );
            console.log('UpdateDiscount', response);
            return response;
        } catch (error: any) {
            console.log('Error UpdateDiscountStatus API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Delete Discount
export const DeleteDiscount: any = createAsyncThunk(
    'delete-discount',
    async (reqData: any) => {
        try {
            const { discountId } = reqData;
            const response: any = await deleteApi(
                `${UPDATE_DISCOUNT}/${discountId}/delete`
            );
            // console.log('DeleteDiscount', response);
            return response;
        } catch (error: any) {
            console.log('Error DeleteDiscount API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Discounts on the pricing page
export const DiscountListOnPricePage: any = createAsyncThunk(
    'discount-list-on-price-page',
    async (reqData: any) => {
        try {
            const { pricingId, currentPage, pageSize } = reqData;
            let url = `${UPDATE_DISCOUNT}/item/${pricingId}/discounts`;

            // Add pagination parameters if provided
            if (currentPage && pageSize) {
                url += `?page=${currentPage}`;
            }

            const response: any = await getApi(url);
            // console.log('DiscountList', response);
            return response;
        } catch (error: any) {
            console.log('Error DiscountList API', error);
            Alertify.error(error.message[0]);
        }
    }
);

// Assign discount to price
export const AssignDiscountToPrice: any = createAsyncThunk(
    'discount-list-on-price-page',
    async (reqData: any) => {
        try {
            const { pricingId, ...restData } = reqData;
            const response: any = await postApi(
                `${UPDATE_DISCOUNT}/${pricingId}/apply-to-items`,
                restData
            );
            // console.log('DiscountList', response);
            return response;
        } catch (error: any) {
            console.log('Error AssignDiscountToPrice API', error);
            Alertify.error(error.message[0]);
        }
    }
);
