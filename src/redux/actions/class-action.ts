import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CLASS_SCHEDULING_LISTING,
    CLASS_PARTICIPANT_LIST,
    CLASSES_CUSTOMER_LIST,
    UPDATE_CLASS_SCHEDULE,
    CLASS_SCHEDULING,
    CLASS_ENROLL_CUSTOMER,
    CREATE_CLASS_SCHEDULE,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';

// Shared error handler
const handleThunkError = (error: any) => {
    console.error('API Error:', error);
    Alertify.error(error?.message?.[0] || 'Something went wrong');
    return Promise.reject(error);
};

export const createClassScheduling = createAsyncThunk(
    'createClassScheduling',
    async (payload: any) => {
        try {
            return await postApi(CREATE_CLASS_SCHEDULE, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classSchedulingList = createAsyncThunk(
    'classSchedulingList',
    async (payload: any) => {
        try {
            return await getApi(CLASS_SCHEDULING_LISTING, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const updateClassScheduling = createAsyncThunk(
    'updateClassScheduling',
    async (
        {
            courseId,
            isActive,
            page,
            pageSize,
            description,
            image,
            isFeatured,
        }: any,
        { dispatch }
    ) => {
        try {
            const response = await patchApi(UPDATE_CLASS_SCHEDULE, {
                courseId,
                isActive,
                description,
                image,
                isFeatured,
            });
            dispatch(classSchedulingList({ page, pageSize }));
            return response;
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classesCustomerList = createAsyncThunk(
    'classesCustomerList',
    async (payload: any) => {
        try {
            return await getApi(CLASSES_CUSTOMER_LIST, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classesParticipantList = createAsyncThunk(
    'classesParticipantList',
    async (payload: any) => {
        try {
            return await getApi(CLASS_PARTICIPANT_LIST, payload);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classCancelScheduling = createAsyncThunk(
    'classCancelScheduling',
    async ({ schedulingId }: { schedulingId: string }) => {
        try {
            return await patchApi(`${CLASS_SCHEDULING}/${schedulingId}/cancel`);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classDeleteScheduling = createAsyncThunk(
    'classDeleteScheduling',
    async ({ schedulingId }: { schedulingId: string }) => {
        try {
            return await deleteApi(
                `${CLASS_SCHEDULING}/${schedulingId}/delete`
            );
        } catch (error) {
            return handleThunkError(error);
        }
    }
);

export const classEnrollClient = createAsyncThunk(
    'classEnrollClient',
    async (payload: any) => {
        try {
            return await postApi(CLASS_ENROLL_CUSTOMER, payload);
        } catch (error) {
            console.error('API Error:', error);
            return error;
        }
    }
);

export const getClassSchedulingDetails = createAsyncThunk(
    'getClassSchedulingDetails',
    async ({ schedulingId }: { schedulingId: string }) => {
        try {
            return await getApi(`${CLASS_SCHEDULING}/${schedulingId}/get`);
        } catch (error) {
            return handleThunkError(error);
        }
    }
);
