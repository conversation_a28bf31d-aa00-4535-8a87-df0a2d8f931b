import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    SALES_BY_CATEGORY_REPORT,
    SALES_BY_EMPLOY_REPORT,
    SALES_BY_ITEM_REPORT,
    SALES_REPORT,
    SCHEDULING_REPORT,
    Z_OUT_REPORT,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { getApi, patchApi, postApi } from '~/services/api-services';

export const SchedulingReport: any = createAsyncThunk(
    'SchedulingReport',
    async (payload: any) => {
        try {
            const response = await postApi(SCHEDULING_REPORT, payload);
            return response;
        } catch (error: any) {
            console.log('Error scheduling report API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const SalesReport: any = createAsyncThunk(
    'SalesReport',
    async (payload: any) => {
        try {
            const response = await postApi(SALES_REPORT, payload);
            return response;
        } catch (error: any) {
            console.log('Error Sales report API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const SalesByEmployReport: any = createAsyncThunk(
    'SalesReport',
    async (payload: any) => {
        try {
            const response = await postApi(SALES_BY_EMPLOY_REPORT, payload);
            return response;
        } catch (error: any) {
            console.log('Error sales by employ report API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const SalesByCategoryReport: any = createAsyncThunk(
    'SalesReport',
    async (payload: any) => {
        try {
            const response = await postApi(SALES_BY_CATEGORY_REPORT, payload);
            return response;
        } catch (error: any) {
            console.log('Error sales by category report API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const SalesByItemReport: any = createAsyncThunk(
    'SalesReport',
    async (payload: any) => {
        try {
            const response = await postApi(SALES_BY_ITEM_REPORT, payload);
            return response;
        } catch (error: any) {
            console.log('Error sales by item report API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const ZoutReportExport: any = createAsyncThunk(
    'z-out-Report',
    async (payload: any) => {
        try {
            const axiosHeaders = {
                'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
            };

            const response = await postApi(Z_OUT_REPORT, payload, {
                ...axiosHeaders,
                responseType:
                    payload.responseType === 'pdf' ||
                    payload.responseType === 'stream'
                        ? 'arraybuffer'
                        : 'json',
            });

            return response.data;
        } catch (error: any) {
            console.error('Z-Out report export failed:', error);
            Alertify.error(error?.message?.[0] || 'Something went wrong');
            return Promise.reject(error);
        }
    }
);
