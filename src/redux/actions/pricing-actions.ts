//---------------------pricing-list action--------------------

import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    ASSIGN_PRICING_TO_SUBTYE,
    BUNDLE_PRICING_LIST,
    CREATE_BUNDLE_PRICING,
    CREATE_PRICING,
    COPY_PRICING,
    PRICE_LIST,
    PRICE_LIST_BY_SERVICE_CATEGORY,
    PRICING_DETAILS,
    PRICING_LIST_BY_ACTIVE_STATUS,
    PRICING_LIST_BY_SUBTYPE,
    PURCHASE_PRICING_LIST_BY_CLASS_TYPE,
    ACTIVE_PURCHASE_PRICING_LIST_BY_USERID,
    PURCHASE_PRICING_PACKAGES,
    REMOVE_PRICING_TO_SUBTYE,
    UPDATE_PRICING_STATUS,
    INACTIVE_PURCHASE_PRICING_LIST_BY_USERID,
    CREATE_CUSTOM_PACKAGE,
    CUSTOM_PACKAGE_LIST,
    CUSTOM_PACKAGE_DETAILS,
    CUSTOM_PACKAGE_UPDATE,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';

interface PricingListParams {
    page: number;
    pageSize: number;
    search?: string;
    fetchWithBundled?: boolean;
}

//---------------------pricing listing------------------------

export const PricingList: any = createAsyncThunk(
    'Pricing-list',
    async ({
        page = 1,
        pageSize = 10,
        search = undefined,
    }: PricingListParams) => {
        try {
            const response = await postApi(PRICE_LIST, {
                page,
                pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

//---------------------pricing listing by active status------------------------

export const PricingListByActiveStatus: any = createAsyncThunk(
    'Pricing-list-active-status',
    async ({ page, pageSize, search, fetchWithBundled }: PricingListParams) => {
        try {
            const response = await postApi(PRICING_LIST_BY_ACTIVE_STATUS, {
                page,
                pageSize,
                search,
                fetchWithBundled,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
); //---------------------Bunlde pricing listing by active status------------------------

export const BundlePricingListByActiveStatus: any = createAsyncThunk(
    'bundle-Pricing-list-active-status',
    async ({ page, pageSize, search, fetchWithBundled }: PricingListParams) => {
        try {
            const response = await postApi(PRICING_LIST_BY_ACTIVE_STATUS, {
                page,
                pageSize,
                search,
                fetchWithBundled,
            });
            return { response, page };
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Pricing ------------------ */

export const CreatePricingAPI: any = createAsyncThunk(
    'createPricingAPI',
    async (reqData: any) => {
        try {
            const response = await postApi(CREATE_PRICING, reqData);
            Alertify.success('Pricing created successfully');
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Pricing ------------------ */

export const CopyPricingAPI: any = createAsyncThunk(
    'copyPricingAPI',
    async (reqData: any) => {
        try {
            const response = await postApi(COPY_PRICING, reqData);
            Alertify.success('Pricing copied successfully');
            return response;
        } catch (error: any) {
            console.log('Error CopyPricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Pricing Details ------------------ */
export const PricingDetails: any = createAsyncThunk(
    'PricingDetails',
    async ({ pricingId }: any) => {
        try {
            const response = await getApi(`${PRICING_DETAILS}/${pricingId}`);
            return response;
        } catch (error: any) {
            console.log('Error fetch Services Details API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Delete Pricing ------------------ */
export const DeletePricing: any = createAsyncThunk(
    'DeletePricing',
    async ({ pricingId }: any) => {
        try {
            const response = await deleteApi(`${PRICING_DETAILS}/${pricingId}`);
            Alertify.success('Pricing deleted successfully');
            return response;
        } catch (error: any) {
            console.log('Error in delete pricing API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Pricing ------------------ */

export const UpdatePricingAPI: any = createAsyncThunk(
    'updatePricingAPI',
    async (reqData: any) => {
        try {
            const response = await patchApi(CREATE_PRICING, reqData);
            Alertify.success('Pricing updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Update Pricing Status ------------------ */

/*------------------- Price List By Service Category ------------------ */

export const PriceListByServiceCategory: any = createAsyncThunk(
    'PriceListByServiceCategory',
    async (reqData: any) => {
        try {
            const response = await postApi(
                PRICE_LIST_BY_SERVICE_CATEGORY,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Price List By Sub type ------------------ */

export const PriceListBySubType: any = createAsyncThunk(
    'PriceListBySubType',
    async (reqData: any) => {
        try {
            const response = await postApi(PRICING_LIST_BY_SUBTYPE, reqData);
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*-------------------Assign Pricing to Sub type ------------------ */

export const AssignPricingToSubType: any = createAsyncThunk(
    'AssignPricingToSubType',
    async (reqData: any) => {
        try {
            const response = await postApi(ASSIGN_PRICING_TO_SUBTYE, reqData);
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*-------------------Remove Pricing to Sub type ------------------ */

export const RemovePricingToSubType: any = createAsyncThunk(
    'RemovePricingToSubType',
    async (reqData: any) => {
        try {
            const response = await postApi(REMOVE_PRICING_TO_SUBTYE, reqData);
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Purchase pricing package ----------------- */

export const PurchasePricingPackages: any = createAsyncThunk(
    'PurchagePricingPackages',
    async (reqData: any) => {
        try {
            const response = await postApi(PURCHASE_PRICING_PACKAGES, reqData);
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Purchase pricing package by client ----------------- */

export const ActivePurchasePricingPackagesByClient: any = createAsyncThunk(
    'ActivePurchasePricingPackagesByClient',
    async ({ userId, page, pageSize }: any) => {
        try {
            const response = await postApi(
                `${ACTIVE_PURCHASE_PRICING_LIST_BY_USERID}/${userId}`,
                {
                    page,
                    pageSize,
                }
            );
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const InActivePurchasePricingPackagesByClient: any = createAsyncThunk(
    'InActivePurchasePricingPackagesByClient',
    async ({ userId, page, pageSize }: any) => {
        try {
            const response = await postApi(
                `${INACTIVE_PURCHASE_PRICING_LIST_BY_USERID}/${userId}`,
                {
                    page,
                    pageSize,
                }
            );
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

//---------------------Bundle pricing listing------------------------

export const BundlePricingList: any = createAsyncThunk(
    'Bundle-Pricing-list',
    async ({
        page = 1,
        pageSize = 10,
        search = undefined,
    }: PricingListParams) => {
        try {
            const response = await postApi(BUNDLE_PRICING_LIST, {
                page,
                pageSize,
                search,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Create Pricing ------------------ */

export const CreateBundlePricingAPI: any = createAsyncThunk(
    'CreateBundlePricingAPI',
    async (reqData: any) => {
        try {
            const response = await postApi(CREATE_BUNDLE_PRICING, reqData);
            Alertify.success('Bundle pricing created successfully');
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*-------------------Bundle  Pricing Details ------------------ */
export const BundlePricingDetails: any = createAsyncThunk(
    'BundlePricingDetails',
    async ({ pricingId }: any) => {
        try {
            const response = await getApi(`${PRICING_DETAILS}/${pricingId}`);
            return response;
        } catch (error: any) {
            console.log('Error fetch Services Details API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*-------------------Bundle Update Pricing ------------------ */

export const BundleUpdatePricingAPI: any = createAsyncThunk(
    'BundleUpdatePricingAPI',
    async (reqData: any) => {
        try {
            const response = await patchApi(CREATE_BUNDLE_PRICING, reqData);
            Alertify.success('Bundle pricing updated successfully');
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const UpdatePricingStatus: any = createAsyncThunk(
    'UpdatePricingStatus',
    async (
        { packageId, status, page, pageSize, action }: any,
        { dispatch }
    ) => {
        try {
            console.log(action);
            const response = await patchApi(
                `${UPDATE_PRICING_STATUS}/${packageId}`,
                {
                    status,
                }
            );
            Alertify.success('Pricing updated successfully');
            if (action === 'BundlePricingList') {
                dispatch(BundlePricingList({ page, pageSize }));
            } else {
                dispatch(PricingList({ page, pageSize }));
            }
            return response;
        } catch (error: any) {
            console.log('Error UpdatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*-------------------create custom Pricing ------------------ */

export const CreateCustomPricingAPI: any = createAsyncThunk(
    'createCustomPricingAPI',
    async (reqData: any) => {
        try {
            const response = await postApi(CREATE_CUSTOM_PACKAGE, reqData);
            Alertify.success('Custom Pricing created successfully');
            console.log(
                'Custom Pricing created successfully response',
                response
            );
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Custom Pricing Listing ------------------ */

export const CustomPricingListAPI: any = createAsyncThunk(
    'CustomPricingListAPI',
    async (reqData: any) => {
        try {
            const response = await postApi(CUSTOM_PACKAGE_LIST, reqData);
            // Alertify.success('Custom Pricing created successfully');
            console.log(
                'Custom Pricing Listing successfully response',
                response
            );
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Custom Pricing Details ------------------ */

export const CustomPricingDetails: any = createAsyncThunk(
    'CustomPricingDetails',
    async ({ customPricingId }: any) => {
        try {
            const response = await getApi(
                `${CUSTOM_PACKAGE_DETAILS}/${customPricingId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch Services Details API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
/*------------------- Custom Pricing UPDATE ------------------ */

export const CustomPricingUpdateAPI: any = createAsyncThunk(
    'CustomPricingUpdateAPI',
    async (reqData: any) => {
        try {
            const response = await patchApi(CUSTOM_PACKAGE_UPDATE, reqData);
            Alertify.success('Custom pricing update successfully');
            console.log(
                'Custom Pricing update successfully response',
                response
            );
            return response;
        } catch (error: any) {
            console.log('Error CreatePricing API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
