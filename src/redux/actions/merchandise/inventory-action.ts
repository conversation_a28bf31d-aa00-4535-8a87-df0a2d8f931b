import { createAsyncThunk } from '@reduxjs/toolkit';
import { postApi, deleteApi, getApi, patchApi } from '~/services/api-services';
import { SEARCH_BY_SKU_DETAILS, CREATE_NEW_INVENTORY, INVENTORY_LIST, UPDATE_INVENTORY, STORE_INVENTORY_LIST } from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import dayjs from 'dayjs';



export const SearchProductBySkuDetails: any = createAsyncThunk(
    'products/searchBySku', // Better naming convention for action type
    async (reqData,
        { rejectWithValue }) => {
        try {
            const res = await postApi(SEARCH_BY_SKU_DETAILS, reqData);
            return res;

        } catch (error: any) {
            console.error('SearchProductBySkuDetails Error:', error);
            return rejectWithValue(error.message || 'Something went wrong');
        }
    }
);


export const CreateNewInventory: any = createAsyncThunk(
    'CreateNewInventory',
    async (reqData: any) => {
        try {
            const res = await postApi(CREATE_NEW_INVENTORY, reqData);
            Alertify.success('Inventory created successfully');
            return { res };
        } catch (error: any) {
            console.error(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
export const InventoryList = createAsyncThunk(
    'inventory/list',
    async (payLoad: any) => {
        try {
            const response = await postApi(INVENTORY_LIST, payLoad);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
);
export const updateInventory: any = createAsyncThunk(
    'UpdateInventory',
    async ({ reqData, inventoryId }: any,) => {
        try {
            const response = await patchApi(`${UPDATE_INVENTORY}/${inventoryId}`, {
                ...reqData,
            });
            Alertify.success('Inventory Update Successfully')
        } catch (error: any) {
            console.error(error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const _ManipulateInventoryList = (data = []) => {
    let inventory_arr: any[] = [];

    data.forEach((item: any) => {
        let obj;
        if (item.productId.length === 0) {
            return;
        }
        if (item.productType === 'simple') {
            obj = {
                name: item.productDetails?.name,
                productId: item.productDetails?._id,
                hsn: item.productDetails?.hsn,
                sku: item.productDetails?.sku,
                productType: item?.productType,
                batchId: item?.batchId,
                salePrice: item?.salePrice,
                mrp: item?.mrp,
                tax: item.productDetails?.gst,
                quantity: item?.quantity,
                expiryDate: item?.expiryDate ? dayjs(item?.expiryDate).format('DD/MM/YYYY') : null,
                discount: item?.discount,
                _id: item?._id,
                price: item?.salePrice,
                inventoryId: item?._id,
                discountPrice: item?.discountPrice,
                discountedValue:(item.salePrice * item.discount)/100
            };
            inventory_arr = [...inventory_arr, obj];
        } else if (item.productType === 'variable') {
            obj = {
                name: item?.productVariantDetails?.name,
                productId: item?.productDetails?._id,
                hsn: item.productVariantDetails?.hsnCode,
                sku: item?.productVariantDetails?.sku,
                productVariantId: item?.productVariantDetails?._id,
                productType: item?.productType,
                batchId: item?.batchId,
                salePrice: item?.salePrice,
                mrp: item?.mrp,
                tax: item?.productDetails?.gst,
                quantity: item?.quantity,
                expiryDate: item?.expiryDate,
                discount: item?.discount,
                _id: item?._id,
                price: item?.salePrice,
                inventoryId: item?._id,
                discountPrice: item?.discountPrice,
                discountedValue:(item.salePrice * item.discount)/100

            };
            inventory_arr = [...inventory_arr, obj];
        }
    });
    return inventory_arr;
}
export const storeInventory: any = createAsyncThunk(
    'inventory/store-list',
    async (payLoad: any) => {
        try {
            const response = await postApi(STORE_INVENTORY_LIST);
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
)
