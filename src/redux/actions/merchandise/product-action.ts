import { createAsyncThunk } from '@reduxjs/toolkit';
import { CREATE_PRODUCT, ATTRIBUTES_LIST, PRODUCT_LIST, GET_PRODUCT_DETAILS, UPDATE_PRODUCT, UPDATE_PRODUCT_STATUS,DELETE_PRODUCT, EXPORT_PRODUCT_LIST, BULK_UPLOAD_PRODUCT, BULK_UPDATE_PRODUCT } from '~/constants/api-constants';
import { postApi, deleteApi, getApi, patchApi } from '~/services/api-services';
import Alertify from '~/services/alertify';

/***************************Create Sub Attribute*********************************** */
export const CreateNewProduct = createAsyncThunk(
    'product/create-product',
    async (payload: any,) => {
        try {
            const apiPayLoad = {
                ...payload,
                gst: Number(payload.gst) || 0,
                status: true,
                attributes: { ...payload.attributes, brand: payload.brand },
                itemCode: payload.sku.trim(),
            }
            delete apiPayLoad.brand;

            const res = await postApi(CREATE_PRODUCT, apiPayLoad
            );
            Alertify.success("Product create successfully")
            return res

        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
)
export const GetAttributesValues = createAsyncThunk(
    'GetAttributesValues',
    async ({ key = 'search', value = '', page = 1, pageSize = 10, attribute }: any, { rejectWithValue }) => {
        try {
            const response = await postApi(ATTRIBUTES_LIST, { [key]: value, page, pageSize, attribute });
            return response.data;
        } catch (error: any) {
            console.error('Error:', error);
            return rejectWithValue(error?.response?.data || error);
        }
    }
);

export const ProductListData = createAsyncThunk(
    'products/products-list',
    async ({ key = 'search', value = '', page = 1, pageSize = 10, filter, search = '' }: any) => {
        try {
            const response = await postApi(PRODUCT_LIST, {
                [key]: value,
                page,
                pageSize,
                filter,
                search
            });
            // console.log('Response-----------', response);
            // Alertify.success('Category created successfully')
            return response.data;
        } catch (error) {
            console.error('Error:', error);
            return Promise.reject();
        }
    }
);
export function ManipulateProductLists(data: any) {
    return data?.map((item: any) => ({
        key: item?._id,
        _id: item?._id,
        name: item?.name,
        sku: item?.sku,
        hsn: item?.hsn || '',
        gst: `${item?.gst}%`,
        status: item?.status,
        type: item?.type,
        category: item?.parentCategory?.name,
        variants: item?.variants,
    }));
}
export const GetProductDetails = createAsyncThunk(
    'GetProductDetails',
    async ({ id }: any) => {
        try {
            const res = await getApi(`${GET_PRODUCT_DETAILS}/${id}`);
            return { res };
        } catch (error) {
            console.error(error);
            return Promise.reject();
        }
    }
);
export const UpdateProduct = createAsyncThunk(
    'UpdateProduct',
    async ({ reqData, id }: any) => {
        try {
            const apiPayLoad = {
                ...reqData,
                gst: Number(reqData.gst) || undefined,
                status: true,
                attributes: { ...reqData.attributes, brand: reqData.brand },
                itemCode: reqData.sku.trim(),
            }
            delete apiPayLoad.brand;
            const res = await patchApi(`${UPDATE_PRODUCT}/${id}`, apiPayLoad);
            Alertify.success('Product is updated successfully');
            return { res };
        } catch (error: any) {
            console.error(error.message);
            Alertify.error(error.message[0])
            return Promise.reject();
        }
    }
);
export const updateProductStatus = createAsyncThunk(
    'Update status',
    async ({ status, productId }: any, { dispatch, rejectWithValue }) => {
        try {

            const response = await patchApi(`${UPDATE_PRODUCT_STATUS}/${productId}`, {
                status,
            });
            await dispatch(ProductListData({ page: 1, pageSize: 10, search: "" }));
            Alertify.success('Product  Status updated successfully');
            return response;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error.response?.data ||
                'Failed to update the status of the Room'
            );
        }
    }
);
export const DeleteProduct = createAsyncThunk(
    'Delete Attribute',
    async (id: string, { dispatch, rejectWithValue }) => {
        try {
            await deleteApi(`${DELETE_PRODUCT}/${id}`);
            Alertify.success('Product deleted successfully');
            return id;
        } catch (error: any) {
            Alertify.error(error.message[0]);
            return rejectWithValue(
                error.response?.data || 'Failed to delete Room'
            );
        }
    }
);
export const ExportProduct = createAsyncThunk(
    'Export Product',
    async (payload: any, { rejectWithValue }) => {
      try {
        const response = await postApi(
          EXPORT_PRODUCT_LIST,
          { ...payload },
          {
            responseType: 'blob',
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`, // if required
              'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
            },
          }
        );
  
        const contentDisposition = response.headers['content-disposition'];
        const date = new Date();
        const istOffset = 330 * 60 * 1000; // IST is UTC+5:30
        const istDate = new Date(date.getTime() + istOffset);
        const istTimestamp = istDate.toISOString().replace(/T/, '_').replace(/:/g, '-').split('.')[0];
        const fileName = `product_${istTimestamp}`
        const filename =`${fileName}.${payload.fileType}`;
  
        const contentType = response.headers['content-type'];
        if (!contentType?.includes('application') && !contentType?.includes('csv')) {
          const reader = new FileReader();
          reader.onload = () => {
            const text = reader.result;
            Alertify.error(`Export failed: ${text}`);
          };
          reader.readAsText(response.data);
          return rejectWithValue('Export failed with unexpected content');
        }
  
        const blob = new Blob([response.data], { type: contentType });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(link.href);
  
        return { success: true };
      } catch (error: any) {
        console.error(error);
        Alertify.error(
          error?.response?.data?.message || 'Failed to export the products'
        );
        return rejectWithValue(error.response?.data || 'Failed to export the products');
      }
    }
  );

  export const BulkUploadProduct = createAsyncThunk(
    'BulkUploadProduct',
    async (payload: any) => {
        try {
            const res = await postApi(BULK_UPLOAD_PRODUCT, payload, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            Alertify.success('Product create successfully')
            return res

        } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
)
export const bulkupdateProduuct = createAsyncThunk(
    'BulkupdateProduuct',
    async (payload: any) => {
        try {
            const res = await postApi(BULK_UPDATE_PRODUCT, payload, {
                headers: {
                    'Content-Type':'multipart/form-data',
                },
            });
            Alertify.success('Product update successfully')
            return res
            } catch (error: any) {
            console.error('Error:', error);
            Alertify.error(error.message[0]);
            return Promise.reject();
        }
    }
    );
  