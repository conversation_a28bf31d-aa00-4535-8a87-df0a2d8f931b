import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    CHANGE_PASSWORD,
    FORGET_PASSWORD_REQUEST_OTP,
    FORGET_RESET_PASSWORD,
    LOGIN_ADMIN,
    LOGOUT_USER,
    SELECT_ORGANIZATION_LIST,
    SET_NEW_PASSWORD,
    VALIDATE_MODULE_PIN,
    VALIDATE_PIN,
    VERIFY_OTP,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { handleApiError, postApi } from '~/services/api-services';

/*----------------Login Admin ---------------- */

export const LoginUser: any = createAsyncThunk(
    'login-user',
    async (reqData: any, { getState }) => {
        try {
            const response = await postApi(LOGIN_ADMIN, reqData);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*----------------Set New Password ---------------- */

export const SetNewPassword: any = createAsyncThunk(
    'set-password',
    async (reqData: any, { getState }) => {
        try {
            const response = await postApi(SET_NEW_PASSWORD, reqData);
            Alertify.success('Password sets successfully');
            return { response, token: reqData.token };
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*---------------- Forget Password ---------------- */

export const ForgetPassword: any = createAsyncThunk(
    'forget-password',
    async (reqData: any, { getState }) => {
        try {
            const response = await postApi(
                FORGET_PASSWORD_REQUEST_OTP,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*---------------- Verify otp  ---------------- */

export const VerifyOtp: any = createAsyncThunk(
    'verify-otp',
    async (reqData: any, { getState }) => {
        try {
            const response = await postApi(VERIFY_OTP, reqData);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*---------------- Reset password  ---------------- */

export const ResetPassword: any = createAsyncThunk(
    'reset-password',
    async (reqData: any, { getState }) => {
        try {
            const response = await postApi(FORGET_RESET_PASSWORD, reqData);
            Alertify.success('Password reset successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*---------------- Change password  ---------------- */

export const ChangeProfilePassword: any = createAsyncThunk(
    'ChangePassword',
    async (reqData: any, { getState }) => {
        try {
            const response = await postApi(CHANGE_PASSWORD, reqData);
            Alertify.success('Password changed successfully');
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

// Validate Pin
export const ValidatePin = createAsyncThunk(
    'auth/ValidatePin',
    async ({ pin }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(VALIDATE_PIN, {
                pin,
                organizationId,
            });
            return response;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const ValidateModulePin = createAsyncThunk(
    'auth/ValidateModulePin',
    async ({ pin }: any, { rejectWithValue }) => {
        try {
            const response = await postApi(VALIDATE_MODULE_PIN, { pin });
            return response;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

// Logout
export const LogoutUser = createAsyncThunk(
    'auth/LogoutUser',
    async (_, { rejectWithValue }) => {
        try {
            const res = await postApi(LOGOUT_USER);
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/* -------------------------- Organization List -------------------------- */

export const organizationList = createAsyncThunk(
    'auth/organizationList',
    async ({ payload }: any, { dispatch, getState, rejectWithValue }) => {
        try {
            const res = await postApi(SELECT_ORGANIZATION_LIST, { ...payload });
            return res;
        } catch (error) {
            return handleApiError(error, rejectWithValue);
        }
    }
);
