import { createAsyncThunk } from '@reduxjs/toolkit';
import {
    COURSE_CANCEL_SCHEDULING,
    COURSE_CHEKIN,
    COURSE_DELETE_ENROLLMENT,
    COURSE_DELETE_SCHEDULING,
    COURSE_DETAILS,
    COURSE_LISTING,
    COURSE_SCHEDULING_CUSTOMER_LIST,
    COURSE_SCHEDULING_DETAILS,
    COURSE_SCHEDULING_LIST,
    COURSES_CUSTOMER_LIST,
    COURSES_ENROLL_CUSTOMER,
    COURSES_STATUS_UPDATE,
    CREATE_COURSE_SCHEDULE,
    UPDATE_COURSES_SCHEDULE,
} from '~/constants/api-constants';
import Alertify from '~/services/alertify';
import { deleteApi, getApi, patchApi, postApi } from '~/services/api-services';

/*------------------------ Courses List ---------------------------- */

export const courseList: any = createAsyncThunk(
    'courses-list',
    async (payload: any) => {
        try {
            const response = await postApi(COURSE_LISTING, {
                ...payload,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses Update  ---------------------------- */

export const courseUpdate: any = createAsyncThunk(
    'courses-update',
    async (
        {
            courseId,
            isActive,
            page,
            pageSize,
            description,
            image,
            isFeatured,
        }: any,
        { dispatch, getState }: any
    ) => {
        try {
            const response = await patchApi(COURSES_STATUS_UPDATE, {
                courseId,
                isActive,
                description,
                image,
                isFeatured,
            });
            dispatch(courseList({ page, pageSize }));
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses Update  ---------------------------- */

export const coursesCustomerList: any = createAsyncThunk(
    'courses-customers-list',
    async ({ payload }: any, { getState }: any) => {
        try {
            const response = await postApi(COURSES_CUSTOMER_LIST, payload);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses Scheduling List  ---------------------------- */

export const coursesSchedulingList: any = createAsyncThunk(
    'courses-scheduling-list',
    async ({ payload }: any, { getState }: any) => {
        try {
            const response = await postApi(COURSE_SCHEDULING_LIST, payload);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses Scheduling Customer List  ---------------------------- */

export const coursesSchedulingCustomerList: any = createAsyncThunk(
    'courses-scheduling-customer-list',
    async ({ payload }: any, { getState }: any) => {
        try {
            const response = await postApi(
                COURSE_SCHEDULING_CUSTOMER_LIST,
                payload
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses Scheduling Delete  ---------------------------- */

export const courseDeleteScheduling: any = createAsyncThunk(
    'courses-scheduling-Delete',
    async ({ scheDulingId }: any, { getState }: any) => {
        try {
            const response = await deleteApi(
                `${COURSE_DELETE_SCHEDULING}/${scheDulingId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses Scheduling Cancel  ---------------------------- */

export const courseCancelScheduling: any = createAsyncThunk(
    'courses-scheduling-cancel',
    async ({ scheDulingId }: any, { getState }: any) => {
        try {
            const response = await patchApi(
                `${COURSE_CANCEL_SCHEDULING}/${scheDulingId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses Scheduling Cancel  ---------------------------- */

export const courseEnrollClient: any = createAsyncThunk(
    'courses-scheduling-enroll-clients',
    async ({ payload }: any, { getState }: any) => {
        try {
            const response = await postApi(
                `${COURSES_ENROLL_CUSTOMER}`,
                payload
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            // Alertify.error(error.message[0]);
            // return Promise.reject(error);
            return error;
        }
    }
);

/*------------------------ Courses EnrollMent Delete  ---------------------------- */

export const courseDeleteEnrollMent: any = createAsyncThunk(
    'courses-enrollment-Delete',
    async ({ enrollmentId }: any, { getState }: any) => {
        try {
            const response = await deleteApi(
                `${COURSE_DELETE_ENROLLMENT}/${enrollmentId}`
            );
            Alertify.success(`Client's enrollment deleted`);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Courses EnrollMent Delete  ---------------------------- */

export const courseEnrollMentCheckIn: any = createAsyncThunk(
    'courses-enrollment-check-in',
    async ({ payload }: any, { getState }: any) => {
        try {
            const response = await postApi(`${COURSE_CHEKIN}`, {
                ...payload,
            });
            Alertify.success(
                `Client ${
                    payload?.isCheckedIn ? 'checked-In' : 'mark unarrived'
                } `
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Create Courses Schedulin  ---------------------------- */

export const createCoursesScheduling: any = createAsyncThunk(
    'create-courses-scheduling',
    async ({ payload }: any, { getState }: any) => {
        try {
            const response = await postApi(CREATE_COURSE_SCHEDULE, payload);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Get Courses Scheduling Details  ---------------------------- */

export const getCoursesSchedulingDetails: any = createAsyncThunk(
    'get-courses-scheduling-details',
    async ({ schedulingId }: any, { getState }: any) => {
        try {
            console.log(
                'schedulingId----------',
                schedulingId,
                `${COURSE_SCHEDULING_DETAILS}/${schedulingId}`
            );
            const response = await getApi(
                `${COURSE_SCHEDULING_DETAILS}/${schedulingId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Update Courses Schedulin  ---------------------------- */

export const updateCoursesScheduling: any = createAsyncThunk(
    'update-courses-scheduling',
    async ({ payload }: any, { getState }: any) => {
        try {
            const response = await patchApi(UPDATE_COURSES_SCHEDULE, payload);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------------ Get Courses Details  ---------------------------- */

export const getCoursesDetails: any = createAsyncThunk(
    'get-courses-details',
    async ({ courseId }: any, { getState }: any) => {
        try {
            const response = await getApi(`${COURSE_DETAILS}/${courseId}`);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
