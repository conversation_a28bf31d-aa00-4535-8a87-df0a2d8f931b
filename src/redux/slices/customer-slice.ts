import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    clientBookingList,
    ClientsDetails,
    CustomerList,
    multipleSharePassTransfer,
    SharedPassList,
} from '../actions/customer-action';

interface AuthState {
    customerList: [];
    customerListCount: number;
    sharedPassListCount: number;
    customerDetails: [] | any;
    customerListForForms: [];
    sharedPassList: [];
    customerPosDetails: any;
    customerSchedulingList: any;
    customerSchedulingListCount: number;
    multipleSharePassTransfer: any;
}

const initialState: AuthState = {
    customerList: [],
    customerListForForms: [],
    sharedPassList: [],
    customerListCount: 0,
    sharedPassListCount: 0,
    customerDetails: [],
    customerPosDetails: {},
    customerSchedulingList: [],
    customerSchedulingListCount: 0,
    multipleSharePassTransfer: [],
};

const customerSlice = createSlice({
    name: 'customer_store',
    initialState,
    reducers: {
        setCustomerPosDetails: (state, { payload }) => {
            state.customerPosDetails = payload;
        },
        clearCustomerPosDetails: (state) => {
            state.customerPosDetails = {};
        },
    },
    extraReducers: (builder) => {
        builder.addCase(CustomerList.fulfilled, (state, { payload, meta }) => {
            // console.log('Payload----------', payload,meta);
            if (meta?.arg?.isActive) {
                state.customerListForForms = payload?.data?.data?.list;
            } else {
                state.customerList = payload?.data?.data?.list;
            }
            state.customerListCount = payload?.data?.data?.count;
        });
        builder.addCase(
            ClientsDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                console.log('Payload---- client details------', payload);
                state.customerDetails = payload?.data?.data;
            }
        );
        builder.addCase(
            SharedPassList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                console.log('Payload---- Shared Pass List------', payload);
                state.sharedPassList = payload?.data?.data?.list;
                state.sharedPassListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            clientBookingList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.customerSchedulingList = payload?.data?.data;
                state.customerSchedulingListCount = payload?.data?.totalCount;
            }
        );
        builder.addCase(
            multipleSharePassTransfer.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                console.log(
                    'Payload---- multipleSharePassTransfer ist------',
                    payload
                );
                state.multipleSharePassTransfer = payload?.data?.data;
            }
        );
    },
});

export const { setCustomerPosDetails, clearCustomerPosDetails } =
    customerSlice.actions;

export default customerSlice.reducer;
