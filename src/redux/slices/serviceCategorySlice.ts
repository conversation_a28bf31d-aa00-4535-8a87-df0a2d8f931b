import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    AllServiceCategoryList,
    AppointmentTypeDetail,
    ServiceCategoryDetail,
    ServiceCategoryList,
    ServiceCategoryListByPackageId,
    ServiceCategoryListByStaffId,
    activeServiceCategoryList,
    UpdateAppointmentTypeStatus,
    DeleteAppointmentType,
    activeServiceCategoyListv1,
    activeServiceCategoyListPricing,
} from '../actions/serviceCategoryAction';

interface ServiceCategory {
    _id: string;
    appointmentType?: [];
    noOfSessions?: number;
}

interface ServiceCategoryState {
    ServiceCategoryListData: ServiceCategory[];
    AllServiceCategoryListData: ServiceCategory[];
    ServiceCategoryListByStaffId: ServiceCategory[];
    ServiceCategoryListByPackageId: ServiceCategory[];
    ServiceCategoryListCount: number;
    AllServiceCategoryListCount: number;
    ServiceCategoryDetailData: any;
    AppointmentTypeDetailData: any;
    ActiveServiceCategoryList: any;
    ActiveServiceCategoryListV1: [];
    ActiveServiceCategoryListPricing: any;
}

const initialState: ServiceCategoryState = {
    ServiceCategoryListData: [],
    ServiceCategoryListByStaffId: [],
    ServiceCategoryListByPackageId: [],
    ServiceCategoryListCount: 0,
    ServiceCategoryDetailData: [],
    AppointmentTypeDetailData: [],
    ActiveServiceCategoryList: [],
    AllServiceCategoryListData: [],
    AllServiceCategoryListCount: 0,
    ActiveServiceCategoryListV1: [],
    ActiveServiceCategoryListPricing: [],
};

const serviceCategorySlice = createSlice({
    name: 'serviceCategoryStore',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(
            ServiceCategoryList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.ServiceCategoryListData = payload?.data?.data?.list;
                state.ServiceCategoryListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            AllServiceCategoryList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.AllServiceCategoryListData = payload?.data?.data?.list;
                state.AllServiceCategoryListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            ServiceCategoryListByPackageId.fulfilled,
            (state, { payload }) => {
                state.ServiceCategoryListByPackageId = payload?.data?.data;
            }
        );
        builder.addCase(
            ServiceCategoryListByStaffId.fulfilled,
            (state, { payload }) => {
                state.ServiceCategoryListByStaffId = payload?.data?.data;
            }
        );
        builder.addCase(
            ServiceCategoryDetail.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.ServiceCategoryDetailData = payload?.data?.data;
            }
        );
        builder.addCase(
            AppointmentTypeDetail.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.AppointmentTypeDetailData = payload?.data?.data;
            }
        );
        builder.addCase(
            UpdateAppointmentTypeStatus.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.AllServiceCategoryListData =
                    state.AllServiceCategoryListData.map((category: any) => {
                        if (
                            category._id ===
                            payload.data.data?.updatedService?._id
                        )
                            category = payload.data.data.updatedService;
                        return category;
                    });
            }
        );
        builder.addCase(
            DeleteAppointmentType.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.AllServiceCategoryListData =
                    state.AllServiceCategoryListData.map((category: any) => {
                        if (category._id === payload.data.data?._id)
                            category = payload.data.data;
                        return category;
                    });
            }
        );
        builder.addCase(
            activeServiceCategoryList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.ActiveServiceCategoryList = payload?.data?.data?.list;
            }
        );
        builder.addCase(
            activeServiceCategoyListv1.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.ActiveServiceCategoryListV1 = payload?.data?.data?.list;
            }
        );
        builder.addCase(
            activeServiceCategoyListPricing.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.ActiveServiceCategoryListPricing =
                    payload?.data?.data?.list;
            }
        );
    },
});

export default serviceCategorySlice.reducer;
