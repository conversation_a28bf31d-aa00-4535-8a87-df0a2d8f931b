import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    BundlePricingList,
    BundlePricingListByActiveStatus,
    PriceListByServiceCategory,
    PriceListBySubType,
    PricingDetails,
    PricingList,
    PricingListByActiveStatus,
    ActivePurchasePricingPackagesByClient,
    InActivePurchasePricingPackagesByClient,
    CustomPricingListAPI,
    CustomPricingDetails,
} from '../actions/pricing-actions';

interface PricingState {
    pricingList: any;
    pricingListCount: number;
    pricingDetailData: any;
    pricingListByServiceCategory: [];
    pricingListBySubType: [];
    pricingListByActiveStatus: [];
    pricingListByActiveStatusCount: number;
    pricingListBySubTypeCount: number;
    purchasepricingListByClient: [];
    purchasepricingListByClientCount: number;
    inActivePurchasepricingListByClient: [];
    inActivePurchasepricingListByClientCount: number;
    bundlePricingList: any;
    bundlePricingListCount: number;
    bundlePricingListByActiveStatus: any;
    bundlePricingListByActiveStatusCount: number;
    customPricingList: any;
    customPricingDetails: any;
}
const initialState: PricingState = {
    pricingList: [],
    pricingListCount: 0,
    pricingDetailData: [],
    pricingListByServiceCategory: [],
    pricingListBySubType: [],
    pricingListBySubTypeCount: 0,
    pricingListByActiveStatus: [],
    pricingListByActiveStatusCount: 0,
    purchasepricingListByClient: [],
    purchasepricingListByClientCount: 0,
    inActivePurchasepricingListByClient: [],
    inActivePurchasepricingListByClientCount: 0,
    bundlePricingList: [],
    bundlePricingListCount: 0,
    bundlePricingListByActiveStatus: [],
    bundlePricingListByActiveStatusCount: 0,
    customPricingList: [],
    customPricingDetails: [],
};

const pricingSlice = createSlice({
    name: 'pricing_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder.addCase(
            PricingList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.pricingList = payload?.data?.data?.list;
                state.pricingListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            PricingDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.pricingDetailData = payload?.data?.data;
            }
        );
        builder.addCase(
            PriceListByServiceCategory.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.pricingListByServiceCategory = payload?.data?.data;
            }
        );
        builder.addCase(
            PriceListBySubType.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.pricingListBySubType = payload?.data?.data?.list;
                state.pricingListBySubTypeCount = payload?.data?.data?.total;
            }
        );
        builder.addCase(
            PricingListByActiveStatus.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.pricingListByActiveStatus = payload?.data?.data?.list;
                state.pricingListByActiveStatusCount =
                    payload?.data?.data?.count;
            }
        );
        builder.addCase(
            BundlePricingListByActiveStatus.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                const list = payload?.response?.data?.data?.list || [];
                const count = payload?.response?.data?.data?.count || 0;

                if (payload?.page === 1) {
                    state.bundlePricingListByActiveStatus = list;
                } else {
                    state.bundlePricingListByActiveStatus = [
                        ...state.bundlePricingListByActiveStatus,
                        ...list,
                    ];
                }

                state.bundlePricingListByActiveStatusCount = count;
            }
        );
        builder.addCase(
            ActivePurchasePricingPackagesByClient.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.purchasepricingListByClient = payload?.data?.data;
                state.purchasepricingListByClientCount =
                    payload?.data?.totalCount;
            }
        );
        builder.addCase(
            InActivePurchasePricingPackagesByClient.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.inActivePurchasepricingListByClient = payload?.data?.data;
                state.inActivePurchasepricingListByClientCount =
                    payload?.data?.totalCount;
            }
        );
        builder.addCase(
            BundlePricingList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                console.log('Payload---dfdfdfd----------------', payload);
                state.bundlePricingList = payload?.data?.data?.list;
                state.bundlePricingListCount = payload?.data?.data?.count;
            }
        );
        builder.addCase(
            CustomPricingListAPI.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                console.log(
                    'Payload---customPricingList----------------',
                    payload
                );
                state.customPricingList = payload?.data?.data;
            }
        );
        builder.addCase(
            CustomPricingDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                console.log(
                    'Payload---CustomPricingDetails----------------',
                    payload
                );
                state.customPricingDetails = payload?.data?.data;
            }
        );
    },
});

export default pricingSlice.reducer;
