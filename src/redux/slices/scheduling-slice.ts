import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { StaffAvailabilityList } from '~/redux/actions/appointment-action';
import {
    BookedSchedulingDetails,
    BookedSchedulingList,
    CreateBookScheduling,
    DeleteBookedScheduling,
    ManipulateBookAppointmentData,
    PurchasePricingPackagesList,
    UpdateBookedScheduling,
    CancelScheduling,
    CheckInScheduling,
    BookedCalendarData,
    ManipulateCalendarData,
    BookedSchedulingListV1,
    CreateAppointmentScheduling,
    UpdateAppointmentScheduling,
} from '~/redux/actions/scheduling-action';

interface AppointmentState {
    staffAvailabilityList: Array<any>;
    schedulingList: Array<any>;
    schedulingListv1: Array<any>;
    calendarSchedulingList: Array<any>;
    scheduleDetails: any;
    schedulingCount: number;
    schedulingCountV1: number;
    purchasePricingList: [];
    purchasePricingListCount: number;
}

const initialState: AppointmentState = {
    staffAvailabilityList: [],
    schedulingList: [],
    schedulingListv1: [],
    scheduleDetails: {},
    schedulingCount: 0,
    schedulingCountV1: 0,
    purchasePricingList: [],
    calendarSchedulingList: [],
    purchasePricingListCount: 0,
};

const appointmentSlice = createSlice({
    name: 'appointmentSlice',
    initialState: initialState,
    reducers: {
        ClearStaffFields: () => {
            return initialState;
        },
    },
    extraReducers(builder) {
        builder.addCase(
            StaffAvailabilityList.fulfilled,
            (state, { payload }) => {
                state.staffAvailabilityList = payload?.data?.data;
            }
        );
        builder.addCase(
            BookedSchedulingList.fulfilled,
            (state, { payload }) => {
                // console.log('Payload----------', payload);
                state.schedulingList = payload?.data?.data.map((item: any) =>
                    ManipulateBookAppointmentData(item)
                );
                state.schedulingCount = payload?.data?.totalCount;
            }
        );
        builder.addCase(
            BookedSchedulingListV1.fulfilled,
            (state, { payload }) => {
                state.schedulingListv1 = payload?.data?.data.map((item: any) =>
                    ManipulateBookAppointmentData(item)
                );
                state.schedulingCountV1 = payload?.data?.totalCount;
            }
        );
        builder.addCase(BookedCalendarData.fulfilled, (state, { payload }) => {
            // console.log('Payload----------', payload);
            state.calendarSchedulingList = payload?.data?.data.map(
                (item: any) => ManipulateCalendarData(item)
            );
        });
        builder.addCase(
            CreateBookScheduling.fulfilled,
            (state, { payload }) => {
                // Check if payload is valid and handle the case where payload might be null or undefined
                if (payload?.data?.data) {
                    const manipulatedData = ManipulateBookAppointmentData(
                        payload.data.data
                    );
                    state.schedulingList = [
                        ...state.schedulingList,
                        manipulatedData,
                    ];
                } else {
                    console.error(
                        'Invalid payload received in CreateBookSession.'
                    );
                }
            }
        );
        builder.addCase(
            CreateAppointmentScheduling.fulfilled,
            (state, { payload }) => {
                // Check if payload is valid and handle the case where payload might be null or undefined
                if (payload?.data?.data) {
                    const manipulatedData = ManipulateBookAppointmentData(
                        payload.data.data
                    );
                    state.schedulingList = [
                        ...state.schedulingList,
                        manipulatedData,
                    ];
                } else {
                    console.error(
                        'Invalid payload received in CreateBookSession.'
                    );
                }
            }
        );

        builder.addCase(
            BookedSchedulingDetails.fulfilled,
            (state, { payload }) => {
                // Ensure payload is valid and handle the data safely
                if (payload?.data?.data) {
                    const manipulatedData = payload.data.data;

                    state.scheduleDetails = manipulatedData;
                } else {
                    console.error(
                        'Invalid payload received in BookedSessionDetails.'
                    );
                }
            }
        );

        builder.addCase(DeleteBookedScheduling.fulfilled, (state, { meta }) => {
            // Safely check if ID exists in payload before performing filtering
            if (meta?.arg?.id) {
                state.schedulingList = state.schedulingList.filter(
                    (item) => item.id !== meta.arg.id
                );
            } else {
                console.error(
                    'Invalid payload received in DeleteBookedSession.'
                );
            }
        });

        builder.addCase(
            UpdateAppointmentScheduling.fulfilled,
            (state, { payload, meta }) => {
                // Ensure the payload is valid before performing the update
                if (meta?.arg?.scheduleId && payload?.data?.data) {
                    const manipulatedData = ManipulateBookAppointmentData(
                        payload.data.data
                    );
                    state.schedulingList = state.schedulingList.map((item) =>
                        item._id === meta?.arg?.scheduleId
                            ? manipulatedData
                            : item
                    );
                    state.scheduleDetails = {};
                } else {
                    console.error(
                        'Invalid payload received in UpdateBookedSession.'
                    );
                }
            }
        );
        builder.addCase(
            UpdateBookedScheduling.fulfilled,
            (state, { payload, meta }) => {
                // Ensure the payload is valid before performing the update
                if (meta?.arg?.scheduleId && payload?.data?.data) {
                    const manipulatedData = ManipulateBookAppointmentData(
                        payload.data.data
                    );
                    state.schedulingList = state.schedulingList.map((item) =>
                        item._id === meta?.arg?.scheduleId
                            ? manipulatedData
                            : item
                    );
                    state.scheduleDetails = {};
                } else {
                    console.error(
                        'Invalid payload received in UpdateBookedSession.'
                    );
                }
            }
        );
        builder.addCase(
            PurchasePricingPackagesList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.purchasePricingList = payload?.data?.data;
                state.purchasePricingListCount = payload?.data?.total;
            }
        );

        builder.addCase(
            CheckInScheduling.fulfilled,
            (state, { payload, meta }) => {
                // Ensure the payload is valid before performing the update
                if (meta?.arg?.scheduleId && payload?.data?.data) {
                    state.schedulingList = state.schedulingList.map((item) => {
                        if (item._id === meta?.arg?.scheduleId)
                            item.bookingStatus = 'checked-in';
                        return item;
                    });
                } else {
                    console.error(
                        'Invalid payload received in CheckInScheduling.'
                    );
                }
            }
        );

        builder.addCase(
            CancelScheduling.fulfilled,
            (state, { payload, meta }) => {
                // Ensure the payload is valid before performing the update
                if (meta?.arg?.scheduleId && payload?.data?.data) {
                    state.schedulingList = state.schedulingList.map((item) => {
                        if (item._id === meta.arg.scheduleId)
                            item.bookingStatus = 'canceled';
                        return item;
                    });
                } else {
                    console.error(
                        'Invalid payload received in CancelScheduling.'
                    );
                }
            }
        );
    },
});

export const { ClearStaffFields } = appointmentSlice.actions;

export default appointmentSlice.reducer;
