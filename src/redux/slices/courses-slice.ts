import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    courseList,
    coursesCustomerList,
    coursesSchedulingCustomerList,
    coursesSchedulingList,
    getCoursesDetails,
    getCoursesSchedulingDetails,
} from '../actions/courses-action';

interface CoursesState {
    coursesList: [];
    coursesListCount: number;
    coursesDetails: [];
    coursesCustomerList: [];
    coursesCustomerListCount: number;
    coursesSchedulingListCount: number;
    coursesSchedulingList: [];
    courseSchedulingCustomerList: [];
    courseSchedulingCustomerListCount: number;
    courseSchedulingDetails: any;
    courseDetails: any;
}

const initialState: CoursesState = {
    coursesList: [],
    coursesListCount: 0,
    coursesDetails: [],
    coursesCustomerList: [],
    coursesCustomerListCount: 0,
    coursesSchedulingList: [],
    coursesSchedulingListCount: 0,
    courseSchedulingCustomerList: [],
    courseSchedulingCustomerListCount: 0,
    courseSchedulingDetails: {},
    courseDetails: {},
};

const coursesSlice = createSlice({
    name: 'course_store',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(courseList.fulfilled, (state, { payload }) => {
                state.coursesList = payload?.data?.list;
                state.coursesListCount = payload?.data?.count;
            })
            .addCase(coursesCustomerList.fulfilled, (state, { payload }) => {
                state.coursesCustomerList = payload?.data?.list;
                state.coursesCustomerListCount = payload?.data?.count;
            })
            .addCase(coursesSchedulingList.fulfilled, (state, { payload }) => {
                state.coursesSchedulingList = payload?.data?.list;
                state.coursesSchedulingListCount = payload?.data?.count;
            })
            .addCase(
                coursesSchedulingCustomerList.fulfilled,
                (state, { payload }) => {
                    state.courseSchedulingCustomerList = payload?.data?.list;
                    state.courseSchedulingCustomerListCount =
                        payload?.data?.count;
                }
            )
            .addCase(
                getCoursesSchedulingDetails.fulfilled,
                (state, { payload }) => {
                    state.courseSchedulingDetails = payload?.data?.data;
                }
            )
            .addCase(getCoursesDetails.fulfilled, (state, { payload }) => {
                console.log('payload ----------fbfvbfbf----------', payload);
                state.courseDetails = payload?.data;
            });
    },
});

// export const { logout } = coursesSlice.actions;

export default coursesSlice.reducer;
