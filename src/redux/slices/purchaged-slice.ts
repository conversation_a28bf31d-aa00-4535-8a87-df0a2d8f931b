import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
    GetReconsiliation,
    OrderInvoiceDetails,
    OrderInvoiceList,
    markOrderAsPaid,
} from '../actions/purchased-action';

interface PurchasedState {
    orderInvoiceList: [];
    orderInvoiceCount: number;
    orderInvoiceDetail: any;
    posPuchasedData: any;
    selectedIds: [];
    selectedData: [];
    calculation: {
        discount: number;
        gst: number;
        subTotal: number;
        total: number;
        promoCode: string;
    };
    markedOrderAsPaid: any;
    reconciliationData: any;
}

const initialState: PurchasedState = {
    orderInvoiceList: [],
    orderInvoiceCount: 0,
    orderInvoiceDetail: {},
    posPuchasedData: {},
    selectedIds: [],
    selectedData: [],
    calculation: {
        discount: 0,
        gst: 0,
        subTotal: 0,
        total: 0,
        promoCode: '',
    },
    markedOrderAsPaid: {},
    reconciliationData: {},
};

const purchasedSlice = createSlice({
    name: 'purchase_store',
    initialState,
    reducers: {
        SetPosPurchasedData: (state, { payload }) => {
            state.posPuchasedData = payload;
        },
        ClearPosPurchasedData: (state) => {
            state.posPuchasedData = {};
        },
        UpdatePosPurchasedData: (state, { payload }) => {
            state.posPuchasedData = { ...state.posPuchasedData, ...payload };
        },
    },
    extraReducers: (builder) => {
        builder.addCase(
            OrderInvoiceList.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.orderInvoiceList = payload?.data?.data;
                state.orderInvoiceCount = payload?.data?.totalCount;
            }
        );
        builder.addCase(
            OrderInvoiceDetails.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.orderInvoiceDetail = payload?.data?.data;
            }
        );
        builder.addCase(
            markOrderAsPaid.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                state.markedOrderAsPaid = payload?.data?.data;
            }
        );
        builder.addCase(
            GetReconsiliation.fulfilled,
            (state, { payload }: PayloadAction<any>) => {
                console.log('Payload in update-------------', payload);
                state.reconciliationData = payload?.data?.data;
            }
        );
    },
});

export const {
    SetPosPurchasedData,
    ClearPosPurchasedData,
    UpdatePosPurchasedData,
} = purchasedSlice.actions;

export default purchasedSlice.reducer;
