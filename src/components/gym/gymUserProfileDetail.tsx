import ImageUpload from '../common/image-upload-comp';
import Title from 'antd/es/typography/Title';
import { ConfigProvider, Tabs, TabsProps } from 'antd';

import General from './general';
import BasicAssesment from './basicAssesment';
import Notes from './notes';
import Policies from './policies';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { ClientsDetails, UpdateClient } from '~/redux/actions/customer-action';
import { useLocation } from 'wouter';
import { formatDateString } from '../common/function';
import { UploadImage } from '~/redux/actions/common-action';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '../library/loader/full-loader';
import {
    summarizeNestedObject,
    GetSettings,
} from '~/redux/actions/settings-actions';
import ActivePackages from './active-packages';
import { getQueryParams } from '~/utils/getQueryParams';
import SharePass from './share-pass';
import WavierForm from '~/screens/customers/wavier/wavierForm';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import Alertify from '~/services/alertify';
import ClientBookingTable from './client-booking';
function goBack() {
    window.history.back();
}
const data = [
    {
        label: 'ID :',
        value: 'P-9GFD',
    },
    {
        label: 'DOB :',
        value: 'Staff',
    },
    {
        label: 'Gender :',
        value: 'Male',
    },
    // {
    //     label: 'Weight :',
    //     value: 'Knox Studio Gurugram',
    // },
    // {
    //     label: 'Goal :',
    //     value: '12-08-2024',
    // },
    // {
    //     label: 'Activity Level :',
    //     value: 'S-12346',
    // },
    // {
    //     label: 'Height:',
    //     value: '12-08-2024',
    // },
    // {
    //     label: 'User Type :',
    //     value: 'S-12346',
    // },
];

const GymUserProfileDetail = () => {
    const [location, setLocation] = useLocation();

    const clientId = location.split('/').pop();
    const params = getQueryParams();
    const userId = params.userId;
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        clientDetails: state.customer_store.customerDetails,
        clientOnboarding: state.settings_store.clientOnboarding,
    }));

    console.log('store-----------------', store.clientDetails);

    const ShowTabvalue = summarizeNestedObject(store.clientOnboarding);

    // console.log("valuealue-----------------", ShowTabvalue);

    const onChange = (key: string) => {
        console.log(key);
    };
    const [sharePassOption, setSharePassOption] = useState(false);
    const [sharePassEnabled, setSharePassEnabled] = useState(false);
    useEffect(() => {
        startLoader();
        if (clientId) {
            dispatch(ClientsDetails({ clientId: clientId }))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_scheduling_sharepass',
            })
        )
            .unwrap()
            .then((response: any) => {
                // console.log(
                //     'The status response is::::::',
                //     response?.data?.data?.isActive
                // );
                setSharePassOption(response?.data?.data?.isActive);
                setSharePassEnabled(response?.data?.data?.isEnabled);
            })
            .catch((error: any) =>
                Alertify.error('Error in fetching setting status')
            );
    }, [clientId]);

    useEffect(() => {
        dispatch(GetSettings({})).unwrap();
    }, []);

    const items: TabsProps['items'] = [
        {
            key: '1',

            label: (
                <div className="flex gap-5">
                    <div className="px-8 font-semibold ">General</div>
                </div>
            ),
            children: <General clientId={clientId} />,
        },
        {
            key: '2',

            label: (
                <div className="flex gap-5">
                    <div className="px-8 font-semibold "> Packages List</div>
                </div>
            ),
            children: <ActivePackages clientId={userId} />,
        },
        // ...(ShowTabvalue.assessment
        //     ? [
        //           {
        //               key: '3',
        //               label: (
        //                   <div className="flex gap-5">
        //                       <div className="px-8 font-semibold">
        //                           Basic Assesment
        //                       </div>
        //                   </div>
        //               ),
        //               children: (
        //                   <BasicAssesment
        //                       clientDetails={store.clientDetails}
        //                       facilityId={store.clientDetails?.facilityId}
        //                       clientId={clientId}
        //                       settingData={store.clientOnboarding}
        //                   />
        //               ),
        //           },
        //       ]
        //     : []),
        // ...(ShowTabvalue?.notes
        //     ? [
        //           {
        //               key: '4',
        //               label: (
        //                   <div className="flex gap-5">
        //                       <div className="px-8 font-semibold">Notes</div>
        //                   </div>
        //               ),
        //               children: <Notes />,
        //           },
        //       ]
        //     : []),
        ...(ShowTabvalue?.policies
            ? [
                  {
                      key: '5',
                      label: (
                          <div className="flex gap-5">
                              <div className="px-8 font-semibold">Policies</div>
                          </div>
                      ),
                      children: (
                          <Policies
                              clientDetails={store.clientDetails}
                              clientId={clientId}
                              facilityId={store.clientDetails?.facilityId}
                              settingData={store.clientOnboarding}
                          />
                      ),
                  },
              ]
            : []),

        ...(sharePassEnabled && ShowTabvalue?.sharePass
            ? [
                  {
                      key: '6',
                      label: (
                          <div className="flex gap-5">
                              <div className="px-8 font-semibold">
                                  Share Pass
                              </div>
                          </div>
                      ),
                      children: <SharePass />,
                  },
              ]
            : []),
        {
            key: '7',
            label: (
                <div className="flex gap-5">
                    <div className="px-8 font-semibold">Waiver</div>
                </div>
            ),
            children: <WavierForm sourceId={store.clientDetails.sourceId} />,
        },
        {
            key: '8',
            label: (
                <div className="flex gap-5">
                    <div className="px-8 font-semibold">Bookings</div>
                </div>
            ),
            children: <ClientBookingTable />,
        },
    ];

    const updatedData = data.map((item) => {
        switch (item.label.trim()) {
            case 'ID :':
                return { ...item, value: store.clientDetails?.clientId };
            case 'DOB :':
                return {
                    ...item,
                    value: formatDateString(store.clientDetails?.dob),
                };
            case 'Gender :':
                return {
                    ...item,
                    value: store.clientDetails?.gender
                        ? store.clientDetails.gender.charAt(0).toUpperCase() +
                          store.clientDetails.gender.slice(1).toLowerCase()
                        : '',
                };

            case 'Weight :':
                return { ...item, value: store.clientDetails?.weight };
            case 'Goal :':
                return { ...item, value: store.clientDetails?.goal };
            case 'Activity Level :':
                return { ...item, value: store.clientDetails?.activityLevel };
            case 'Height:':
                return { ...item, value: store.clientDetails?.height };
            case 'User Type :':
                return { ...item, value: store.clientDetails?.userType };
            default:
                return item;
        }
    });

    console.log('tore.clientDetails,', store.clientDetails);

    const handleImageUpload = (file: File) => {
        console.log('Uploaded file:', file);
        dispatch(UploadImage({ file })).then((res) => {
            console.log('Res--------------', res);
            if (
                res?.payload?.res?.status === 200 ||
                res?.payload?.res?.status === 201
            ) {
                const policiesWithoutId = store.clientDetails?.policies.map(
                    (policy: any) => {
                        return {
                            policyType: policy.policyType,
                            isEnabled: policy.isEnabled,
                        };
                    }
                );
                const payload = {
                    firstName: store.clientDetails?.firstName,
                    lastName: store.clientDetails?.lastName,
                    dob: store.clientDetails?.dob,
                    gender: store.clientDetails?.gender,
                    activityLevel: store.clientDetails?.activityLevel,
                    mobile: store.clientDetails?.mobile,
                    email: store.clientDetails?.email,
                    countryCode: store.clientDetails?.countryCode,
                    address: {
                        addressLine1:
                            store.clientDetails?.address?.addressLine1,
                        addressLine2:
                            store.clientDetails?.address?.addressLine2,
                        postalCode: store.clientDetails?.address?.postalCode
                            ? Number(store.clientDetails?.address?.postalCode)
                            : undefined,
                        city: store.clientDetails?.address?.city,
                        state: store.clientDetails?.address?.state,
                        country: 'India',
                    },
                    emergencyContactPerson:
                        store.clientDetails?.emergencyContactPerson,
                    emergencyContactPhone:
                        store.clientDetails?.emergencyContactPhone,
                    policies: policiesWithoutId,
                    facilityId: store.clientDetails?.facilityId,
                    photo: res?.payload?.res?.data?.data,
                };

                // console.log('Data in payload-------------', payload);
                dispatch(
                    UpdateClient({ reqData: payload, clientId: clientId })
                );
            }
        });
    };

    return (
        <>
            {loader ? (
                <FullLoader state={true} />
            ) : (
                <>
                    <div className="flex items-center gap-4 ">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[10px] cursor-pointer"
                            onClick={goBack}
                        />
                        <Title className="text-[#1a3353]" level={4}>
                            Client Profile
                        </Title>
                    </div>
                    <div className="my-10 flex rounded-lg py-10   lg:w-[90%] @sm:w-full">
                        <div className="w-[15%] @sm:hidden">
                            <ImageUpload
                                imageUrl={store.clientDetails?.photo}
                                onUpload={handleImageUpload}
                            />
                        </div>
                        <div className="w-[85%] ps-14">
                            <Title className="text-primary @sm:ps-5 " level={4}>
                                {store.clientDetails?.firstName +
                                    ' ' +
                                    store.clientDetails?.lastName}
                            </Title>
                            <div className="w-full justify-start gap-x-10  lg:flex lg:flex-wrap">
                                {updatedData?.map((item, index) => (
                                    <div
                                        key={index}
                                        className={`0 mt-5 flex items-center gap-3 lg:w-[31%] @sm:ps-5   `}
                                    >
                                        <div
                                            className={` w-auto text-lg font-bold text-[#1A3353]`}
                                        >
                                            <p className=" text-2xl font-semibold">
                                                {item.label}
                                            </p>
                                        </div>
                                        <div className="overflow-wrap  break-word w-auto break-words text-xl text-[#72849A]">
                                            <p>{item.value}</p>
                                        </div>
                                    </div>
                                ))}
                                <div
                                    className={`mt-5 flex items-center gap-3  lg:w-[31%] @sm:ps-5   `}
                                >
                                    <div
                                        className={` w-auto text-lg font-bold text-[#1A3353]`}
                                    >
                                        <p className=" text-2xl font-semibold">
                                            Membership ID :
                                        </p>
                                    </div>
                                    <div className="overflow-wrap  break-word w-auto break-words text-xl text-[#72849A]">
                                        <p>
                                            {store.clientDetails?.membershipId}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* --------------tabs-------------- */}
                    <div className="lg:w-[90%]">
                        <ConfigProvider
                            theme={{
                                components: {},
                            }}
                        >
                            <Tabs
                                defaultActiveKey="1"
                                items={items}
                                onChange={onChange}
                                tabPosition="top"
                                tabBarStyle={
                                    {
                                        // backgroundColor: '#f5f5f5',
                                    }
                                }
                            />
                        </ConfigProvider>
                    </div>
                </>
            )}
        </>
    );
};

export default GymUserProfileDetail;
