import React from 'react';
import { Collapse, Space, Form, Input, Button, Select } from 'antd';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';
const { TextArea } = Input;

const text1 = (
    <>
        <TextArea
            showCount
            maxLength={100}
            placeholder="Enter a Note"
            style={{ height: 120, resize: 'none' }}
        />
    </>
);

const Notes = () => {
    const onChange = (key: string | string[]) => {
        console.log(key);
    };
    const customExpandIcon = (panelProps: any) =>
        panelProps.isActive ? <MinusOutlined /> : <PlusOutlined />;
    return (
        <div className="w-full  lg:py-20 lg:pr-10">
            <Space className="w-full " direction="vertical" size="large">
                <Collapse
                    className="custom-collapse client-profile-collapse w-full rounded-2xl"
                    bordered={false}
                    defaultActiveKey={['1', '2']}
                    onChange={onChange}
                    expandIcon={customExpandIcon}
                    items={[
                        {
                            key: '1',
                            label: (
                                <div className="w-fit border-b-2 border-primary font-semibold">
                                    Messages
                                </div>
                            ),
                            children: (
                                <div className="rounded-xl border bg-[#f5f5f5] px-8 py-5">
                                    <p className="py-5">{text1}</p>
                                </div>
                            ),
                        },
                    ]}
                    // className="custom-collapse"
                    expandIconPosition="right"
                />
            </Space>

            <div className="flex flex-row justify-end gap-5">
                {/* <Form.Item>
                    <div className="mt-10" style={{ display: 'flex' }}>
                        <Button
                            className="border-1 border-[#1A3353] px-20 py-7 text-2xl"
                            htmlType="submit"
                        >
                            Cancel
                        </Button>
                    </div>
                </Form.Item> */}
                <Form.Item>
                    <div
                        className="mt-10"
                        style={{ display: 'flex', gap: '10px' }}
                    >
                        <Button
                            className="bg-purpleLight px-20 py-7 text-2xl"
                            type="primary"
                            htmlType="submit"
                        >
                            Save
                        </Button>
                    </div>
                </Form.Item>
            </div>
        </div>
    );
};

export default Notes;
