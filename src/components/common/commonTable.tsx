import {
    <PERSON><PERSON>,
    Config<PERSON><PERSON><PERSON>,
    DatePicker,
    Form,
    Input,
    Select,
    Table,
    Tabs,
    Typography,
} from 'antd';
import { Link, useLocation } from 'wouter';
import React, { useMemo, useState } from 'react';
import { STAFF_ROLE_DROPDOWN } from '~/types/enums-value';
import { useAppSelector } from '~/hooks/redux-hooks';
import { navigate } from 'wouter/use-location';
import {
    ClassType,
    PERMISSIONS_ENUM,
    RoleType,
    SUBJECT_TYPE,
} from '~/types/enums';
import TabPane from 'antd/es/tabs/TabPane';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import { useSelector } from 'react-redux';
import { MessageOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Search } = Input;

interface ItemProps {
    label: string;
    value: string;
}

interface IFacility {
    _id: string;
    facilityName: string;
    [key: string]: any;
}

interface CommonTableProps {
    heading?: any | string;
    backButton?: boolean;
    headingContent?: string;
    bulkAction?: boolean;
    addNewLink?: string;
    addNewTitle?: string;
    discountManagementButton?: string;
    discountManagementButtonForInventory?: boolean;
    columns: any[];
    dataSource: any[];
    toggleDiv?: (record: any, rowIndex: any) => void;
    loading?: boolean;
    className?: string;
    addNewModal?: boolean;
    openModal?: any;
    onSearch?: (val: string) => void;
    checkPin?: boolean;
    module?: string;
    subModule?: string;
    search?: string;
    showStaffRole?: boolean;
    selectedRoles?: any;
    setSelectedRoles?: any;
    selectedCity?: any;
    setSelectedCity?: any;
    showSearch?: boolean;
    showStaffLocation?: boolean;
    cityDisabled?: any;
    onCitySearch?: any;
    cityOptions?: ItemProps[];
    onCityChange?: any;
    stateOptions?: ItemProps[];
    onStateChange?: any;
    organizationFilters?: boolean;
    classType?: boolean;
    classTypes?: any;
    setClassTypes?: any;
    bundlePricing?: boolean;
    pricingType?: boolean;
    selectedPricing?: string;
    setSelectedPricing?: any;
    showLeadButton?: boolean;
    onLeadButtonClick?: any;
    addchildButton?: boolean;
    onAddChildButtonClick?: any;
    addNew?: any;
    selectedClass?: any;
    DivWidth?: any;
    smsButton?: boolean;
    leadsDatePicker?: boolean;
}

const sharedProps = {
    mode: 'multiple',
    style: { width: '100%' },
    placeholder: 'Select...',
    maxTagCount: 'responsive',
};

const CommonTable: React.FC<CommonTableProps> = (props) => {
    const [, setLocation] = useLocation();
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);

    // console.log('DivWidth ==============', props.DivWidth);
    const handleChange = (value: any) => {
        if (value.includes('all')) {
            props.setSelectedCity(['all']);
        } else {
            props.setSelectedCity(value);
        }
    };

    const handleRoleChange = (value: any) => {
        if (value.includes('all')) {
            props.setSelectedRoles(['all']);
        } else {
            props.setSelectedRoles(value);
        }
    };
    const handleClassTypeChange = (value: any) => {
        props.setClassTypes(value);
    };

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        role: state.auth_store.role,
    }));

    const handleSearchChange = (value: string) => {
        props.onSearch?.(value);
        navigate(`?page=1&pageSize=10&search=${value}`, { replace: true });
    };

    const { role } = useSelector((state: any) => state.auth_store);

    const handleClick = (link: string) => {
        if (props.checkPin && role !== RoleType.ORGANIZATION)
            setPinModalVisible(true);
        else setLocation(link);
    };

    const handleRedirect = () => {
        if (props.addNewLink) setLocation(props.addNewLink);
        sessionStorage.setItem('accessViaSecureFlow', 'true');
        setPinModalVisible(false);
    };
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPricingWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PRICING_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Table: {
                        borderColor: '#0000001A',
                        cellFontSize: 13,
                        headerBg: '#fff',
                        headerColor: '#1A3353',
                        colorText: '#455560',
                    },
                },
            }}
        >
            <div>
                {/* Header Section */}
                {/* <div className="shadow-b-md  flex items-center justify-between  pb-4  @sm:flex-col @sm:gap-4 @sm:pb-7"> */}
                <div
                    className={`shadow-b-md  flex items-center justify-between @sm:flex-col @sm:gap-4 @sm:pb-7 ${
                        props.pricingType ? 'pb-0' : 'pb-4'
                    }`}
                >
                    {props.heading && (
                        <div className="items-center gap-4  @sm:w-full">
                            <div className="flex items-center gap-4">
                                {props.backButton && (
                                    <img
                                        src="/icons/back.svg"
                                        alt="edit"
                                        className="h-[10px] cursor-pointer"
                                        onClick={() => window.history.back()}
                                    />
                                )}

                                <Title className="text-[#1A3353] " level={4}>
                                    {props.heading}
                                </Title>
                            </div>
                            <div className="">
                                <Text className="text-[#455560]">
                                    {props.headingContent}
                                </Text>
                            </div>
                        </div>
                    )}
                    {/* Filters and Actions */}
                    {/* <div className="flex w-1/2 "> */}
                    {props.pricingType && (
                        <div className="flex flex-row items-center justify-between  pt-2">
                            <ConfigProvider
                                theme={{
                                    components: {
                                        Tabs: {
                                            margin: 0,
                                        },
                                    },
                                }}
                            >
                                <Tabs
                                    activeKey={props.selectedPricing}
                                    onChange={(key) =>
                                        props.setSelectedPricing(key)
                                    }
                                    className="w-full"
                                >
                                    <TabPane
                                        tab="Regular Pricing"
                                        key="regularPricing"
                                    />
                                    <TabPane
                                        tab="Bundle Pricing"
                                        key="bundlePricing"
                                    />
                                </Tabs>
                            </ConfigProvider>
                        </div>
                    )}

                    <div
                        className={`ms-auto flex flex-wrap-reverse items-center justify-end  ${
                            props.DivWidth ? props.DivWidth : 'w-[75%]'
                        } lg:gap-10 @sm:grid @sm:grid-cols-1 @sm:gap-4`}
                    >
                        {props.showSearch && (
                            <div className="lg:w-[30%] 2xl:w-[20%]">
                                <Search
                                    allowClear
                                    placeholder="Search"
                                    onChange={(e) =>
                                        handleSearchChange(e.target.value)
                                    }
                                    value={props.search}
                                />
                            </div>
                        )}
                        {props.leadsDatePicker && (
                            <div className="w-[20%] ">
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    placeholder="Select date"
                                    format="DD/MM/YYYY"
                                    style={{ width: '100%' }}
                                />
                            </div>
                        )}
                        {props.smsButton && (
                            <div className="flex justify-end">
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                    onClick={() => setLocation('/lead-sms')}
                                >
                                    <MessageOutlined />
                                    <p>SMS</p>
                                </Button>
                            </div>
                        )}
                        {props.organizationFilters && (
                            <>
                                <Select
                                    {...sharedProps}
                                    placeholder="Select state"
                                    className="w-[25%]"
                                    options={props.stateOptions}
                                    onChange={props.onStateChange}
                                />
                                <Select
                                    {...sharedProps}
                                    className="w-[25%]"
                                    placeholder="Select city"
                                    options={props.cityOptions}
                                    value={props.selectedCity}
                                    onChange={props.onCityChange}
                                    disabled={props.cityDisabled}
                                    onSearch={props.onCitySearch}
                                />
                            </>
                        )}
                        {props.showStaffLocation && (
                            <div className="lg:w-[25%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Location
                                </p>
                                <Select
                                    {...sharedProps}
                                    options={store.facilityList.map(
                                        (facility: IFacility) => ({
                                            label: facility?.facilityName,
                                            value: facility?._id,
                                        })
                                    )}
                                    value={props.selectedCity}
                                    onChange={handleChange}
                                />
                            </div>
                        )}
                        {props.showStaffRole && (
                            <div className="lg:w-[25%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Role
                                </p>
                                <Select
                                    {...sharedProps}
                                    options={STAFF_ROLE_DROPDOWN.map(
                                        (role) => ({
                                            label: role.label,
                                            value: role.value,
                                        })
                                    )}
                                    value={props.selectedRoles}
                                    onChange={handleRoleChange}
                                />
                            </div>
                        )}
                        {props.classType && (
                            <div className="lg:w-[25%]">
                                <p className="text-lg font-medium text-[#1A3353] lg:-mt-7">
                                    Type
                                </p>
                                {/* <Select
                                    {...sharedProps}
                                    options={Object.values(ClassType).map(
                                        (type) => ({
                                            label: type, // Use the string value directly as the label
                                            value: type, // Use the string value directly as the value
                                        })
                                    )}
                                    value={props.selectedClass}
                                    onChange={handleClassTypeChange}
                                /> */}
                                <Select
                                    {...sharedProps}
                                    options={STAFF_ROLE_DROPDOWN.map(
                                        (role) => ({
                                            label: role.label
                                                .toLowerCase()
                                                .split(' ')
                                                .map(
                                                    (word) =>
                                                        word
                                                            .charAt(0)
                                                            .toUpperCase() +
                                                        word.slice(1)
                                                )
                                                .join(' '),
                                            value: role.value,
                                        })
                                    )}
                                    value={props.selectedRoles}
                                    onChange={handleRoleChange}
                                />
                            </div>
                        )}

                        {props.discountManagementButton &&
                            props.selectedPricing === 'regularPricing' && (
                                <Button
                                    className="fw-500 flex items-center rounded-lg border px-8 py-3 text-xl"
                                    onClick={() =>
                                        setLocation('/discount-management')
                                    }
                                >
                                    <p>Discount Management</p>
                                </Button>
                            )}
                        {props.discountManagementButtonForInventory &&
                            props.selectedPricing === 'regularPricing' && (
                                <Button
                                    className="fw-500 flex items-center rounded-lg border px-8 py-3 text-xl"
                                    onClick={() =>
                                        setLocation(
                                            '/store-detail/discount-management'
                                        )
                                    }
                                >
                                    <p>Discount Management</p>
                                </Button>
                            )}

                        {props.addchildButton && (
                            <Button
                                className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                onClick={props.onAddChildButtonClick}
                            >
                                <p>Add Sub-Client</p>
                                <span className="-translate-y-1 text-3xl">
                                    +
                                </span>
                            </Button>
                        )}

                        {(hasPricingWritePermission ||
                            store.role === RoleType.ORGANIZATION) &&
                            props.bundlePricing &&
                            props.selectedPricing === 'bundlePricing' && (
                                <Link to="/create-bundle-pricing/0">
                                    {' '}
                                    <Button className="fw-500 flex items-center rounded-lg border bg-[#8143D1] px-8 py-3 text-xl text-white">
                                        <p>Create Bundle</p>
                                        <span className="-translate-y-1 text-3xl">
                                            +
                                        </span>
                                    </Button>
                                </Link>
                            )}

                        {props.addNewLink && (
                            <Link to={props.addNewLink}>
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-[#8143D1] px-8 py-3 text-xl text-white"
                                    onClick={props.addNew}
                                >
                                    <p>{props.addNewTitle}</p>
                                    <span className="-translate-y-1 text-3xl">
                                        +
                                    </span>
                                </Button>
                            </Link>
                        )}
                        {props.showLeadButton && (
                            <>
                                <Button
                                    className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                    onClick={() => props.onLeadButtonClick()}
                                >
                                    <p>Request(s)</p>
                                </Button>
                            </>
                        )}
                        {props.addNewModal && (
                            <Button
                                className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                                onClick={() => props.openModal(true)}
                            >
                                <p>{props.addNewTitle}</p>
                                <span className="-translate-y-1 text-3xl">
                                    +
                                </span>
                            </Button>
                        )}
                    </div>
                </div>

                {/* Table Section */}
                <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                    <ConfigProvider
                        theme={{
                            components: {
                                Table: {
                                    selectionColumnWidth: 50,
                                },
                            },
                        }}
                    >
                        <Table
                            className={`m-2 overflow-x-auto rounded-[6px] border-1 ${props.className}`}
                            pagination={false}
                            columns={props.columns}
                            dataSource={props.dataSource?.map(
                                (row: any, index: number) => {
                                    // Process the row to handle arrays
                                    const processedRow = Object.entries(
                                        row
                                    ).reduce((acc: any, [key, value]) => {
                                        acc[key] = Array.isArray(value)
                                            ? value.join(', ')
                                            : value; // Convert arrays to comma-separated strings
                                        return acc;
                                    }, {});

                                    return {
                                        ...processedRow,
                                        key: index,
                                    };
                                }
                            )}
                            loading={props.loading}
                        />
                    </ConfigProvider>
                </div>
            </div>

            {pinModalVisible && (
                <ModulePinConfirmationModal
                    visible={pinModalVisible}
                    onConfirm={handleRedirect}
                    onCancel={() => setPinModalVisible(false)}
                    module={props.module}
                    subModule={props.subModule}
                />
            )}
        </ConfigProvider>
    );
};

export default CommonTable;
