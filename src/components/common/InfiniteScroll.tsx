import React, { useState, useEffect, useCallback } from 'react';
import { Select, Spin, Tooltip } from 'antd';
import debounce from 'lodash.debounce';

interface InfiniteScrollSelectProps {
    fetchOptions: (searchText: string, page: number) => Promise<any[]>;
    value?: any;
    onChange: (value: string, option: any) => void;
    placeholder?: string;
    pageSize?: number;
    disabled?: boolean;
    className?: string;
    tooltipMessage?: string;
    mode?: 'multiple' | 'tags';
    extractOptions?: any;
    defaultOptions?: any[];
}

const InfiniteScrollSelect: React.FC<InfiniteScrollSelectProps> = ({
    fetchOptions,
    value,
    onChange,
    placeholder = 'Select an option',
    pageSize = 10,
    disabled,
    className,
    tooltipMessage = '',
    mode,
    extractOptions,
    defaultOptions = [],
}) => {
    const [options, setOptions] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [searchText, setSearchText] = useState('');
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    const loadOptions = async (
        search: string,
        pageNumber: number,
        reset = false
    ) => {
        if (loading) return;
        setLoading(true);
        try {
            console.log(pageNumber);
            const newOptions = await fetchOptions(search, pageNumber);
            if (!Array.isArray(newOptions)) {
                throw new Error('fetchOptions must return an array');
            }
            setOptions((prev) =>
                reset
                    ? [...defaultOptions, ...newOptions]
                    : [...defaultOptions, ...prev, ...newOptions]
            ); // Reset only when required
            extractOptions &&
                extractOptions((prev: any) =>
                    reset ? newOptions : [...prev, ...newOptions]
                );
            setHasMore(newOptions?.length >= pageSize);
        } catch (error) {
            console.error('Failed to load options:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = useCallback(
        debounce((query: string) => {
            setSearchText(query);
            setPage(1);
            loadOptions(query, 1, true);
        }, 500),
        []
    );

    const handlePopupScroll = async (event: any) => {
        const target = event.target as HTMLElement;

        // Corrected scroll condition
        if (
            target.scrollTop + target.clientHeight >=
                target.scrollHeight - 10 &&
            hasMore &&
            !loading
        ) {
            setLoading(true); // Set loading before fetching more

            setPage((prevPage) => {
                const nextPage = prevPage + 1;
                loadOptions(searchText, nextPage);
                return nextPage;
            });
        }
    };

    const handleDropdownVisibleChange = (open: boolean) => {
        setIsDropdownOpen(open);
        if (open) {
            setSearchText('');
            setPage(1);
            loadOptions('', 1, true);
        }
    };

    useEffect(() => {
        loadOptions('', 1, true);
    }, []);

    return (
        <Tooltip title={disabled ? tooltipMessage : ''} placement="top">
            <span className="w-full ">
                <Select
                    variant="borderless"
                    {...(mode ? { mode } : {})}
                    value={value}
                    onChange={onChange}
                    placeholder={placeholder}
                    showSearch
                    onSearch={handleSearch}
                    onPopupScroll={handlePopupScroll}
                    onDropdownVisibleChange={handleDropdownVisibleChange}
                    notFoundContent={
                        loading ? <Spin size="small" /> : 'No data'
                    }
                    options={options}
                    filterOption={false}
                    disabled={disabled}
                    className={className}
                    style={
                        disabled
                            ? {
                                  backgroundColor: '#f5f5f5',
                                  color: '#a0a0a0',
                                  cursor: 'not-allowed',
                              }
                            : {}
                    }
                />
            </span>
        </Tooltip>
    );
};

export default InfiniteScrollSelect;
