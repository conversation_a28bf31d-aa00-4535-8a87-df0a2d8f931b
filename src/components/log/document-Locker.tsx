import { UploadOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Table } from 'antd';

const columns = [
    {
        title: 'Indent No',
        dataIndex: 'number',
        render: (text: any) => <a>{text}</a>,
    },
    {
        title: 'Indent Date',
        dataIndex: 'date',
    },
    {
        title: 'Prescription Name',
        dataIndex: 'prescription',
    },
    {
        title: 'Status',
        dataIndex: 'status',
        render: (status: any) => {
            let color = '';
            if (status === 'Confirm')
                color = 'text-green-500 bg-green-100 border-green-500';
            else if (status === 'Pending')
                color = 'text-yellow-500 bg-yellow-100 border-yellow-500';
            else if (status === 'Cancel')
                color = 'text-red-500 bg-red-100 border-red-500';

            return (
                <span
                    className={`rounded-md border px-3 py-1 ${color} font-semibold`}
                >
                    {status}
                </span>
            );
        },
    },
];

const data = [
    {
        key: '1',
        number: 'IND001',
        date: '2024-03-10',
        prescription: 'Pain Reliever',
        status: 'Confirm',
    },
    {
        key: '2',
        number: 'IND002',
        date: '2024-03-12',
        prescription: 'Antibiotics',
        status: 'Pending',
    },
    {
        key: '3',
        number: 'IND003',
        date: '2024-03-15',
        prescription: 'Cough Syrup',
        status: 'Cancel',
    },
    {
        key: '4',
        number: 'IND004',
        date: '2024-03-18',
        prescription: 'Vitamin Supplements',
        status: 'Confirm',
    },
    {
        key: '5',
        number: 'IND005',
        date: '2024-03-20',
        prescription: 'Fever Medicine',
        status: 'Pending',
    },
];

const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
        console.log(
            'Selected Row Keys:',
            selectedRowKeys,
            'Selected Rows:',
            selectedRows
        );
    },
};

const DocumentLocker = () => {
    return (
        <>
            <div className="flex flex-row items-center justify-between border-b pb-5">
                <p className="text-[15px] font-semibold text-[#1a3353]">
                    Indent Request
                </p>
                <div>
                    <Button className="h-12 border border-purpleLight bg-purpleLight px-4 text-xl text-[#ffffff]">
                        <UploadOutlined /> Upload Document
                    </Button>
                </div>
            </div>
            <div className="py-14">
                <ConfigProvider>
                    {/* Corrected rowSelection prop */}
                    <Table
                        rowSelection={rowSelection}
                        columns={columns}
                        dataSource={data}
                    />
                </ConfigProvider>
            </div>
        </>
    );
};

export default DocumentLocker;
