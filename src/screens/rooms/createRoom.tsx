import React, { useEffect, useState } from 'react';
import {
    ConfigProvider,
    Typography,
    Form,
    Row,
    Col,
    Input,
    Select,
    Checkbox,
    InputNumber,
    Button,
    Upload,
} from 'antd';
import { useLoader } from '~/hooks/useLoader';
import { useParams, useLocation } from 'wouter';
import ReactQuill from 'react-quill';
import { PlusOutlined, LoadingOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import {
    createRoom,
    getRoomDetails,
    updateRoomDetails,
} from '~/redux/actions/room-action';
import { activeServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { useAppSelector } from '~/hooks/redux-hooks';
import { UploadImage } from '~/redux/actions/common-action';
import { classesType } from '../services-and-products/classes';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';

const { Title } = Typography;
interface RoomValues {
    classType: string[]; // Ensure it is defined as an array
    image?: string;
}

const CreateRoom: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [loader, startLoader, endLoader] = useLoader();
    const [loading, setLoading] = useState(false);
    const [location, setLocation] = useLocation();
    const { id } = useParams<{ id: string }>();
    const [imageUrl, setImageUrl] = useState<string>();
    const [classTypeValue, setClassTypeValue] = useState<any>([]);
    const [activeServiceList, setActiveServiceList] = useState([]);
    const [isCourseEnabled, setIsCourseEnabled] = useState(false);

    const fetchRoomData = async () => {
        startLoader();
        try {
            const roomDetails = await dispatch(
                getRoomDetails({ roomId: id })
            ).unwrap();
            const response = await dispatch(
                activeServiceCategoryList({
                    reqData: roomDetails.classType || [],
                })
            ).unwrap();

            const categories = response?.data?.data?.list || [];
            const activeList = categories.map((item: any) => ({
                value: item._id,
                label: item.name,
            }));
            setActiveServiceList(activeList);
            if (roomDetails.image) {
                setImageUrl(roomDetails.image);
            }
            setClassTypeValue(roomDetails.classType);
            form.setFieldsValue({
                roomName: roomDetails.roomName,
                facilityId: roomDetails.facilityId,
                classType: roomDetails.classType,
                capacity: roomDetails.capacity,
                description: roomDetails.description,
                serviceCategory:
                    roomDetails.serviceCategory?.map(
                        (category: any) => category._id
                    ) || [],
            });
        } catch (error) {
            console.error('Error fetching room data:', error);
        } finally {
            endLoader();
        }
    };
    useEffect(() => {
        dispatch(FacilitiesList({}));
        if (id && id !== '0') {
            fetchRoomData();
        }
    }, [id]);

    useEffect(() => {
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_setup_courses',
            })
        ).then((response: any) => {
            setIsCourseEnabled(response?.payload?.data?.data?.isEnabled);
        });
    }, []);

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
    }));

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));
    const handleImageUpload = (file: any) => {
        console.log('Uploaded file:', file);
        dispatch(UploadImage({ file: file.file })).then((res: any) => {
            console.log('Res------------------', res);
            setImageUrl(res?.payload?.res?.data?.data);
        });
    };
    const handleTypeChange = async (value: any) => {
        console.log('Value-----------', value);
        setLoading(true); // Optional loading state
        setClassTypeValue(value);
        try {
            const response = await dispatch(
                activeServiceCategoryList({ reqData: value })
            ).unwrap();
            const categories = response?.data?.data?.list || [];
            const activeList = categories.map((item: any) => ({
                value: item._id,
                label: item.name,
            }));
            setActiveServiceList(activeList); // Trigger re-render
            // form.setFieldsValue({ serviceCategory: [] }); // Reset selection
        } catch (error) {
            console.error('Error fetching service categories:', error);
        } finally {
            setLoading(false);
        }
    };

    const onFinish = async (values: any) => {
        startLoader();
        try {
            console.log(values, '---');
            const payload = {
                ...values,
            };
            if (imageUrl) {
                values['image'] = imageUrl;
            }
            if (id === '0') {
                await dispatch(createRoom(payload)).unwrap();
            } else {
                await dispatch(
                    updateRoomDetails({ reqData: payload, roomId: id })
                ).unwrap();
            }
            setLocation('/setting/room');
        } catch (error) {
            console.error('Error submitting form:', error);
        } finally {
            endLoader();
        }
    };

    const uploadButton = (
        <div>
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </div>
    );

    return (
        <ConfigProvider>
            <div className="flex justify-between">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="back"
                        className="h-[10px] cursor-pointer"
                        onClick={() => setLocation('/setting/room')}
                    />
                    <Title level={4} className="text-[#1A3353]">
                        {id === '0' ? 'Create Room' : 'Edit Room'}
                    </Title>
                </div>
            </div>
            <div className="flex flex-row gap-10  pt-10">
                <div className="w-[60%] rounded-2xl border border-[#d9d9d9] p-8">
                    <Form
                        form={form}
                        layout="vertical"
                        initialValues={{ serviceCategory: [], classType: [] }}
                        onFinish={onFinish}
                    >
                        <Form.Item
                            label="Room Name"
                            name="roomName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter a room name.',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Room name" />
                        </Form.Item>
                        <Form.Item
                            label="Location"
                            name="facilityId"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select a location.',
                                },
                            ]}
                        >
                            <Select
                                placeholder="Select facility"
                                filterOption={(input, option) =>
                                    String(option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={FacilityOptions}
                            />
                        </Form.Item>
                        <div className="mb-6 w-full">
                            <Form.Item
                                label="Types"
                                name="classType"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select a type.',
                                    },
                                ]}
                            >
                                <Checkbox.Group
                                    // value={classTypeValue}
                                    options={[
                                        { label: 'Classes', value: 'classes' },
                                        { label: 'Booking', value: 'bookings' },
                                        {
                                            label: 'Personal Appointment',
                                            value: 'personalAppointment',
                                        },
                                        ...(isCourseEnabled
                                            ? [
                                                  {
                                                      label: 'Courses',
                                                      value: 'courses',
                                                  },
                                              ]
                                            : []),
                                    ]}
                                    onChange={(selectedTypes) => {
                                        console.log(
                                            'Selected types:',
                                            selectedTypes
                                        ); // Logs the array of selected values
                                        handleTypeChange(selectedTypes); // Calls the custom change handler, if needed
                                    }}
                                />
                            </Form.Item>
                        </div>

                        <div className="mb-5 h-72 overflow-scroll rounded-2xl border p-6">
                            <Form.Item
                                label="Limit to these Services"
                                name="serviceCategory"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please select at least one service.',
                                        type: 'array',
                                    },
                                ]}
                            >
                                <Checkbox.Group
                                    options={activeServiceList}
                                    value={form.getFieldValue(
                                        'serviceCategory'
                                    )}
                                    onChange={(value) =>
                                        form.setFieldsValue({
                                            serviceCategory: value,
                                        })
                                    }
                                    className="flex flex-col  gap-3"
                                />
                            </Form.Item>
                        </div>
                        <Form.Item
                            label="Max Capacity"
                            name="capacity"
                            rules={[{ required: true }]}
                        >
                            <InputNumber
                                min={0}
                                placeholder="Enter Max Capacity"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                        <Form.Item label="Description" name="description">
                            <ReactQuill theme="snow" />
                        </Form.Item>

                        <Form.Item className="flex justify-end">
                            <Button
                                htmlType="submit"
                                className="mt-6 bg-purpleLight text-white"
                            >
                                Save
                            </Button>
                        </Form.Item>
                    </Form>
                </div>
                <div className="w-[40%] rounded-2xl border border-[#d9d9d9] px-8 pb-8 pt-5">
                    <Typography.Title level={5}>
                        <span className="text-primary ">UPLOAD IMAGE</span>
                    </Typography.Title>
                    <Upload
                        listType="picture-card"
                        className="avatar-uploader overflow-hidden rounded-2xl lg:mt-8 @sm:mt-2"
                        showUploadList={false}
                        customRequest={handleImageUpload}
                    >
                        {imageUrl ? (
                            <img
                                src={imageUrl}
                                alt="avatar"
                                className="rounded-3xl object-center lg:object-cover @sm:object-cover"
                                style={{
                                    width: '100%',
                                    height: '100%',
                                }}
                            />
                        ) : (
                            uploadButton
                        )}
                    </Upload>
                </div>
            </div>
        </ConfigProvider>
    );
};

export default CreateRoom;
