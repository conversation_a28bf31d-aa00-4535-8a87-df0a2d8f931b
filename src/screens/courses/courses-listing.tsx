import {
    <PERSON><PERSON>,
    Config<PERSON><PERSON><PERSON>,
    DatePicker,
    Dropdown,
    message,
    Select,
    Space,
} from 'antd';
import Title from 'antd/es/typography/Title';
import React, { useEffect, useState } from 'react';
import { DownOutlined } from '@ant-design/icons';
import ScheduleClassesModal from '../services-and-products/schedule-classes-modal';
import ScheduledCourses from './schedule-courses';
import BookingModal from '../appointment/booking-modal';
import { useLocation } from 'wouter';

const { RangePicker } = DatePicker;

const tabs = [
    { key: 1, label: 'Today' },
    // { key: 2, label: 'Week' },
    // { key: 3, label: 'Month' },
];

const CoursesListing = () => {
    const [location, setLocation] = useLocation();
    const [activeKey, setActiveKey] = useState(1);
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [dateRange, setDateRange] = useState<{
        startDate: string | null;
        endDate: string | null;
    }>({ startDate: null, endDate: null });

    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleClose = () => {
        setIsModalVisible(false);
    };

    // Handle menu click inside the component
    // const handleMenuClick = ({ key }: { key: string }) => {
    //     if (key === '1') {
    //         showModal(); // Open modal when "Create or Schedule a Class" is clicked
    //     }
    // };

    const onChange = (dates: any, dateStrings: any) => {
        if (dates) {
            // When dates are selected, update the dateRange state
            setDateRange({
                startDate: dateStrings[0],
                endDate: dateStrings[1],
            });
        } else {
            // When the date picker is cleared, reset the dateRange to empty
            setDateRange({
                startDate: null,
                endDate: null,
            });
        }
    };

    // const handleChange = (value: any) => {
    //     console.log(`selected ${value}`);
    // };

    return (
        <div className="flex flex-col  ">
            <div className="flex flex-row justify-between lg:pe-24">
                <Title className=" text-[#1a3353]" level={4}>
                    Courses
                </Title>
                <>
                    {/* <Button
                    onClick={() => {
                        showModal();
                    }}
                    className="border border-[#1a3353]"
                >
                    Schedule
                </Button> */}
                    <Button
                        onClick={() => {
                            setLocation('/create-pricing/0?type=courses');
                        }}
                        className="fw-500 flex items-center rounded-lg border bg-purpleLight px-8 py-3 text-xl text-white"
                    >
                        Add Course
                    </Button>
                </>
            </div>

            <div className="mt-10 flex flex-row justify-end  rounded-t-2xl border-l border-r border-t ps-10 pt-10 lg:pe-24">
                <div className="flex flex-row gap-5  ">
                    {/* {tabs?.map((tab) => (
                        <div
                            key={tab.key}
                            className={`flex cursor-pointer flex-row items-center justify-center rounded-xl border border-[#1A3353]  lg:w-[100px] ${
                                activeKey === tab.key
                                    ? 'border-purpleLight bg-purpleLight text-white'
                                    : ''
                            }`}
                            onClick={() => setActiveKey(tab.key)}
                        >
                            <p className="text-center"> {tab.label} </p>
                        </div>
                    ))} */}
                    <ConfigProvider
                        theme={{
                            token: {
                                colorBorder: '#1a3353',
                            },
                        }}
                    >
                        <div>
                            <RangePicker
                                format="DD/MM/YYYY"
                                onChange={(dates, dateStrings) =>
                                    onChange(dates, dateStrings)
                                }
                                className="custom-datepicker"
                                allowClear
                            />
                        </div>
                    </ConfigProvider>
                </div>
            </div>

            {/* -------------tab content------------ */}
            <div className=" rounded-b-2xl border-b border-l border-r py-10 ps-10 lg:pe-20">
                {activeKey === 1 && (
                    <div>
                        <ScheduledCourses
                            type={'today'}
                            dateRange={dateRange}
                        />
                    </div>
                )}

                {/* {activeKey === 2 && (
                    <div>
                        <ScheduledCourses type={'week'} />
                    </div>
                )}
                {activeKey === 3 && (
                    <div>
                        <ScheduledCourses type={'month'} />
                    </div>
                )} */}
            </div>

            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleClose}
                    tabValue={'courses'}
                />
            )}
        </div>
    );
};

export default CoursesListing;
