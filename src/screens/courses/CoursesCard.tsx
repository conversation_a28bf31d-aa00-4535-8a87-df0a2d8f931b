import React, { useEffect, useState } from 'react';
import { Button, Switch } from 'antd';
import { useLocation } from 'wouter';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';

// Define Props Interface
interface CourseCardProps {
    workshopTitle: string;
    courseId: string;
    date: string | null;
    description: string;
    onEnroll?: () => void;
    isActive?: boolean;
    onToggleActive?: (newState: boolean) => void;
    isExpired?: boolean;
    seriviceCategory?: string;
}

const CourseCard: React.FC<CourseCardProps> = ({
    workshopTitle,
    date,
    courseId,
    description,
    onEnroll,
    isActive,
    onToggleActive,
    isExpired,
    seriviceCategory,
}) => {
    const [location, setLocation] = useLocation();
    const [isActiveState, setIsActiveState] = React.useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [pendingState, setPendingState] = useState<boolean | null>(null);

    useEffect(() => {
        if (isActive) {
            setIsActiveState(isActive);
        }
    }, [isActive]);

    const handleToggle = (checked: boolean) => {
        setPendingState(checked);
        setShowConfirmModal(true); // show modal first
    };

    const handleConfirmToggle = () => {
        if (pendingState !== null) {
            setIsActiveState(pendingState); // update visual switch
            onToggleActive?.(pendingState); // call parent
        }
        setShowConfirmModal(false);
        setPendingState(null);
    };

    const handleCancelToggle = () => {
        setShowConfirmModal(false);
        setPendingState(null);
    };

    return (
        <div className="rounded-lg border bg-[#f5f5f5] px-14 py-14">
            <div className=" flex items-center  justify-between ">
                <div>
                    <div className=" flex items-center gap-2">
                        <h2 className="text-3xl font-medium text-primary">
                            {workshopTitle}
                        </h2>
                        {/* <p className="text-2xl font-medium text-[#000]">
                            with {instructor}
                        </p> */}
                    </div>
                    {/* <p className="mt-2 text-18 font-normal text-[#455560]">
                        {seriviceCategory}
                    </p> */}
                    <p className="mt-5 ">{description}</p>
                    {date && (
                        <div className="mb-10 mt-5 flex items-center space-x-4 ">
                            <span className="rounded border bg-white px-6 py-2 text-xl text-[#455560]">
                                Date : {date}
                            </span>
                            {/* <span className="rounded border bg-white px-2 py-1 text-xl font-medium text-black">
                            From: {time}
                        </span> */}
                        </div>
                    )}

                    {/* <p className="mt-2 text-xl font-medium text-black">
                        Sign Up : {signUpCount}
                    </p> */}
                    <div className="mt-4 flex space-x-4">
                        <Button
                            type="default"
                            className="px-8 py-4 text-14"
                            onClick={() =>
                                setLocation(
                                    `/courses-customer-list/${courseId}`
                                )
                            }
                        >
                            Clients
                        </Button>
                        <Button
                            onClick={() =>
                                setLocation(`/courses-card-listing/${courseId}`)
                            }
                            className="bg-purpleLight px-8 py-4 text-14"
                            type="primary"
                        >
                            Manage Schedules
                        </Button>
                    </div>
                </div>
                <div className="text-right">
                    <Switch
                        className="mb-2"
                        checked={isActiveState && !isExpired}
                        onChange={handleToggle}
                        checkedChildren="Active"
                        disabled={isExpired}
                        unCheckedChildren="Inactive"
                    />
                </div>
                <CommonConfirmationModal
                    visible={showConfirmModal}
                    onConfirm={handleConfirmToggle}
                    onCancel={handleCancelToggle}
                    message={`Are you sure you want to ${
                        pendingState ? 'activate' : 'deactivate'
                    } this course?`}
                />
            </div>
        </div>
    );
};

export default CourseCard;
