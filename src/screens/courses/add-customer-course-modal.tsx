import React, { useState } from 'react';
import { Button, ConfigProvider, Form, Modal, Select, Table } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import Title from 'antd/es/typography/Title';
import { useAppDispatch } from '~/hooks/redux-hooks';
import {
    courseEnrollClient,
    coursesCustomerList,
    coursesSchedulingList,
} from '~/redux/actions/courses-action';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { useLoader } from '~/hooks/useLoader';
interface AddCustomerCourseModalProps {
    visible: boolean;
    onClose: () => void;
    selectedScheduleIds?: any;
    courseId?: string;
    currentPage?: number;
    pageSize?: number;
}

const AddCustomerCourseModal: React.FC<AddCustomerCourseModalProps> = ({
    visible,
    onClose,
    selectedScheduleIds,
    courseId,
    currentPage,
    pageSize,
}) => {
    const dispatch = useAppDispatch();
    const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
    const [tableData, setTableData] = useState<any[]>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const [errorModalVisible, setErrorModalVisible] = useState(false);
    const [errorLines, setErrorLines] = useState<string[]>([]);
    const [options, setOptions] = useState<any[]>([]);

    const handleDelete = (key: string) => {
        const updatedTable = tableData.filter((item) => item.key !== key);
        setTableData(updatedTable);

        setSelectedCustomers((prev) => prev.filter((id) => id !== key));
    };

    const fetchCustomerOptions = async (search: string, page: number) => {
        const payload = {
            page,
            pageSize: 10,
            courseId: courseId,
            // schedulingId: selectedScheduleIds[0],
        };

        const res = await dispatch(coursesCustomerList({ payload })).unwrap();
        console.log('Res-----------------', res);

        return [
            ...(res?.data?.list?.length
                ? [{ label: 'All', value: 'all' }]
                : []),
            ...(res?.data?.list?.map((customer: any) => ({
                label: customer.name,
                value: customer._id,
                phone: customer.mobile,
                balance: customer.balance,
                status: customer.status,
                ...customer,
            })) || []),
        ];
    };

    const handleSelectChange = (
        selectedValues: string[],
        selectedOptions: any[]
    ) => {
        let selectedData = [];
        const uniqueMap: Record<string, any> = {};
        if (selectedValues.includes('all')) {
            selectedData = options?.filter((option) => option.value !== 'all');
            setSelectedCustomers(selectedData?.map((option) => option.value));
        } else {
            setSelectedCustomers(selectedValues);
            selectedData = selectedOptions;
        }
        selectedData.forEach((option) => {
            uniqueMap[option.value] = {
                key: option.value,
                NAME: option.name || option.label,
                ID: `${option.clientId}`,
                PHONE_NO: option.mobile || option.phone,
                BALANCE: option.balance,
                STATUS: option.status,
                onDelete: handleDelete,
            };
        });

        setTableData(Object.values(uniqueMap));
    };

    console.log('Selected idss----', selectedScheduleIds);

    const handlModalClose = () => {
        setSelectedCustomers([]);
        setTableData([]);
        onClose();
    };

    const handleEnroll = () => {
        if (
            !selectedCustomers.length ||
            !selectedScheduleIds.length ||
            !courseId
        ) {
            return;
        }

        startLoader();

        const payload = {
            scheduleIds: selectedScheduleIds,
            customerIds: selectedCustomers,
            courseId: courseId,
        };

        dispatch(courseEnrollClient({ payload }))
            .unwrap()
            .then((res: any) => {
                console.log('Res--------', res);
                if (res?.statusCode === 400) {
                    console.log(res?.message);
                    const rawMessage =
                        res?.response?.data?.message[0] ||
                        res?.message[0] ||
                        '';

                    const lines = rawMessage
                        ?.split('\n')
                        ?.filter((line: string) => !!line);

                    setErrorLines(lines);
                    setErrorModalVisible(true);
                } else {
                    dispatch(
                        coursesSchedulingList({
                            payload: {
                                page: currentPage,
                                pageSize: pageSize,
                                courseId: courseId,
                            },
                        })
                    );
                    handlModalClose();
                }
            })
            .catch((err: any) => {
                console.error('Enroll Error:', err);
            })
            .finally(endLoader);
    };

    const columns = [
        { title: 'NAME', dataIndex: 'NAME', key: 'NAME' },
        { title: 'ID', dataIndex: 'ID', key: 'ID' },
        { title: 'PHONE NO', dataIndex: 'PHONE_NO', key: 'PHONE_NO' },
        {
            title: <p className="text-center">ACTION</p>,
            dataIndex: 'ACTION',
            key: 'ACTION',
            render: (text: any, record: any) => (
                <div className="flex flex-row justify-center">
                    <DeleteOutlined
                        style={{ color: 'red', cursor: 'pointer' }}
                        onClick={() => handleDelete(record.key)}
                    />
                </div>
            ),
        },
    ];

    return (
        <Modal
            footer={false}
            title="Add Customer"
            visible={visible}
            onCancel={handlModalClose}
            className="w-[70%]"
        >
            <InfiniteScrollSelect
                fetchOptions={fetchCustomerOptions}
                value={selectedCustomers}
                onChange={(value: any, option: any) =>
                    handleSelectChange(value, option)
                }
                placeholder="Select customers"
                // className="w-[50%] "
                className="w-[50%] border-b border-[#d1d5db]"
                mode="multiple"
                extractOptions={setOptions}
            />

            <div className="flex flex-col gap-4 pt-10">
                <Title className="text-[#1a3353]" level={4}>
                    Selected Customers
                </Title>

                <div className="lg:w-[100%]">
                    <ConfigProvider
                        theme={{
                            components: {
                                Table: { cellPaddingBlock: 5 },
                            },
                        }}
                    >
                        <Table
                            dataSource={tableData}
                            columns={columns}
                            pagination={false}
                        />
                    </ConfigProvider>
                </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-end justify-end gap-5 pt-16 lg:flex-row">
                <Button
                    className="border-1 border-[#1A3353] px-8 py-7 text-xl"
                    onClick={handlModalClose}
                >
                    Cancel
                </Button>
                <Button
                    type="primary"
                    onClick={handleEnroll}
                    loading={loader}
                    className="bg-purpleLight px-11 py-7 text-xl"
                >
                    Save
                </Button>
            </div>
            <Modal
                open={errorModalVisible}
                title="Enrollment Error"
                onCancel={() => setErrorModalVisible(false)}
                footer={[
                    <Button
                        key="ok"
                        className="bg-purpleLight px-11 py-7 text-xl text-white"
                        onClick={() => setErrorModalVisible(false)}
                    >
                        OK
                    </Button>,
                ]}
            >
                <ul className="list-disc pl-4">
                    {errorLines?.map((line, index) => (
                        <li key={index}>{line}</li>
                    ))}
                </ul>
            </Modal>
        </Modal>
    );
};

export default AddCustomerCourseModal;
