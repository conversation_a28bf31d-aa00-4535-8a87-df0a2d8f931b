import { DeleteOutlined } from '@ant-design/icons';
import { Button, Input, Pagination, Select, Table } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useState } from 'react';
import { useParams } from 'wouter';
import { navigate } from 'wouter/use-location';
import {
    capitalizeFirstLetter,
    formatDate,
    formatTime,
} from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { coursesCustomerList } from '~/redux/actions/courses-action';
import { getQueryParams } from '~/utils/getQueryParams';
const { Search } = Input;
function goBack() {
    window.history.back();
}

const columns = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
        width: '20%',
        render: (text: string) => capitalizeFirstLetter(text),
    },
    {
        title: 'PHONE NO ',
        dataIndex: 'mobile',
        key: 'mobile',
        width: '20%',
    },
    {
        title: 'DATE',
        dataIndex: 'date',
        key: 'date',
        width: '20%',
        render: (text: string) => formatDate(text),
    },
    {
        title: 'TIME',
        dataIndex: 'date',
        key: 'date',
        width: '20%',
        render: (text: string) => formatTime(text),
    },
    {
        title: 'CHECK IN VS ENROLLMENTS',
        dataIndex: '',
        key: 'checkIn',
        // align: 'center',
        width: '20%',
        render: (_: any, record: any) =>
            `${record.totalCheckedIns} / ${record.totalEnrollments}`,
    },
    // {
    //     title: <p className="text-center">ACTION</p>,
    //     dataIndex: 'ACTION',
    //     key: 'ACTION',
    // },
];
const CourseCustomerList = () => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const { id } = useParams<{ id: string }>();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        coursesCustomerList: state.course_store.coursesCustomerList,
        coursesCustomerListCount: state.course_store.coursesCustomerListCount,
    }));

    console.log('Stor-----------courses Customer List---', store);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    useEffect(() => {
        startLoader();
        const payload = {
            page: currentPage,
            pageSize: pageSizes,
            courseId: id,
        };
        if (id) {
            dispatch(coursesCustomerList({ payload }))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        }
    }, [currentPage, pageSizes]);

    return (
        <div className="flex flex-col gap-12">
            <div className="flex flex-row justify-between">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353] " level={4}>
                        List Of Clients
                    </Title>
                </div>

                {/* <Button
                    onClick={handleOpenModal}
                    className="rounded-xl bg-purpleLight px-10 text-white"
                >
                    Add +
                </Button> */}
            </div>
            <div className="rounded-lg border px-8 py-6 lg:w-[100%]">
                <Table
                    dataSource={store.coursesCustomerList}
                    columns={columns}
                    pagination={false}
                    loading={loader}
                />
            </div>
            <div className="flex justify-center  py-10">
                <Pagination
                    current={currentPage}
                    total={store.coursesCustomerListCount}
                    pageSizeOptions={['10', '20', '50']}
                    pageSize={pageSizes}
                    onChange={paginate}
                    hideOnSinglePage
                />
            </div>
        </div>
    );
};

export default CourseCustomerList;
