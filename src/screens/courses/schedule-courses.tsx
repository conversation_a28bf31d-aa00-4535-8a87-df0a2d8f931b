import { ConfigProvider, Dropdown, Menu, Pagination, Table } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import CommonTable from '~/components/common/commonTable';
import classes from '../services-and-products/classes';

import {
    EditOutlined,
    HomeOutlined,
    SettingOutlined,
    StopOutlined,
    UsergroupAddOutlined,
    UserOutlined,
    WarningOutlined,
} from '@ant-design/icons';
import { Link } from 'wouter';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { courseList, courseUpdate } from '~/redux/actions/courses-action';
import { capitalizeFirstLetter } from '~/components/common/function';
import { getQueryParams } from '~/utils/getQueryParams';
import { navigate } from 'wouter/use-location';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import dayjs from 'dayjs';
import CourseCard from './CoursesCard';

function formatDateSlashWise(isoDate: string | null | undefined): string {
    if (!isoDate) return 'N/A';

    const date = new Date(isoDate);
    if (isNaN(date.getTime())) return 'N/A';

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
}

const workshopData = [
    {
        workshopTitle: 'Workshop 1',
        instructor: 'Amber Dunney',
        date: '10/12/2028-10/14/2028',
        time: '8:00 pm - 9:00 pm',
        location: 'climb central delhi',
        description: 'Basic Yoga',
        signUpCount: '3/6',
    },
    {
        workshopTitle: 'Workshop 2',
        instructor: 'Sanjay Kumar',
        date: '10/12/2028-10/14/2028',
        time: '8:00 pm - 9:00 pm',
        location: 'climb central delhi',
        description: 'Ballet',
        signUpCount: '3/6',
    },
];

const menu = (
    <Menu>
        <Menu.Item key="1">
            <EditOutlined /> &nbsp; Edit class setup
        </Menu.Item>
        <Menu.Item key="2">
            <StopOutlined /> &nbsp; Cancel or delete class
        </Menu.Item>
        <Menu.Item key="3">
            <UserOutlined /> &nbsp; Change instructor
        </Menu.Item>
        <Menu.Item key="4">
            <UsergroupAddOutlined /> &nbsp; Assign assistant
        </Menu.Item>
        <Menu.Item key="5">
            <HomeOutlined /> &nbsp; Change room
        </Menu.Item>
        <Menu.Item key="6">
            <WarningOutlined /> &nbsp; View client sign-up restrictions
        </Menu.Item>
    </Menu>
);

const columns = (dynamicTitle: string) => [
    {
        title: dynamicTitle,
        dataIndex: 'date',
        key: 'date',
        render: (text: string, record: any) => (
            <div className="flex justify-between gap-6">
                <span>{text}</span>
                <Link to="/course-attendees" className="text-gray-500">
                    Sign up ({record.signedin})
                </Link>
            </div>
        ),
    },
    {
        title: 'Courese (click for data)',
        dataIndex: 'courses',
        key: 'courses',
    },
    {
        title: 'Teacher',
        dataIndex: 'teacher',
        key: 'teacher',
    },
    {
        title: 'Assintant',
        dataIndex: 'Assintant',
        key: 'Assintant',
    },
    {
        title: 'Location',
        dataIndex: 'Location',
        key: 'Location',
    },
    {
        title: 'Room',
        dataIndex: 'Room',
        key: 'Room',
    },
    {
        title: 'Edit',
        dataIndex: 'Edit',
        key: 'Edit',
    },
];

const generateDates = (type: string) => {
    const today = new Date();

    const generateDateRange = (startDate: Date, days: number) => {
        return Array.from({ length: days }, (_, i) => {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            return date;
        });
    };

    let dates: Date[];
    switch (type) {
        case 'today':
            dates = [today];
            break;
        case 'week':
            dates = generateDateRange(today, 7);
            break;
        case 'month':
            dates = generateDateRange(today, 30);
            break;
        default:
            dates = [today];
    }

    return dates;
};

// Function to generate mock data
const generateMockData = (type: string) => {
    const dates = generateDates(type);
    const courses = [
        'Yoga',
        'Pilates',
        'Zumba',
        'Spin Class',
        'Strength Training',
        'Meditation',
        'Cardio',
    ];
    const teachers = [
        'Alex Johnson',
        'Sarah Lee',
        'Emily Smith',
        'John Doe',
        'Anna Garcia',
        'Sophia Clark',
        'Daniel Evans',
    ];
    const assistants = [
        'Nina White',
        'Mark Davis',
        'Liam Brown',
        'Emma Wilson',
        'Chris Taylor',
        'Ethan Moore',
        'Mia Hall',
    ];
    const locations = [
        'Main Branch',
        'Downtown',
        'East Wing',
        'North Side',
        'West Branch',
    ];

    return dates.map((date, index) => ({
        key: `${index + 1}`,
        date: date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
        }),
        signedin: `${Math.floor(Math.random() * 20)}/${Math.floor(
            Math.random() * 25 + 5
        )}`,
        courses: courses[index % courses.length],
        teacher: teachers[index % teachers.length],
        Assintant: assistants[index % assistants.length],
        Location: locations[index % locations.length],
        Room: 100 + ((index % 7) + 1),
        Edit: (
            <Dropdown
                overlay={menu}
                trigger={['click']}
                placement="bottomRight"
            >
                <SettingOutlined />
            </Dropdown>
        ),
    }));
};

const getDynamicTitle = (type: string) => {
    // Use the first record's date and signed-in data to set the title
    const mockData = generateMockData(type);
    if (mockData.length > 0) {
        const record = mockData[0];
        const date = new Date(record.date);
        const formattedDate = date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'long',
            day: 'numeric',
            year: 'numeric',
        });

        const [signedIn, total] = record.signedin.split('/').map(Number);
        const percentage =
            total > 0 ? ((signedIn / total) * 100).toFixed(2) : '0.00';

        return `${formattedDate} Signed in ${signedIn} (${percentage}%)`;
    }
    return 'Class Date';
};

interface ScheduleCourses {
    type: string;
    dateRange?: any;
}
const ScheduledCourses: React.FC<ScheduleCourses> = ({ type, dateRange }) => {
    const dataSource = useMemo(() => generateMockData(type), [type]);
    const dynamicTitle = useMemo(() => getDynamicTitle(type), [type]);
    const params = getQueryParams();
    const dispatch = useAppDispatch();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();
    const [statusChange, setStatusChange] = useState<boolean>(false);

    const store = useAppSelector((state) => ({
        courseList: state.course_store.coursesList,
        courseListCount: state.course_store.coursesListCount,
    }));

    console.log('Course lisiting --------', store.courseList);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    useEffect(() => {
        startLoader();
        const payload: any = {
            page: currentPage,
            pageSize: pageSizes,
        };

        if (dateRange.startDate && dateRange.endDate) {
            payload.startDate = dayjs(dateRange.startDate, 'DD/MM/YYYY')
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
            payload.endDate = dayjs(dateRange.endDate, 'DD/MM/YYYY')
                .endOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        }
        console.log('Payload---------', payload);
        dispatch(courseList(payload))
            .unwrap()
            .then((res: any) => {
                console.log('Res-------', res);
            })
            .finally(endLoader);
    }, [
        currentPage,
        pageSizes,
        dateRange.startDate,
        dateRange.endDate,
        // statusChange,
    ]);

    return (
        <div className="">
            {loader ? (
                <div className="flex items-center justify-center py-10">
                    <FullLoader state={true} />
                </div>
            ) : (
                <>
                    <ConfigProvider
                        theme={{
                            components: {
                                Table: {
                                    cellPaddingBlock: 8,
                                },
                            },
                        }}
                    >
                        {/* <Table
                    dataSource={dataSource}
                    columns={columns(dynamicTitle)}
                    pagination={false}
                /> */}
                    </ConfigProvider>
                    <div className="space-y-6 p-4">
                        {store.courseList?.map(
                            (course: any, courseIndex: number) => {
                                const appointmentBoxes: any = (
                                    <div className="flex flex-row flex-wrap gap-3">
                                        {course.appointmentTypes?.map(
                                            (type: any, idx: number) => (
                                                <div
                                                    key={idx}
                                                    className="rounded-lg border bg-white px-4 py-2 text-xl text-[#455560]"
                                                >
                                                    {course.serviceCategoryName}{' '}
                                                    - {type.name}
                                                </div>
                                            )
                                        )}
                                    </div>
                                );

                                return (
                                    <CourseCard
                                        seriviceCategory={
                                            course.serviceCategoryName
                                        }
                                        courseId={course._id}
                                        key={courseIndex + course?._id}
                                        workshopTitle={capitalizeFirstLetter(
                                            course.name
                                        )}
                                        date={
                                            course.startDate
                                                ? `${formatDateSlashWise(
                                                      course.startDate
                                                  )} - ${formatDateSlashWise(
                                                      course.endDate
                                                  )}`
                                                : null
                                        }
                                        isActive={course.isActive}
                                        isExpired={course.isExpired}
                                        description={appointmentBoxes}
                                        onToggleActive={(
                                            newStatus: boolean
                                        ) => {
                                            setStatusChange(true);
                                            dispatch(
                                                courseUpdate({
                                                    courseId: course._id,
                                                    isActive: newStatus,
                                                    page: 1,
                                                    pageSize: 10,
                                                })
                                            );
                                        }}
                                    />
                                );
                            }
                        )}

                        <div className="flex justify-center  py-10">
                            <Pagination
                                current={currentPage}
                                total={store.courseListCount}
                                pageSizeOptions={['10', '20', '50']}
                                pageSize={pageSizes}
                                onChange={paginate}
                                hideOnSinglePage
                            />
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default ScheduledCourses;
