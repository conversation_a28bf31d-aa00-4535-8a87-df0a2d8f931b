import React, { useEffect, useState } from 'react';
import {
    Button,
    ConfigProvider,
    DatePicker,
    Form,
    Select,
    FormProps,
} from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { SchedulingReport } from '~/redux/actions/report.action';
import { capitalizeFirstLetter } from '~/components/common/function';
import { useWatch } from 'antd/es/form/Form';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { GetStaffList } from '~/redux/actions/staff-action';
import dayjs from 'dayjs';
import { FacilitiesList } from '~/redux/actions/facility-action';

const SchedulingReports = () => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [options, setOptions] = useState<any[]>([]);
    const [staffOptions, setStaffOptions] = useState<any[]>([]);
    useEffect(() => {
        dispatch(FacilitiesList({}));
        form.resetFields();
    }, []);
    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        trainerList: state.appointment_store.trainerList,
    }));
    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));
    const selectedClassTypes = useWatch('classType', form);

    const shouldShowStaff =
        selectedClassTypes?.includes('personalAppointment') ||
        selectedClassTypes?.includes('courses');

    const fetchStaffOption = async (search: string, page: number) => {
        const facilityId = form.getFieldValue('facilityId');
        if (!facilityId) return [];
        const res = await dispatch(
            GetStaffList({
                page,
                pageSize: 10,
                locationId: [facilityId],
            })
        ).unwrap();

        return [
            ...(res?.res?.data?.data?.length
                ? [{ label: 'All', value: 'all' }]
                : []),
            ...(res?.res?.data?.data?.map((staff: any) => ({
                label: capitalizeFirstLetter(
                    `${staff.firstName} ${staff.lastName}`
                ),
                value: staff.userId,
            })) || []),
        ];
    };

    const handleSelectChange = (selectedValues: string[]) => {
        console.log(selectedValues);
        let selectedData = [];
        if (selectedValues.includes('all')) {
            selectedData = options
                ?.filter((option) => option.value !== 'all')
                .map((option) => option.value);
            // form.setFieldsValue({
            //     staffIds: selectedData?.map((option) => option.value),
            // });
            setStaffOptions(selectedData);
        } else setStaffOptions(selectedValues);
    };

    const onFinish: FormProps['onFinish'] = async (values) => {
        const payload: any = {
            facilityIds: [values?.facilityId],
            startDate: values?.startDate?.format('YYYY-MM-DD HH:mm:ss'),
            endDate: values?.endDate?.format('YYYY-MM-DD HH:mm:ss'),
            responseType: 'stream',
            staffIds:
                shouldShowStaff && staffOptions?.length ? staffOptions : null,
            classType: values?.classType,
            scheduleStatus: values?.scheduleStatus,
        };
        dispatch(SchedulingReport(payload))
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    const blob = new Blob([res.data], {
                        type: 'text/csv;charset=utf-8;',
                    });
                    const url = window.URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    const date = new Date();
                    const istOffset = 330 * 60 * 1000; // IST is UTC+5:30
                    const istDate = new Date(date.getTime() + istOffset);
                    const istTimestamp = istDate
                        .toISOString()
                        .replace(/T/, '_')
                        .replace(/:/g, '-')
                        .split('.')[0];

                    const filename = `scheduling_Report_${istTimestamp}`;
                    link.setAttribute('download', `${filename}.csv`);

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
            })
            .finally(() => {
                form.resetFields();
                setStaffOptions([]);
            });
    };

    const classTypes = [
        {
            label: 'Personal Appointment',
            value: 'personalAppointment',
        },
        {
            label: 'Classes',
            value: 'classes',
        },
        {
            label: 'Bookings',
            value: 'bookings',
        },
        {
            label: 'Courses',
            value: 'courses',
        },
    ];
    return (
        <>
            <p className=" text-2xl font-semibold text-[#1a3353]">
                Scheduling at a Glance
            </p>
            <div className="py-10">
                <ConfigProvider
                    theme={{
                        token: { controlHeight: 40 },
                    }}
                >
                    <Form
                        layout="vertical"
                        className="w-full"
                        form={form}
                        onFinish={onFinish}
                        initialValues={{
                            startDate: dayjs().startOf('day'),
                            facilityId: FacilityOptions?.[0]?.value || null,
                        }}
                    >
                        <div className="flex w-[100%] flex-row items-center justify-between">
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Start Date"
                                    name="startDate"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a start date',
                                        },
                                    ]}
                                    className="w-full"
                                >
                                    <DatePicker
                                        className="w-[100%]"
                                        format="DD-MMM-YYYY"
                                    />
                                </Form.Item>
                            </div>
                            <div className="w-[48%]">
                                <Form.Item
                                    label="End Date"
                                    name="endDate"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select an end date',
                                        },
                                    ]}
                                    className="w-full"
                                >
                                    <DatePicker
                                        className="w-[100%]"
                                        format="DD-MMM-YYYY"
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-[100%] flex-row items-center justify-between ">
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Facility"
                                    name="facilityId"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a location.',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="w-[100%]"
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(
                                                (option &&
                                                    (
                                                        option as {
                                                            label: string;
                                                        }
                                                    ).label) ??
                                                    ''
                                            )
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={FacilityOptions}
                                    />
                                </Form.Item>
                            </div>

                            <div className="w-[48%]">
                                <Form.Item
                                    label="Service Type"
                                    name="classType"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a Service Type.',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="w-[100%]"
                                        mode="multiple"
                                        maxTagCount="responsive"
                                        showSearch
                                        placeholder="Service Type"
                                        filterOption={(input, option) =>
                                            String(
                                                (option &&
                                                    (
                                                        option as {
                                                            label: string;
                                                        }
                                                    )?.label) ??
                                                    ''
                                            )
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={classTypes}
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-[100%] flex-row items-center justify-between">
                            {shouldShowStaff && (
                                <div className="w-[48%]">
                                    <p className="pb-2 ps-2 text-[13px] font-medium text-[#1a3353]">
                                        Select Staffs
                                        <span className="text-2xl text-red-400">
                                            *
                                        </span>
                                    </p>
                                    <InfiniteScrollSelect
                                        // className="w-[100%] "
                                        className="w-[100%] border-b border-[#d1d5db]"
                                        fetchOptions={fetchStaffOption}
                                        onChange={(value: any, option: any) =>
                                            handleSelectChange(value)
                                        }
                                        placeholder="Select staffs"
                                        mode="multiple"
                                        extractOptions={setOptions}
                                    />
                                </div>
                            )}
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Schedule Status"
                                    name="scheduleStatus"
                                >
                                    <Select
                                        className="w-[100%]"
                                        mode="multiple"
                                        showSearch
                                        placeholder="Schedule Status"
                                        filterOption={(input, option) =>
                                            String(
                                                (option &&
                                                    (
                                                        option as {
                                                            label: string;
                                                        }
                                                    )?.label) ??
                                                    ''
                                            )
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={[
                                            {
                                                label: 'Booked',
                                                value: 'booked',
                                            },
                                            {
                                                label: 'Checked In',
                                                value: 'checked-in',
                                            },
                                            {
                                                label: 'Canceled',
                                                value: 'canceled',
                                            },
                                        ]}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex flex-row justify-end">
                            <Button
                                className="mt-10 bg-purpleLight px-14 py-7 text-xl text-white  "
                                htmlType="submit"
                            >
                                Export as CSV
                            </Button>
                        </div>
                    </Form>
                </ConfigProvider>
            </div>
        </>
    );
};

export default SchedulingReports;
