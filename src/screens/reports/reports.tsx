import { ConfigProvider, Tabs, TabsProps } from 'antd';

import Title from 'antd/es/typography/Title';
import SalesReport from './sales-report';
import SchedulingReports from './scheduling-report';
import { useEffect } from 'react';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { useAppDispatch } from '~/hooks/redux-hooks';

const Reports = () => {
    const onChange = (key: string) => {
        console.log(key);
    };

    const dispatch = useAppDispatch();
    useEffect(() => {
        dispatch(FacilitiesList({}));
    }, []);
    const items: TabsProps['items'] = [
        {
            key: '1',
            label: (
                <div className="flex items-center">
                    {/* <img src={Asset.profileIconTab} /> */}

                    <div>Total Sales </div>
                </div>
            ),
            children: <SalesReport heading="Total Sales" type="Total_Sales" />,
        },
        {
            key: '2',
            label: (
                <div className="flex items-center">
                    {/* <img src={Asset.profileIconTab} /> */}

                    <div>Category - Wise Sales </div>
                </div>
            ),
            // children: <FollowUpNotes />,
            children: (
                <SalesReport
                    heading="Category - Wise Sales"
                    type="Category_Wise_Sales"
                />
            ),
        },
        {
            key: '3',
            label: (
                <div className="flex items-center">
                    {/* <img src={Asset.profileIconTab} /> */}

                    <div>Item - Wise Sales</div>
                </div>
            ),
            // children: <DocumentLocker />,
            children: (
                <SalesReport
                    heading="Item - Wise Sales"
                    type="Item_Wise_Sales"
                />
            ),
        },
        {
            key: '4',
            label: (
                <div className="flex items-center">
                    {/* <img src={Asset.profileIconTab} /> */}

                    <div>Sales by Employee</div>
                </div>
            ),
            // children: <DocumentLocker />,
            children: (
                <SalesReport
                    heading="Sales by Employee"
                    type="Sales_BY_Employ"
                />
            ),
        },
        {
            key: '5',
            label: (
                <div className="flex items-center">
                    {/* <img src={Asset.profileIconTab} /> */}

                    <div>Scheduling at a Glance</div>
                </div>
            ),
            // children: <DocumentLocker />,
            children: <SchedulingReports />,
        },
    ];
    return (
        <>
            <Title className=" text-[#1a3353]" level={4}>
                Reports
            </Title>
            <div className=" mt-10  ">
                <ConfigProvider
                    theme={{
                        components: {},
                    }}
                >
                    <Tabs
                        defaultActiveKey="1"
                        items={items}
                        onChange={onChange}
                        tabPosition="left"
                        renderTabBar={(props, DefaultTabBar) => (
                            <DefaultTabBar {...props}>
                                {(node) => (
                                    <div
                                        style={{
                                            backgroundColor:
                                                node.key === props.activeKey
                                                    ? 'rgba(129, 67, 209, 0.1)'
                                                    : 'transparent',
                                            marginBottom: '20px',
                                            borderRadius: '4px',
                                            transition: 'background-color 0.3s',
                                        }}
                                    >
                                        {node}
                                    </div>
                                )}
                            </DefaultTabBar>
                        )}
                    />
                </ConfigProvider>
            </div>
        </>
    );
};

export default Reports;
