import { useEffect, useState } from 'react';
import {
    <PERSON><PERSON>,
    ConfigProvider,
    DatePicker,
    Form,
    Select,
    FormProps,
} from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    SalesByCategoryReport,
    SalesByEmployReport,
    SalesByItemReport,
    SalesReport,
} from '~/redux/actions/report.action';
import { capitalizeFirstLetter } from '~/components/common/function';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { GetStaffList } from '~/redux/actions/staff-action';
import dayjs from 'dayjs';

const SalesReports = ({ heading, type }: any) => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [options, setOptions] = useState<any[]>([]);

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        trainerList: state.appointment_store.trainerList,
    }));
    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const fetchStaffOption = async (search: string, page: number) => {
        const facilityId = form.getFieldValue('facilityId');
        if (!facilityId) return [];
        const res = await dispatch(
            GetStaffList({
                page,
                pageSize: 10,
                locationId: [facilityId],
            })
        ).unwrap();

        return [
            ...(res?.res?.data?.data?.length
                ? [{ label: 'All', value: 'all' }]
                : []),
            ...(res?.res?.data?.data?.map((staff: any) => ({
                label: capitalizeFirstLetter(
                    `${staff.firstName} ${staff.lastName}`
                ),
                value: staff.userId,
            })) || []),
        ];
    };

    const handleSelectChange = (selectedValues: string[]) => {
        let selectedData = [];
        if (selectedValues.includes('all')) {
            selectedData = options?.filter((option) => option.value !== 'all');
            form.setFieldsValue({
                staffIds: selectedData?.map((option) => option.value),
            });
        } else form.setFieldsValue({ staffIds: selectedValues });
    };

    const onFinish: FormProps['onFinish'] = async (values) => {
        const payload: any = JSON.parse(
            JSON.stringify({
                facilityIds: [values?.facilityId],
                startDate: values?.startDate?.format('YYYY-MM-DD HH:mm:ss'),
                endDate: values?.endDate?.format('YYYY-MM-DD HH:mm:ss'),
                responseType: 'stream',
            })
        );

        let functionName;
        switch (type) {
            case 'Total_Sales':
                functionName = SalesReport;
                payload.facilityId = [values?.facilityId];
                delete payload.facilityIds;
                break;
            case 'Item_Wise_Sales':
                functionName = SalesByItemReport;
                break;
            case 'Category_Wise_Sales':
                functionName = SalesByCategoryReport;
                break;
            case 'Sales_BY_Employ':
                functionName = SalesByEmployReport;
                payload.staffIds = values?.staffIds?.length
                    ? values?.staffIds
                    : null;
                break;
        }

        if (functionName)
            dispatch(functionName(payload))
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        const blob = new Blob([res.data], {
                            type: 'text/csv;charset=utf-8;',
                        });
                        const url = window.URL.createObjectURL(blob);

                        const link = document.createElement('a');
                        link.href = url;
                        const date = new Date();
                        const istOffset = 330 * 60 * 1000; // IST is UTC+5:30
                        const istDate = new Date(date.getTime() + istOffset);
                        const istTimestamp = istDate
                            .toISOString()
                            .replace(/T/, '_')
                            .replace(/:/g, '-')
                            .split('.')[0];

                        const filename = `${type}_Report_${istTimestamp}`;
                        link.setAttribute('download', `${filename}.csv`);

                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }
                })
                .catch((error: any) => {
                    console.error('Export failed:', error);
                })
                .finally(() => {
                    form.resetFields();
                });
    };

    useEffect(() => {
        form.resetFields();
    }, [type]);

    return (
        <>
            <p className=" text-2xl font-semibold text-[#1a3353]">{heading}</p>
            <div className="py-10">
                <ConfigProvider
                    theme={{
                        token: { controlHeight: 40 },
                    }}
                >
                    <Form
                        layout="vertical"
                        className="w-full"
                        form={form}
                        onFinish={onFinish}
                        initialValues={{
                            startDate: dayjs().startOf('day'),
                            facilityId: FacilityOptions?.[0]?.value || null,
                        }}
                    >
                        <div className="flex w-[100%] flex-row items-center justify-between">
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Start Date"
                                    name="startDate"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a start date',
                                        },
                                    ]}
                                    className="w-full"
                                >
                                    <DatePicker
                                        className="w-[100%]"
                                        format="DD-MMM-YYYY"
                                    />
                                </Form.Item>
                            </div>
                            <div className="w-[48%]">
                                <Form.Item
                                    label="End Date"
                                    name="endDate"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select an end date',
                                        },
                                    ]}
                                    className="w-full"
                                >
                                    <DatePicker
                                        className="w-[100%]"
                                        format="DD-MMM-YYYY"
                                    />
                                </Form.Item>
                            </div>
                        </div>
                        <div className="flex w-[100%] flex-row items-center justify-between ">
                            <div className="w-[48%]">
                                <Form.Item
                                    label="Facility"
                                    name="facilityId"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please select a location.',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="w-[100%]"
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(
                                                (option &&
                                                    (
                                                        option as {
                                                            label: string;
                                                        }
                                                    ).label) ??
                                                    ''
                                            )
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={FacilityOptions}
                                    />
                                </Form.Item>
                            </div>
                            {type === 'Sales_BY_Employ' && (
                                <div className="w-[48%]">
                                    <Form.Item
                                        label="Staff"
                                        name="staffIds"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select a Trainer.',
                                            },
                                        ]}
                                    >
                                        <InfiniteScrollSelect
                                            className="border-b border-[#d1d5db]"
                                            fetchOptions={fetchStaffOption}
                                            onChange={(
                                                value: any,
                                                option: any
                                            ) => handleSelectChange(value)}
                                            placeholder="Select staffs"
                                            mode="multiple"
                                            extractOptions={setOptions}
                                        />
                                    </Form.Item>
                                </div>
                            )}
                        </div>

                        <div className="flex flex-row justify-end">
                            <Button
                                className="mt-10 bg-purpleLight px-14 py-7 text-xl text-white  "
                                htmlType="submit"
                            >
                                Export as CSV
                            </Button>
                        </div>
                    </Form>
                </ConfigProvider>
            </div>
        </>
    );
};

export default SalesReports;
