import React, { useState, useEffect } from 'react';
import {
    ConfigProvider,
    Row,
    Col,
    Typography,
    Form,
    Input,
    Button,
} from 'antd';
import { useParams, useLocation } from 'wouter';
import CommonTable from '~/components/common/commonTable';
import PackageModal from './addPackageModal';
import { useLoader } from '~/hooks/useLoader';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import {
    createFeature,
    getFeatureById,
    updateFeatureDetail,
} from '~/redux/actions/feature-action';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import PackageListingTable from './packageListingTable';

const { Title } = Typography;
function goBack() {
    window.history.back();
}
const column = [
    {
        title: 'Names',
        dataIndex: 'name',
        // width: "25%",
    },
    {
        title: 'Pricing',
        dataIndex: 'price',
        // width: "25%",
    },
    {
        title: 'Color',
        dataIndex: 'color',
        // width: "25%",
    },
    {
        title: 'Sorting',
        dataIndex: 'sorting',
        // width: "25%",
    },
];
const createFeatureTab: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const { id } = useParams<{ id: string }>();
    const [showPackageModal, setShowPackageModal] = useState<boolean>(false);
    const [packageListing, setPackageListing] = useState<any>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const [location, setLocation] = useLocation();
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [removepackageData, setRemovepackageData] = useState();

    const featchFeatureDatabyId = async () => {
        startLoader();
        try {
            const featureDetails = await dispatch(
                getFeatureById({ featureId: id })
            ).unwrap();
            console.log(featureDetails);
            if (featureDetails) {
                // Set the form fields with the fetched data
                form.setFieldsValue({
                    subCategoryName: featureDetails.name,
                });

                // Set the package listing if available
                if (featureDetails.packageList) {
                    const formattedPackages = featureDetails.packageList.map(
                        (pkg: any) => ({
                            value: pkg.packageId,
                            color: pkg.color,
                            sorting: pkg.sorting,
                            name: pkg.packageDetails?.name,
                            price: pkg.packageDetails?.price.toFixed(2),

                            _id: pkg.packageId,
                        })
                    );
                    setPackageListing(formattedPackages);
                }
            }
        } catch (error) {
            console.error('Error fetching feature data:', error);
        } finally {
            endLoader();
        }
    };

    useEffect(() => {
        if (id && id !== '0') {
            featchFeatureDatabyId();
        }
    }, [id]);
    const onFinish = async (values: any) => {
        startLoader();
        const payLoad = {
            name: values.subCategoryName,
            packageList: packageListing.map((pkg: any) => ({
                packageId: pkg.value,
                color: pkg.color,
                sorting: pkg.sorting,
            })),
        };

        try {
            if (id === '0') {
                console.log(payLoad);
                await dispatch(createFeature(payLoad)).unwrap();
            } else {
                await dispatch(
                    updateFeatureDetail({ reqData: payLoad, featureId: id })
                ).unwrap();
            }
            setLocation('/setting/feature-tab');
        } catch (error) {
            console.error('Error submitting form:', error);
        } finally {
            endLoader();
        }
    };

    const showModal = () => {
        setShowPackageModal(true);
    };
    const hideAddPackageModal = () => {
        setShowPackageModal(false);
    };
    const packageData = (fields: any, formRef: any, packages: any) => {
        const packageListing = [];
        if (packages) {
            packages['color'] = fields.color;
            packages['sorting'] = fields.sorting;
            packageListing.push(packages);
        }
        setPackageListing((prevPackageListing: any[]) => [
            ...prevPackageListing,
            packages,
        ]);
        setShowPackageModal(false);
    };
    const openConfirmationModal = (record: any, isDelete: boolean = false) => {
        console.log(record);
        setConfirmationModalVisible(true);
        setRemovepackageData(record);
    };
    const handleCancelConfirmation = () => {
        setConfirmationModalVisible(false);
    };
    const removePackageFromList = () => {
        if (removepackageData) {
            setPackageListing((prevPackageListing: any[]) =>
                prevPackageListing.filter(
                    (pkg) => pkg.value !== removepackageData.value
                )
            );
        }
        setConfirmationModalVisible(false);
    };

    const actionColumn = [
        {
            title: 'Action',
            width: '90px',
            key: 'action',
            render: (_: any, record: any) => {
                console.log('Record: ', record);
                return (
                    <span className="flex gap-2">
                        <div
                            className="cursor-pointer"
                            onClick={() => openConfirmationModal(record, true)}
                        >
                            <img
                                src="/icons/common/delete.svg"
                                alt="delete"
                                className="h-[20px]"
                            />
                        </div>
                    </span>
                );
            },
        },
    ];

    const combinedColumns = [...column, ...actionColumn];
    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Typography: {
                            titleMarginBottom: 0,
                            titleMarginTop: 0,
                        },
                        Input: {
                            colorPrimary: '#E6EBF1',
                            colorPrimaryActive: '#E6EBF1',
                            colorPrimaryHover: '#E6EBF1',
                        },
                        Select: {
                            colorPrimary: '#E6EBF1',
                            colorPrimaryActive: '#E6EBF1',
                            colorPrimaryHover: '#E6EBF1',
                        },
                    },
                }}
            >
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="Back"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {id === '0'
                            ? 'Create Featured Tabs'
                            : 'Edit Featured Tab'}
                    </Title>
                </div>
                <Row className="mt-16">
                    <Col
                        className="w-[100%] rounded-3xl border p-16 sm:w-[80%]"
                        span={30}
                    >
                        <Form
                            name="featureCreate"
                            layout="vertical"
                            size="large"
                            form={form}
                            initialValues={{ remember: true }}
                            onFinish={onFinish}
                            autoComplete="off"
                        >
                            <Form.Item
                                label="Name"
                                name="subCategoryName"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter a Featured name.',
                                    },
                                    {
                                        max: 50,
                                        message:
                                            'Name cannot exceed 50 characters.',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="Enter Featured name"
                                    maxLength={50}
                                    className="w-[40%]"
                                />
                            </Form.Item>

                            <div className="pt-10">
                                <PackageListingTable
                                    heading="Packages"
                                    columns={combinedColumns}
                                    dataSource={packageListing}
                                    // className="min-w-min"
                                    addNewModal={
                                        packageListing.length < 12
                                            ? true
                                            : false
                                    }
                                    openModal={showModal}
                                    addNewTitle="Add Package"
                                />
                            </div>
                            <Form.Item className="mt-8 flex justify-end gap-8 @sm:hidden ">
                                <Button
                                    // loading={loader}
                                    onClick={() => goBack()}
                                    htmlType="button"
                                    className="w-[110px] border-[#1A3353] bg-[#fff]  text-black "
                                >
                                    <p> Cancel </p>
                                </Button>
                                <Button
                                    // loading={loader}
                                    htmlType="submit"
                                    className="w-[110px] bg-purpleLight text-white lg:ms-7"
                                >
                                    <p> Save </p>
                                </Button>
                            </Form.Item>
                        </Form>
                    </Col>
                </Row>
            </ConfigProvider>
            <PackageModal
                visible={showPackageModal}
                onClose={hideAddPackageModal}
                onSave={packageData}
                addedPackage={packageListing}
            />
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={removePackageFromList}
                onCancel={handleCancelConfirmation}
                message="Are you sure you want to remove this Package?"
            />
        </>
    );
};
export default createFeatureTab;
