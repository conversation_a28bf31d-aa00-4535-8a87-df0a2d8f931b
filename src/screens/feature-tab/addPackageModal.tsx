import React, { useState, useEffect, useRef } from 'react';
import { Form, Modal, Select, Input, Button, Spin, ColorPicker } from 'antd';
import {
    PricingList,
    PricingListByActiveStatus,
} from '~/redux/actions/pricing-actions';
import { useDispatch } from 'react-redux';
import { useLoader } from '~/hooks/useLoader';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';

interface ISPackageFormFields {
    packageName: string;
    pricing: string;
    color: string;
    sorting: string;
}

interface AddPackageModalProps {
    visible: boolean;
    onClose: () => void;
    onSave: (fields: ISPackageFormFields, formRef: any) => void;
    addedPackage: any;
}

const PackageModal: React.FC<AddPackageModalProps> = ({
    visible,
    onClose,
    onSave,
    addedPackage,
}) => {
    const [form] = Form.useForm();
    const [color, setColor] = useState('#ffffff');
    const [loader, startLoader, endLoader] = useLoader();
    const [priceListingOption, setPriceListingOption] = useState([]); // Combined options
    const [packages, setPackage] = useState<string>('');

    const dispatch = useDispatch();

    // Handle color change
    const handleColorChange = (newColor: any) => {
        // console.log(newColor?.toHexString())
        console.log(newColor?.toHexString(), 'lllll');
        setColor(newColor?.toHexString());
        form.setFieldsValue({ color: newColor?.toHexString() });
    };

    const fetchPricingList = async () => {
        startLoader(true);
        try {
            const response = await dispatch(
                PricingListByActiveStatus({
                    page: 1,
                    pageSize: 50,
                })
            ).unwrap();
            console.log(addedPackage, 'added Package', response);

            // Filter out items that are already in addedPackage in sub category
            const filteredList = response.data.data.list.filter(
                (item: any) =>
                    !addedPackage.some((pkg: any) => pkg._id === item._id)
            );

            // Map the filtered list into the required format
            const newOptions = filteredList.map((item: any) => ({
                value: item._id,
                label: item.name,
                id: item._id,
                ...item,
            }));

            setPriceListingOption(newOptions);
        } catch (error) {
            console.error(error);
        } finally {
            endLoader();
        }
    };
    const handleModalClose = () => {
        form.resetFields();
        setColor('#ffffff');
        onClose();
    };

    useEffect(() => {
        form.resetFields();
        setColor('#ffffff');
        if (visible) {
            startLoader();
            // fetchPricingList(); // Fetch initial data
        }
    }, [visible]);

    const handlePackageChange = (value: string, option: any) => {
        form.setFieldsValue({
            // pricing: option.price,
            pricing: Number(option.price).toFixed(2),
        });
        setPackage(option);
    };

    const fetchOptions = async (searchText: string, page: number) => {
        const response = await dispatch(
            PricingListByActiveStatus({
                page,
                pageSize: 20,
                search: searchText,
            })
        ).unwrap();

        console.log('Response:----------- ' + response);

        // Filter out already-added packages
        const filteredList = response?.data?.data?.list?.filter(
            (item: any) =>
                !addedPackage.some((pkg: any) => pkg._id === item._id)
        );

        // Return in the format Select expects
        return filteredList?.map((item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
            ...item,
        }));
    };

    return (
        <Modal
            title={<p className=" text-2xl text-[#1A3353]">Add Package</p>}
            className="lg:w-[55%]"
            footer={false}
            visible={visible}
            onCancel={handleModalClose}
        >
            <Form
                className="py-6"
                layout="vertical"
                onFinish={(fields) => onSave(fields, form, packages)}
                form={form}
                initialValues={{ color }}
            >
                <div className="flex w-[100%] flex-col  ">
                    <div className="flex flex-row items-center gap-5 ">
                        <div className="w-[50%]">
                            <Form.Item
                                label="Package Name"
                                name="packageName"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please input!',
                                    },
                                ]}
                            >
                                {/* <Select
                                    filterOption={(input, option: any) =>
                                        String(option?.label ?? '')
                                            .toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={priceListingOption}
                                    // onPopupScroll={(e) => handlePopupScroll(e)}
                                    loading={loader}
                                    notFoundContent={
                                        loader ? (
                                            <Spin size="small" />
                                        ) : (
                                            'No data'
                                        )
                                    }
                                    onChange={handlePackageChange}
                                    placeholder="Select Package"
                                /> */}
                                <InfiniteScrollSelect
                                    className="border-b border-[#d1d5db]"
                                    fetchOptions={fetchOptions}
                                    value={form.getFieldValue('packageName')}
                                    onChange={(value, option) =>
                                        handlePackageChange(value, option)
                                    }
                                    placeholder="Select Package"
                                    pageSize={20}
                                />
                            </Form.Item>
                        </div>
                        <div className="w-[50%]">
                            <Form.Item
                                label="Pricing"
                                name="pricing"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please input!',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter Pricing" disabled />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="flex flex-row items-center gap-4 ">
                        {/* <div className="w-[50%] ">
                            <Form.Item
                                className=""
                                label="Color"
                                name="color"

                            >

                                <ColorPicker
                                    className="h-[31px] w-[100%] border border-[#ccc]"
                                    style={{
                                        display: 'inline-block',
                                        // width: '140px',
                                        // height: '40px',
                                        backgroundColor: color,
                                        // border: '1px solid #ccc',
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                    }}
                                    format="hex"
                                    value={color}
                                    onChange={handleColorChange}
                                />
                            </Form.Item> */}
                        <div className="w-[50%]">
                            <Form.Item
                                label="Color"
                                className="w-[100%] "
                                name="color"
                                initialValue={color}
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please pick a color!',
                                    },
                                ]}
                            >
                                <div
                                    className="flex h-[30px] w-[100%] items-center  border-[#ccc]"
                                    style={{
                                        borderRadius: '4px',
                                        padding: '5px',
                                        cursor: 'pointer',
                                    }}
                                >
                                    <ColorPicker
                                        format="hex"
                                        value={color}
                                        onChange={handleColorChange}
                                        style={{
                                            display: 'inline-block',
                                            width: '100%',
                                            height: '100%',
                                            backgroundColor: color,
                                        }}
                                    />
                                </div>
                            </Form.Item>
                        </div>
                        <div className="w-[50%]">
                            <Form.Item
                                label="Sorting"
                                name="sorting"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please Select Sorting',
                                    },
                                ]}
                            >
                                <Select placeholder="Select a number">
                                    {Array.from(
                                        { length: 12 },
                                        (_, index) => index + 1
                                    )
                                        .filter(
                                            (number) =>
                                                !addedPackage.some(
                                                    (pkg: any) =>
                                                        pkg.sorting === number
                                                )
                                        )
                                        .map((number) => (
                                            <Select.Option
                                                key={number}
                                                value={number}
                                            >
                                                {number}
                                            </Select.Option>
                                        ))}
                                </Select>
                            </Form.Item>
                        </div>
                    </div>
                </div>

                <Form.Item className="mt-8 flex justify-end gap-8 @sm:hidden ">
                    <Button
                        // loading={loader}
                        onClick={handleModalClose}
                        htmlType="button"
                        className="w-[110px] border-[#1A3353] bg-[#fff]  text-black "
                    >
                        <p> Cancel </p>
                    </Button>
                    <Button
                        // loading={loader}
                        htmlType="submit"
                        className="w-[110px] bg-purpleLight  text-white lg:ms-7"
                    >
                        <p> Save </p>
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default PackageModal;
