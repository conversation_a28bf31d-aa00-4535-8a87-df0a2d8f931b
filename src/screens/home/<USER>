import React from 'react';
import { Card, Typography, Space, DatePicker, Row, Col } from 'antd';
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON>Axis,
    Y<PERSON><PERSON>s,
    Tooltip,
    ResponsiveContainer,
    CartesianGrid,
} from 'recharts';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const data = [
    { name: 'Personal Training', value: 70 },
    { name: 'Memberships', value: 50 },
    { name: 'Group Classes', value: 60 },
    { name: 'Courses', value: 30 },
];

const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload?.length) {
        return (
            <div
                style={{
                    background: '#4B3F6B',
                    padding: '4px 8px',
                    borderRadius: '8px',
                    color: 'white',
                    fontSize: 12,
                }}
            >
                {payload[0].value}%
            </div>
        );
    }
    return null;
};

const PopularPackageChart = () => {
    return (
        <div className="flex h-full flex-col pb-16">
            <Row
                justify="space-between"
                align="middle"
                style={{ marginBottom: 20 }}
            >
                <Title level={5} style={{ margin: 0 }}>
                    Lorem Ipsum
                </Title>
                <DatePicker
                    picker="month"
                    defaultValue={dayjs('2025-05', 'YYYY-MM')}
                    format="MMMM YYYY"
                    allowClear={false}
                    style={{ borderRadius: 8 }}
                />
            </Row>

            <Row gutter={32} style={{ marginBottom: 24 }}>
                <Col>
                    <Title level={4} style={{ margin: 0 }}>
                        523,201
                    </Title>
                    <Space size="small">
                        <span
                            style={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                background: '#38bdf8',
                                display: 'inline-block',
                            }}
                        />
                        <Text type="secondary">Physical Class</Text>
                    </Space>
                </Col>
                <Col>
                    <Title level={4} style={{ margin: 0 }}>
                        379,237
                    </Title>
                    <Space size="small">
                        <span
                            style={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                background: '#a78bfa',
                                display: 'inline-block',
                            }}
                        />
                        <Text type="secondary">Online Class</Text>
                    </Space>
                </Col>
            </Row>

            <div className="flex-1">
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={data}>
                        <CartesianGrid vertical={false} strokeDasharray="3 3" />
                        <XAxis
                            dataKey="name"
                            tick={{ fontSize: 12 }}
                            axisLine={false}
                            tickLine={false}
                        />
                        <YAxis hide />
                        <Tooltip
                            content={<CustomTooltip />}
                            cursor={{ fill: 'transparent' }}
                        />
                        <Bar
                            dataKey="value"
                            fill="#8b5cf6"
                            radius={[8, 8, 0, 0]}
                            barSize={40}
                        />
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </div>
    );
};

export default PopularPackageChart;
