import { ArrowUpOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, ConfigProvider, Select, Table, Typography } from 'antd';
import Search from 'antd/es/transfer/search';
import { title } from 'process';
import React from 'react';
import Dashboard<PERSON>hart from './dashbodar<PERSON>hart';
import PopularPackageChart from './popularPackageChart';
const { Text, Title } = Typography;
const Dashboard = () => {
    const mostVisitedColumns = [
        {
            title: 'Name',
            dataIndex: 'name',
        },
        {
            align: 'center',
            title: 'Monthly Visit',
            dataIndex: 'monthlyVisit',
        },
        {
            align: 'center',
            title: 'Last Visit',
            dataIndex: 'lastVisit',
        },
    ];
    const mostVisitedData = [
        {
            key: 0,
            name: '<PERSON><PERSON><PERSON>',
            monthlyVisit: '20(+5)',
            lastVisit: '2025-07-09',
        },
        {
            key: 1,
            name: '<PERSON><PERSON>',
            monthlyVisit: '22(+2)',
            lastVisit: '2025-07-08',
        },
        {
            key: 2,
            name: '<PERSON>',
            monthlyVisit: '15(+10)',
            lastVisit: '2025-07-06',
        },
        {
            key: 3,
            name: '<PERSON><PERSON><PERSON>',
            monthlyVisit: '10(+2)',
            lastVisit: '2025-07-05',
        },
        {
            key: 4,
            name: 'Rahul',
            monthlyVisit: '5(+1)',
            lastVisit: '2025-07-05',
        },
        {
            key: 5,
            name: 'Shivam Gupta',
            monthlyVisit: '25(+5)',
            lastVisit: '2025-07-05',
        },
        {
            key: 6,
            name: 'Shivam Dwivedi',
            monthlyVisit: '45(+10)',
            lastVisit: '2025-07-05',
        },
        {
            key: 7,
            name: 'Navneet',
            monthlyVisit: '10(+2)',
            lastVisit: '2025-07-05',
        },
        {
            key: 8,
            name: 'Abhinav Rana',
            monthlyVisit: '10(+2)',
            lastVisit: '2025-07-04',
        },
        {
            key: 9,
            name: 'Deepak Panghal',
            monthlyVisit: '5',
            lastVisit: '2025-07-04',
        },
    ];

    const popularPackagesColumns = [
        {
            title: 'ID',
            dataIndex: 'id',
        },
        {
            title: 'PACKAGE NAME',
            dataIndex: 'packageName',
        },
        {
            title: 'TIMES PURCHASED',
            dataIndex: 'totalSold',
            align: 'center',
        },
        {
            title: 'ACTION',
            dataIndex: '',
            align: 'center',
            width: '100px',
            render: () => {
                return (
                    <div className="flex justify-center gap-2">
                        <img
                            src="/icons/common/edit.svg"
                            alt="edit"
                            className="h-[20px] cursor-pointer"
                        />
                        <img
                            src="/icons/common/delete.svg"
                            alt="delete"
                            className="h-[20px] cursor-pointer"
                        />
                    </div>
                );
            },
        },
    ];
    const popularPackagesData = [
        {
            key: 0,
            id: 1,
            packageName: 'Basic Package',
            totalSold: 100,
        },
        {
            key: 1,
            id: 2,
            packageName: 'Premium Package',
            totalSold: 50,
        },
        {
            key: 2,
            id: 3,
            packageName: 'Gold Package',
            totalSold: 20,
        },
        {
            key: 3,
            id: 4,
            packageName: 'Platinum Package',
            totalSold: 10,
        },
        {
            key: 4,
            id: 5,
            packageName: 'Diamond Package',
            totalSold: 5,
        },
        {
            key: 5,
            id: 6,
            packageName: 'Ruby Package',
            totalSold: 2,
        },
        {
            key: 6,
            id: 7,
            packageName: 'Sapphire Package',
            totalSold: 1,
        },
        {
            key: 7,
            id: 8,
            packageName: 'Emerald Package',
            totalSold: 1,
        },
        {
            key: 8,
            id: 9,
            packageName: 'Onyx Package',
            totalSold: 1,
        },
        {
            key: 9,
            id: 10,
            packageName: 'Opal Package',
            totalSold: 1,
        },
    ];
    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Card: {
                        bodyPadding: 12,
                    },
                },
            }}
        >
            <Title className=" text-[#1a3353]" level={4}>
                Dashboard
            </Title>
            <div className="flex justify-between gap-8">
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Check-Ins
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            2,510
                        </Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw]">
                                +18
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>This month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Customers
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            6,982
                        </Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw]">
                                +50
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>Added this month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Earnings
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            ₹8,310
                        </Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw]">
                                +15.7%
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>Compare to last month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        Total Packages Sold
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            4,320
                        </Title>
                        <div className="flex items-center gap-2">
                            <Text className="text-[1.2vw]">
                                +5.7
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>Compare to last month</Text>
                </Card>
                <Card className="flex-1">
                    <Title level={5} className="mb-2 text-[1.1vw]">
                        New Customers
                    </Title>
                    <div className="mt-4 flex items-center gap-8">
                        <Title className="text-[1.6vw] font-semibold">
                            7,045
                        </Title>
                        <div className="flex items-center">
                            <Text className="text-[1.2vw]">
                                <span className="text-green-500">+46</span>
                                <ArrowUpOutlined />
                            </Text>
                        </div>
                    </div>
                    <Text>This month</Text>
                </Card>
            </div>
            <div className="mt-8 flex items-center justify-end gap-8">
                <Button className="bg-[#8143d1] text-white">Add Sales</Button>
                <Button>Share Pass</Button>
                <Button>Add Customer</Button>
                <Button>Add Enquiry</Button>
            </div>
            <div className="mt-8 flex gap-4">
                <div className="w-2/3 rounded-xl bg-gray-100 p-8">
                    <DashboardChart />
                </div>
                <div className="w-1/3 rounded-xl border-2 p-4">
                    <div className="mb-4 flex items-center justify-between">
                        <Title level={3}>Most Active Customers</Title>
                        <Select
                            className="w-[150px]"
                            defaultValue="currentMonth"
                            options={[
                                {
                                    label: 'This Month',
                                    value: 'currentMonth',
                                },
                                {
                                    label: 'Last Month',
                                    value: 'lastMonth',
                                },
                            ]}
                        />
                    </div>
                    <Search allowClear placeholder="Search" />
                    <Table
                        columns={mostVisitedColumns}
                        dataSource={mostVisitedData}
                        pagination={false}
                        scroll={{ y: 55 * 6 }}
                    />
                </div>
            </div>
            <div className="mt-8 flex gap-4">
                <div className="w-3/5 rounded-xl border-2 p-4">
                    <Title level={3}>Popular Packages</Title>

                    <div className="my-4 flex items-center justify-between">
                        <div className="w-[300px]">
                            <Search allowClear placeholder="Search" />
                        </div>
                        <Select
                            className="w-[150px]"
                            defaultValue="currentMonth"
                            options={[
                                {
                                    label: 'This Month',
                                    value: 'currentMonth',
                                },
                                {
                                    label: 'Last Month',
                                    value: 'lastMonth',
                                },
                            ]}
                        />
                    </div>
                    <Table
                        columns={popularPackagesColumns}
                        dataSource={popularPackagesData}
                        pagination={false}
                        scroll={{ y: 55 * 6 }}
                    />
                </div>
                <div className="w-2/5 rounded-xl bg-gray-100 p-8">
                    <PopularPackageChart />
                </div>
            </div>
        </ConfigProvider>
    );
};

export default Dashboard;
