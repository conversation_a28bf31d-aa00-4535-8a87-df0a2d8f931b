import React, { useEffect, useState } from 'react';
import {
    CaretRightOutlined,
    FormOutlined,
    InfoCircleFilled,
} from '@ant-design/icons';
import {
    Collapse,
    theme,
    Checkbox,
    Button,
    Progress,
    ConfigProvider,
} from 'antd';
import AddStaffModal from '~/components/modals/add-staff';
import CreateStaffModal from '~/components/modals/create-staff';
import { useLocation } from 'wouter';
import Title from 'antd/es/typography/Title';
import ScheduleModal from '~/components/staff/scheduleModal';
import { CreateStaff, GetStaffList } from '~/redux/actions/staff-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { FacilitiesList } from '~/redux/actions/facility-action';
import BookingModal from '../appointment/booking-modal';
import AddClientModal from '../customers/add-client-modal';
import { log } from 'console';

const SetupChecklist = () => {
    const { token } = theme.useToken();
    const dispatch = useAppDispatch();
    const [location, setLocation] = useLocation();
    const panelStyle = {
        marginBottom: 24,
        background: token.colorFillAlter,
        borderRadius: token.borderRadiusLG,
        border: 'none',
    };

    const [id, setId] = useState(null);
    const [isBookingModal, setIsBookingModal] = useState<boolean>(false);
    const [currentTab, setCurrentTab] = useState<string>(''); // Manage the current tab state

    const showBookClassModal = (value: string) => {
        setCurrentTab(value);
        setIsBookingModal(true);
    };

    const showBookModal = (value: string) => {
        setCurrentTab(value);
        setIsBookingModal(true);
    };

    const handleCloseBookClass = () => {
        setIsBookingModal(false); // Close modal
    };

    const [isClientModal, setIsClientModal] = useState<boolean>(false);

    const showClientModal = () => {
        setIsClientModal(true);
    };
    const handleCloseClient = () => {
        setIsClientModal(false);
    };

    // ----------------modal handling starts-------------
    const [addStaffModal, setAddStaffModal] = useState(false);
    const showAddStaffModal = () => {
        setAddStaffModal(true);
    };
    const hideAddStaffModal = () => {
        setAddStaffModal(false);
    };

    useEffect(() => {
        dispatch(FacilitiesList({})).unwrap();
    }, []);

    const [createStaffModal, setCreateStaffModal] = useState(false);

    const showCreateStaffModal = () => {
        setCreateStaffModal(true);
    };

    const hideCreateStaffModal = () => {
        setCreateStaffModal(false);
    };

    const [openScheduleModal, setOpenScheduleModal] = useState<boolean>(false);
    const openScheduleUpdateModal = () => {
        setOpenScheduleModal(true);
    };

    const closeScheduleUpdateModal = () => {
        setOpenScheduleModal(false);
    };
    // ----------------modal handling closed-------------

    const onChange = (e: any) => {
        console.log(`checked = ${e.target.checked}`);
    };

    function saveStaffModal(fields: any, formRef: any) {
        console.log('Fields', fields);
        dispatch(CreateStaff({ fields }))
            .unwrap()
            .then((res: any) => {
                console.log('Res-------------', res);
                if (res?.status === 200 || res?.status === 201) {
                    hideAddStaffModal();
                    formRef.resetFields();
                    setLocation('/staffs');
                    dispatch(GetStaffList({})).unwrap();
                }
            });
    }

    function saveAnotherStaff(fields: any, formRef: any) {
        console.log('Fields', fields);
        dispatch(CreateStaff({ fields }))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    formRef.resetFields();
                }
            });
    }

    const GettingStarted = (
        <div>
            <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
                Support Article
            </p>
            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Find Hop.Wellness Support
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
            <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        href="/facilities-location"
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add details about your business
                        </p>
                    </Checkbox>
                </div>
                <div onClick={() => setLocation('/facilities-location')}>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            {/* <div className="flex flex-row items-center justify-between  pt-4 lg:pe-14">
                
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 bg-purpleLight rounded-2xl sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Retake Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}></Checkbox>
                    <p className="checkbox-text text-xl lg:ms-2 @sm:me-2 text-[#455560]">
                        Create your setup goal
                    </p>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
        </div>
    );

    const SetupStaff = (
        <div>
            <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
                Support Article
            </p>
            <div className="flex flex-row items-center justify-between border-b pb-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={showAddStaffModal}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add staff
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            {/* <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        // onClick={showCreateStaffModal}
                        onClick={() => setLocation('/Staff-onboard')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Create staff login
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
            <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/staffs')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Set up staff specialization
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            {/* <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl lg:ms-2 @sm:me-2 text-[#455560]">
                            Set up hourly pay rates
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Review permission groups
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
        </div>
    );

    // const SetupClasses = (
    //     <div>
    //         <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
    //             Support Article
    //         </p>
    //         <div className="flex flex-row items-center justify-between border-b-1 pb-4 lg:pe-14">
    //             <div className="flex items-center lg:flex-row">
    //                 <Button
    //                     // onClick={() => setLocation('/add-class/:0')}
    //                     onClick={() => {
    //                         setId('0'); // Set the id to '0' for adding a class
    //                         setLocation('/add-class/0'); // Update the URL or route
    //                     }}
    //                     className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
    //                     type="primary"
    //                 >
    //                     Start Task
    //                 </Button>

    //                 <Checkbox disabled className="me-4" onChange={onChange}>
    //                     <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
    //                         Add a class
    //                     </p>
    //                 </Checkbox>
    //             </div>
    //             <div>
    //                 <FormOutlined className="cursor-pointer text-3xl" />
    //             </div>
    //         </div>
    //         <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
    //             <div className="flex items-center lg:flex-row">
    //                 <Button
    //                     onClick={showBookClassModal}
    //                     className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
    //                     type="primary"
    //                 >
    //                     Start Task
    //                 </Button>

    //                 <Checkbox disabled className="me-4" onChange={onChange}>
    //                     <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
    //                         Schedule your classes
    //                     </p>
    //                 </Checkbox>
    //             </div>
    //             <div>
    //                 <FormOutlined className="cursor-pointer text-3xl" />
    //             </div>
    //         </div>
    //         <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
    //             <div className="flex items-center lg:flex-row">
    //                 <Button
    //                     onClick={() => setLocation('/create-pricing/0')}
    //                     className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
    //                     type="primary"
    //                 >
    //                     Start Task
    //                 </Button>

    //                 <Checkbox disabled className="me-4" onChange={onChange}>
    //                     <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
    //                         Add pricing
    //                     </p>
    //                 </Checkbox>
    //             </div>
    //             <div>
    //                 <FormOutlined className="cursor-pointer text-3xl" />
    //             </div>
    //         </div>

    //         {/* <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
    //             <div className="flex items-center lg:flex-row">
    //                 <Button
    //                     className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
    //                     type="primary"
    //                 >
    //                     Retake Task
    //                 </Button>

    //                 <Checkbox disabled className="me-4" onChange={onChange}>
    //                     <p className="checkbox-text text-xl lg:ms-2 @sm:me-2 text-[#455560]">
    //                         Set booking windows for classes
    //                     </p>
    //                 </Checkbox>
    //             </div>
    //             <div>
    //                 <FormOutlined className="cursor-pointer text-3xl" />
    //             </div>
    //         </div> */}
    //     </div>
    // );

    const SetupAppointments = (
        <div>
            <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
                Support Article
            </p>
            <div className="flex flex-row items-center justify-between border-b-1 pb-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/appointments-types')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Set up your appointments
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            {/* <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={openScheduleUpdateModal}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add staff schedules
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
            {/* 
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Retake Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl lg:ms-2 @sm:me-2 text-[#455560]">
                            Set booking windows for appointments
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
        </div>
    );

    const SetupCourses = (
        <div>
            <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
                Support Article
            </p>
            <div className="flex flex-row items-center justify-between border-b-1 pb-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() =>
                            setLocation(
                                'add-appointment-service/0?serviceType=classes'
                            )
                        }
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add a course service category
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('courses')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add & schedule a course
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/create-pricing/0')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add pricing
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 py-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add additional courses
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        StartTask
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl lg:ms-2 @sm:me-2 text-[#455560]">
                            Set schedule windows for Courses
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
        </div>
    );
    const SetupBooking = (
        <div>
            <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
                Support Article
            </p>
            <div className="flex flex-row items-center justify-between border-b-1 pb-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/booking-types')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Setup your appointments
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>

            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/create-pricing/0')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add pricing
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
        </div>
    );

    const AdditionalPricing = (
        <div>
            <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
                Support Article
            </p>
            <div className="flex flex-row items-center justify-between border-b-1 pb-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/create-pricing/0')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox
                        disabled
                        disabled
                        className="me-4"
                        onChange={onChange}
                    >
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add pricing
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>

            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Set up gift cards
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/point-of-sales')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Sell pricing package on POS
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
        </div>
    );
    const EverydayUse = (
        <div>
            <p className="mb-2 text-end text-xl font-semibold text-[#1A3353]">
                Support Article
            </p>
            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Turn on and customize your auto emails
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}

            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={showClientModal}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add a client
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/customers')}
                        className="me-3  rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Look up client
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/user-profile/0')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Manage client information
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => setLocation('/point-of-sales')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Sell services
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => showBookModal('bookings')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add and schedule bookings
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        onClick={() => showBookModal('personalAppointment')}
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Add and schedule appointments
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            {/* <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Enroll clients in a course
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Learn about reporting
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div>
            <div className="flex flex-row items-center justify-between border-b-1 pb-2 pt-4 lg:pe-14">
                <div className="flex items-center lg:flex-row">
                    <Button
                        className="me-3 rounded-2xl  bg-purpleLight sm:w-[120px] @sm:p-3"
                        type="primary"
                    >
                        Start Task
                    </Button>

                    <Checkbox disabled className="me-4" onChange={onChange}>
                        <p className="checkbox-text text-xl text-[#455560] lg:ms-2 @sm:me-2">
                            Marketing suite
                        </p>
                    </Checkbox>
                </div>
                <div>
                    <FormOutlined className="cursor-pointer text-3xl" />
                </div>
            </div> */}
        </div>
    );
    const getItems = (panelStyle: any) => [
        {
            key: '1',
            label: <p className="text-2xl font-semibold">Getting Started</p>,
            children: <p>{GettingStarted}</p>,
            style: panelStyle,
        },
        {
            key: '4',
            label: <p className="text-2xl font-semibold">Setup Appointments</p>,
            children: <p>{SetupAppointments}</p>,
            style: panelStyle,
        },
        {
            key: '2',
            label: <p className="text-2xl font-semibold">Setup Staff</p>,
            children: <p>{SetupStaff}</p>,
            style: panelStyle,
        },
        // {
        //     key: '3',
        //     label: <p className="text-2xl font-semibold">Setup Classes</p>,
        //     children: <p>{SetupClasses}</p>,
        //     style: panelStyle,
        // },

        // {
        //     key: '2',
        //     label: <p className="text-2xl font-semibold">Setup Staff</p>,
        //     children: <p>{SetupStaff}</p>,
        //     style: panelStyle,
        // },
        // {
        //     key: '3',
        //     label: <p className="text-2xl font-semibold">Setup Classes</p>,
        //     children: <p>{SetupClasses}</p>,
        //     style: panelStyle,
        // },

        // {
        //     key: '2',
        //     label: <p className="text-2xl font-semibold">Setup Staff</p>,
        //     children: <p>{SetupStaff}</p>,
        //     style: panelStyle,
        // },
        // {
        //     key: '3',
        //     label: <p className="text-2xl font-semibold">Setup Classes</p>,
        //     children: <p>{SetupClasses}</p>,
        //     style: panelStyle,
        // },

        // {
        //     key: '5',
        //     label: <p className="text-2xl font-semibold">Setup Courses</p>,
        //     children: <p>{SetupCourses}</p>,
        //     style: panelStyle,
        // },
        {
            key: '6',
            label: <p className="text-2xl font-semibold">Setup Bookings</p>,
            children: <p>{SetupBooking}</p>,
            style: panelStyle,
        },
        {
            key: '7',
            label: <p className="text-2xl font-semibold">Setup Pricing</p>,
            children: <p>{AdditionalPricing}</p>,
            style: panelStyle,
        },
        {
            key: '8',
            label: <p className="text-2xl font-semibold">Everyday Use</p>,
            children: <p>{EverydayUse}</p>,
            style: panelStyle,
        },
    ];

    const list = (
        <ol className="list-disc ps-12">
            <li>Admin</li>
            <li>Trainer</li>
            <li>User</li>
        </ol>
    );
    const items = [
        {
            key: '1',
            label: (
                <p className="text-xl text-primary">
                    Who else can access this checklist?
                </p>
            ),
            children: list,
        },
    ];

    return (
        <>
            <Title className=" text-[#1a3353]" level={4}>
                Setup Checklist
            </Title>
            <p className="pb-10 pt-8 text-[#455560] lg:w-[45%] lg:text-[16px]">
                Welcome to Hop.Wellness Use this checklist to set up your site.
                When you finish a task, check it off
            </p>
            <div className=" flex gap-5 lg:flex-row @sm:flex-col">
                <div className="lg:w-[55%]">
                    <ConfigProvider
                        theme={{
                            components: {
                                Collapse: {
                                    contentBg: '#fff',
                                    headerBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Collapse
                            bordered={false}
                            data-id="checkboxCollapse"
                            defaultActiveKey={[
                                '1',
                                '2',
                                '3',
                                '4',
                                '5',
                                '6',
                                '7',
                                '8',
                                '9',
                            ]}
                            expandIcon={({ isActive }) => (
                                <CaretRightOutlined
                                    rotate={isActive ? 90 : 0}
                                />
                            )}
                            style={{
                                background: token.colorBgContainer,
                            }}
                            items={getItems(panelStyle)}
                        />
                    </ConfigProvider>
                </div>
                <div className=" flex flex-col gap-10 lg:w-[18%]">
                    <div className="flex flex-col items-center justify-center rounded-lg border border-[#E6EBF1] py-5 ">
                        <p className="pb-5 text-center text-xl font-semibold text-[#1A3353]">
                            Your Progress
                        </p>
                        <ConfigProvider
                            theme={{
                                components: {},
                            }}
                        >
                            <Progress type="dashboard" percent={75} />
                        </ConfigProvider>
                    </div>
                    <div className="flex hidden flex-col items-center justify-center rounded-lg border border-[#E6EBF1] px-4 py-5 ">
                        <p className="pb-5 text-center text-xl font-semibold text-[#1A3353]">
                            Site Setup Goal &nbsp;
                            <InfoCircleFilled className="text-2xl text-[#D9D9D9]" />
                        </p>
                        <div className="w-[45px] bg-[#D9D9D9] py-1">
                            <p className="text-center text-xl font-semibold">
                                Oct
                            </p>
                        </div>
                        <div className="w-[45px] border border-[#E6EBF1] py-2 shadow-lg">
                            <p className="text-center text-xl font-semibold">
                                26
                            </p>
                        </div>
                        <p className="cursor-pointer pb-5 pt-2 text-center text-xl font-semibold">
                            Edit
                        </p>
                        <div className="  w-full rounded-lg bg-[#EFE1E0] px-6 py-3 font-normal text-[#1A3353] ">
                            <p className="text-center lg:text-lg ">
                                Your site setup goal has passed
                            </p>
                        </div>
                    </div>
                    <div className="flex flex-col items-center justify-center ">
                        <Collapse items={items} bordered={false} />
                    </div>
                </div>
            </div>
            {/* ------------------------modals---------------------- */}
            <AddStaffModal
                visible={addStaffModal}
                onClose={hideAddStaffModal}
                onSave={saveStaffModal}
                onSaveAnother={saveAnotherStaff}
            />
            {createStaffModal && (
                <CreateStaffModal
                    visible={createStaffModal}
                    onClose={hideCreateStaffModal}
                />
            )}

            {isBookingModal && (
                <BookingModal
                    visible={isBookingModal}
                    onClose={handleCloseBookClass}
                    tabValue={currentTab}
                />
            )}
            {openScheduleModal && (
                <ScheduleModal
                    onClose={() => closeScheduleUpdateModal()}
                    visible={openScheduleModal}
                />
            )}

            {isClientModal && (
                <AddClientModal
                    open={isClientModal}
                    onClose={handleCloseClient}
                />
            )}
        </>
    );
};

export default SetupChecklist;
