import { Checkbox, DatePicker, Form, Input, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { customerListV1 } from '~/redux/actions/customer-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import dayjs from 'dayjs';
import { InfoCircleOutlined } from '@ant-design/icons';
const FormClientDetails = ({
    facilityId,
    onClientSelect,
    clientData,
    isEdit,
}: any) => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    const [clientOptions, setClientOptions] = useState([]);
    const [selectedClient, setSelectedClient] = useState<any>(null); // Store selected client data
    const [imageUrl, setImageUrl] = useState<any>('/assets/Profile_icon.png');
    const [ageWarning, setAgeWarning] = useState(false);
    const fetchClientList = async (searchText = '', page = 1) => {
        if (!facilityId || facilityId.length === 0) return [];

        try {
            const response = await dispatch(
                customerListV1({
                    page,
                    pageSize: 10,
                    facilityIds: Array.isArray(facilityId)
                        ? facilityId
                        : [facilityId],
                    search: searchText,
                    isActive: true,
                })
            ).unwrap();

            return (
                response?.data?.data?.list?.map((item: any) => ({
                    value: item.userId,
                    label: `${item?.firstName || ''} ${
                        item?.lastName || ''
                    }`.trim(),
                    id: item._id,
                    phone: item?.mobile || '',
                    email: item?.email || '',
                    age: item?.age || '',
                    unpaidSessions: item?.unpaidSessions || '',
                    proficiencyLevel: item.proficiencyLevel || '',
                    lastVisited: item?.lastVisited || '',
                    photo: item.photo || undefined,
                })) || []
            );
        } catch (error) {
            console.error('Error fetching client list:', error);
            return [];
        }
    };
    useEffect(() => {
        if (clientData?.clientId) {
            dispatch(
                customerListV1({
                    page: 1,
                    pageSize: 1,
                    clientId: clientData.clientId,
                    isActive: true,
                })
            )
                .unwrap()
                .then((response: any) => {
                    const client = response?.data?.data?.list?.find(
                        (item: any) => item.userId === clientData.clientId
                    );
                    if (client) {
                        setSelectedClient({
                            value: client.userId,
                            label: `${client.firstName || ''} ${
                                client.lastName || ''
                            }`.trim(),
                            id: client._id,
                            phone: client.mobile || '',
                            email: client.email || '',
                            age: client.age || '',
                            unpaidSessions: client.unpaidSessions || '',
                            proficiencyLevel: client.proficiencyLevel || '',
                            lastVisited: client.lastVisited || '',
                            photo: client.photo || undefined,
                        });

                        form.setFieldsValue({
                            client: `${client.firstName || ''} ${
                                client.lastName || ''
                            }`.trim(),
                            phone: client.mobile || '',
                            email: client.email || '',
                            Age: client.age
                                ? dayjs()
                                      .diff(dayjs(client.age), 'year')
                                      .toString()
                                : '',
                            unpaidSessions: client.unpaidSessions || '',
                            ProficiencyLevel: client.proficiencyLevel || '',
                            lastVisited: client.lastVisited
                                ? dayjs(client.lastVisited).format('DD/MM/YYYY')
                                : null,
                        });
                    }
                })
                .catch((error: any) =>
                    console.error('Error fetching client details:', error)
                );
        }
    }, [clientData]);

    useEffect(() => {
        if (facilityId) {
            fetchClientList().then(setClientOptions);
            form.resetFields();
            setSelectedClient(null);
        }
    }, [facilityId]);
    const calculateAge = (dob: string) => {
        if (!dob) return;

        const birthDate = dayjs(dob);
        const today = dayjs();
        const age = today.diff(birthDate, 'year'); // Calculate age in years

        form.setFieldsValue({ Age: age.toString() }); // Update the Age field
        if (age < 18) {
            setAgeWarning(true);
        }
    };
    useEffect(() => {
        if (selectedClient) {
            if (selectedClient.photo) {
                setImageUrl(selectedClient.photo);
            } else {
                setImageUrl('/assets/Profile_icon.png');
            }
            form.setFieldsValue({
                //  client:  selectedClient.userId,
                phone: selectedClient.phone
                    ? selectedClient.phone
                    : selectedClient.mobile,
                email: selectedClient.email || '',
                Age: selectedClient.age ? dayjs(selectedClient.age) : null,
                unpaidSessions: selectedClient.unpaidSessions || '',
                ProficiencyLevel: selectedClient.proficiencyLevel || '',
                lastVisited: selectedClient.lastVisited
                    ? dayjs(selectedClient.lastVisited).format('DD/MM/YYYY')
                    : null,
            });
            if (selectedClient.mobile) {
                form.setFieldsValue({
                    client: selectedClient.userId,
                });
            }
            if (selectedClient.age) {
                calculateAge(selectedClient.age);
            }
            onClientSelect && onClientSelect(selectedClient);
        }
    }, [selectedClient]); // Update form when a client is selected

    return (
        <div className="flex flex-col items-center pe-8">
            <img
                src={imageUrl}
                className="h-36 w-36 rounded-full object-cover"
            />
            <Form
                form={form}
                className="flex w-[100%] flex-col lg:pt-10"
                name="schedule-form"
                layout="horizontal"
                variant="borderless"
                size="large"
                disabled={isEdit}
            >
                <Form.Item
                    label={<p className=" text-left">Client</p>}
                    name="client"
                    rules={[
                        { required: true, message: 'Please select client' },
                    ]}
                    className=""
                >
                    <InfiniteScrollSelect
                        className="border-b border-[#d1d5db]"
                        fetchOptions={fetchClientList}
                        // options={clientOptions}
                        onChange={(value, option) => {
                            setAgeWarning(false);
                            form.setFieldsValue({ client: value });
                            setSelectedClient(option); // Update state with selected client data
                        }}
                        placeholder="Select Client"
                    />
                </Form.Item>

                <Form.Item
                    label={<p className="text-left ">Phone</p>}
                    name="phone"
                    className=""
                >
                    <Input
                        placeholder="Phone Number"
                        disabled
                        className="border-b"
                        style={{
                            borderBottom: '0.5px solid #d1d5db',
                            borderRadius: '0px',
                            backgroundColor: '#f5f5f5',
                            color: '#a0a0a0',
                            cursor: 'not-allowed',
                        }}
                    />
                </Form.Item>

                <Form.Item
                    label={<p className="text-left ">Email</p>}
                    name="email"
                >
                    <Input
                        placeholder="Email"
                        type="email"
                        readOnly
                        style={{
                            borderBottom: '0.5px solid #d1d5db',
                            borderRadius: '0px',
                            backgroundColor: '#f5f5f5',
                            color: '#a0a0a0',
                            cursor: 'not-allowed',
                        }}
                    />
                </Form.Item>

                <Form.Item label={<p className="text-left ">Age</p>} name="Age">
                    <Input
                        placeholder="Age"
                        readOnly
                        style={{
                            borderBottom: '0.5px solid #d1d5db',
                            borderRadius: '0px',
                            backgroundColor: '#f5f5f5',
                            color: '#a0a0a0',
                            cursor: 'not-allowed',
                        }}
                    />
                </Form.Item>
                {ageWarning && (
                    <>
                        <div className="flex flex-row items-center">
                            <div className=" @sm:flex-col @lg:w-[20%] @xl:w-[20%] @2xl:w-[20%]"></div>
                            <div className=" -translate-y-3 text-lg font-semibold text-red-500">
                                <InfoCircleOutlined className="text-red-500" />
                                &nbsp; Client is under 18 years old.
                            </div>
                        </div>
                    </>
                )}
                <Form.Item
                    label={<p className="text-left ">Last Visited</p>}
                    name="lastVisited"
                >
                    <Input
                        placeholder="Last Visited"
                        readOnly
                        style={{
                            borderBottom: '0.5px solid #d1d5db',
                            borderRadius: '0px',
                            backgroundColor: '#f5f5f5',
                            color: '#a0a0a0',
                            cursor: 'not-allowed',
                        }}
                    />
                    {/* <DatePicker format="DD/MM/YYYY" disabled className="w-[80%] border-b border-gray-300" /> */}
                </Form.Item>

                <Form.Item
                    label={
                        <p className="text-left">
                            Proficiency <br /> Level
                        </p>
                    }
                    // className="mt-7"
                    name="ProficiencyLevel"
                >
                    <Input
                        placeholder="Proficiency Level"
                        readOnly
                        style={{
                            borderBottom: '0.5px solid #d1d5db',
                            borderRadius: '0px',
                            backgroundColor: '#f5f5f5',
                            color: '#a0a0a0',
                            cursor: 'not-allowed',
                        }}
                    />
                </Form.Item>
                {/* 
                <Form.Item label={<p className="text-left ">Unpaid Sessions</p>} name="unpaidSessions">
                    <Input placeholder="No. of unpaid sessions" readOnly className="border-b border-gray-300" />
                </Form.Item> */}
            </Form>
        </div>
    );
};

export default FormClientDetails;
