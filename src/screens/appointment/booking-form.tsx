import React, { useEffect, useMemo, useState } from 'react';
import {
    Select,
    DatePicker,
    TimePicker,
    Input,
    Checkbox,
    Form,
    Button,
    FormProps,
    ConfigProvider,
} from 'antd';
// import { Dayjs } from 'dayjs';
import { useSelector } from 'react-redux';
import { ClassType, RoleType } from '~/types/enums';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { capitalizeFirstLetter } from '~/components/common/function';
import { GetFacilityListByStaffId } from '~/redux/actions/facility-action';
import {
    TrainerListing,
    PricingListingByUserAndType,
    PricingListingByUserAndSubType,
    serviceBypricingIdV1,
} from '~/redux/actions/appointment-action';
import { ClientsDetails, CustomerList } from '~/redux/actions/customer-action';
import { useDebounce } from '~/hooks/useDebounce';
import dayjs from 'dayjs';
import TextArea from 'antd/es/input/TextArea';
import {
    ServiceCategoryListByPackageId,
    activeServiceCategoyListv1,
} from '~/redux/actions/serviceCategoryAction';
import {
    roomListingByServiceCategory,
    roomListingByScheduling,
} from '~/redux/actions/room-action';
import {
    BookedSchedulingDetails,
    CreateBookScheduling,
    UpdateBookedScheduling,
    BookedCalendarData,
} from '~/redux/actions/scheduling-action';
import FormClientDetails from './form-client-detail';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';

const { Option, OptGroup } = Select;
interface BookingFormProps {
    visible?: boolean;
    onClose?: any;
    scheduleId?: string;
    isEdit?: boolean;
    clientId?: string;
    purchaseId?: string;
    facilityId?: string;
    slotSelectedInfo?: any;
}

const BookingForm: React.FC<BookingFormProps> = ({
    visible,
    onClose,
    scheduleId,
    isEdit,
    clientId,
    purchaseId,
    facilityId,
    slotSelectedInfo,
}) => {
    // Api start
    const { role, user } = useSelector((state: any) => state.auth_store);

    const [form] = Form.useForm();

    const [selectedLocation, setSelectedLocation] = useState(
        facilityId ? facilityId : null
    );
    const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(dayjs());
    const [startTime, setStartTime] = useState<dayjs.Dayjs | null>(null);
    const [endTime, setEndTime] = useState<dayjs.Dayjs | null>(null);
    const [startTimeMinuteStep, setStartTimeMinuteStep] = useState<any>(1);
    const [appointmentTypeOption, setAppointmentTypeOption] = useState<any>([]);
    const [clientData, setClientData] = useState<any>();
    const [durationOption, setDurationOption] = useState<any>([]);
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [
        submitCheckInLoader,
        startSubmitCheckInLoader,
        endSubmitCheckInLoader,
    ] = useLoader();
    const [remainingSessions, setRemainingSessions] = useState('');
    const [serviceCategoryId, setServiceCategoryId] = useState<any>(null);
    const [subCategoryId, setSubcategoryId] = useState<any>(null);
    const [roomData, setRoomData] = useState<any>([]);
    const [selectedServiceLabel, setSelectedServiceLabel] = useState('');
    const [selectedRoomReason, setSelectedRoomReason] = useState<string | null>(
        null
    );
    const [selectedRoomAvailability, setSelectedRoomAvailability] = useState<
        boolean | null
    >(null);

    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        roomList: state.room_store.roomList,
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        trainerListForForms: state.appointment_store.trainerListForForms,
        pricingList: state.appointment_store.pricingList,
        customerListForForms: state.customer_store.customerListForForms,
        ServiceCategoryListByPackageId:
            state.service_category_store.ServiceCategoryListByPackageId,
        activeServiceCategoyListv1:
            state.service_category_store.ActiveServiceCategoryListV1,
        pricingListUserAndSubType:
            state.appointment_store.pricingListUserAndSubType,
    }));

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const [loader, startLoader, endLoader] = useLoader();

    useEffect(() => {
        if (slotSelectedInfo) {
            setStartTime(dayjs(slotSelectedInfo.startDate));
            setStartDate(dayjs(slotSelectedInfo.startDate));
        }
    }, [slotSelectedInfo]);

    useEffect(() => {
        if (role === RoleType.TRAINER) {
            dispatch(GetFacilityListByStaffId({ staffId: user._id })).then(
                (res: any) => {
                    form.setFieldsValue({
                        location: res?.payload?.data?.data?.[0]?._id,
                    });
                }
            );
        }
    }, [visible]);

    const transformServiceData = (apiData: any) => {
        return apiData.map((service: any) => ({
            category: service.name,
            options: service.appointmentType.map((appointment: any) => ({
                label: `${appointment.name} - ${appointment.durationInMinutes} mins`,
                value: JSON.stringify({
                    serviceId: service._id,
                    appointmentId: appointment._id,
                    // duration: appointment.durationInMinutes,
                }),
            })),
        }));
    };

    useEffect(() => {
        if (role !== RoleType.TRAINER && selectedLocation) {
            dispatch(
                TrainerListing({ facilityId: selectedLocation, isActive: true })
            );
            if (facilityId) form.setFieldValue('location', facilityId);
        }
        if (purchaseId) form.setFieldsValue({ package: purchaseId });
    }, [selectedLocation, facilityId]);
    const fetchPackageListing = async (searchText = '', page = 1) => {
        if (clientData && serviceCategoryId && subCategoryId) {
            const payload = {
                classType: ClassType.BOOKING,
                clientUserId: clientData.value
                    ? clientData.value
                    : clientData.userId,
                serviceCategoryId: serviceCategoryId,
                subTypeId: subCategoryId,
                pageSize: 10,
                page,
                search: searchText,
                isNewBooking: true,
            };
            const response = await dispatch(
                PricingListingByUserAndSubType(payload)
            ).unwrap();
            return (
                response?.data?.data?.map((item: any) => ({
                    value: item.purchaseId,
                    label: `${item.packageName}`.trim(),
                })) || []
            );
        }
    };

    useEffect(() => {
        const fetchSchedulingDetails = async () => {
            if (!scheduleId) return;

            try {
                // Fetch scheduling details
                const res = await dispatch(
                    BookedSchedulingDetails({ scheduleId })
                ).unwrap();
                const scheduleData = res?.data?.data;

                if (!scheduleData) return;

                const {
                    facilityId,
                    clientId,
                    clientName,
                    serviceCategoryId,
                    subTypeId,
                    subTypeDuration,
                    date,
                    from,
                    duration,
                    purchaseId,
                    roomId,
                    notes,
                } = scheduleData;
                // Set initial state
                setSelectedLocation(facilityId);
                setClientData({ clientId, clientName });
                setServiceCategoryId(serviceCategoryId);
                setSubcategoryId(subTypeId);

                // Set start time, end time, and date
                const start = dayjs(from, 'HH:mm');

                console.log('statrt-------------', start);
                setStartDate(dayjs(date));
                setStartTime(start);
                setEndTime(start.add(duration, 'minute'));

                // Generate duration options dynamically
                setDurationOption(
                    Array.from({ length: 5 }, (_, i) => {
                        const value = subTypeDuration * (i + 1);
                        return {
                            label: `${value} Minutes`,
                            value,
                        };
                    })
                );

                // Fetch in parallel:
                const [_serviceCategories, packageResponse, _roomList] =
                    await Promise.all([
                        dispatch(
                            activeServiceCategoyListv1({
                                classType: ClassType.BOOKING,
                            })
                        ).unwrap(),

                        dispatch(
                            PricingListingByUserAndSubType({
                                classType: 'bookings',
                                clientUserId: clientId,
                                serviceCategoryId,
                                subTypeId,
                                pageSize: 50,
                            })
                        ).unwrap(),
                    ]);
                // in case of edit and view send a a parameter isEdit
                await dispatch(
                    roomListingByScheduling({
                        serviceId: serviceCategoryId,
                        facilityId: selectedLocation,
                        classType: ClassType.BOOKING,
                        date: dayjs(date).format('YYYY-MM-DD'),
                        startTime: dayjs(start).format('HH:mm'),
                        endTime: dayjs(start.add(duration, 'minute')).format(
                            'HH:mm'
                        ),
                        scheduleId: scheduleId,
                        // isEdit: true
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        const roomOptions = res?.data?.data?.map(
                            (item: any) => ({
                                value: item._id,
                                label: item.roomName,
                                id: item._id,
                                isAvailable: item.isAvailable,
                                reason: item.reason,
                            })
                        );
                        setRoomData(roomOptions);
                    });
                // Match selected package from response
                const packageList = packageResponse?.data?.data || [];
                const selectedPackage = packageList.find(
                    (pkg: any) => pkg.purchaseId === purchaseId
                );

                if (selectedPackage) {
                    setRemainingSessions(selectedPackage.remainingSession);
                }
               
                // Pre-populate form
                form.setFieldsValue({
                    location: facilityId,
                    client: clientId,
                    roomId,
                    notes,
                    package: selectedPackage
                        ? {
                            value: selectedPackage.purchaseId,
                            label: selectedPackage.packageName,
                        }
                        : undefined,
                    serviceType: JSON.stringify({
                        serviceId: serviceCategoryId,
                        appointmentId: subTypeId,
                    }),
                    subType: subTypeId,
                    duration,
                });
            } catch (error) {
                console.error('Error fetching scheduling details:', error);
            }
        };

        fetchSchedulingDetails();
    }, [dispatch, scheduleId]);

    const getNextTimeSlot = (step: number) => {
        const now = dayjs(startDate);
        const minutes = now.minute();
        const nextMinutes = Math.ceil(minutes / step) * step;
        return now.minute(nextMinutes).second(0).millisecond(0);
    };

    const handleAppointmentTypeChange = (appointmentData: any) => {
        setDurationOption(
            Array.from({ length: 5 }, (_, i) => {
                const multiple = appointmentData.durationInMinutes * (i + 1);
                return {
                    label: `${multiple} Minutes`,
                    value: multiple,
                };
            })
        );
        const startTimeValue = getNextTimeSlot(
            appointmentData.durationInMinutes
        );

        if (!scheduleId) {
            setStartTime(startTimeValue);
        }
        form.setFieldValue('duration', appointmentData.durationInMinutes);
        const calculatedEndTime = startTimeValue.add(
            appointmentData.durationInMinutes,
            'minute'
        );
        setEndTime(calculatedEndTime);
        setStartTimeMinuteStep(appointmentData.durationInMinutes);
    };
    useEffect(() => {
        const fetchInitialBookingData = async () => {
            startLoader();

            try {
                // 1. Set client upfront
                if (clientId) setClientData({ clientId });

                // 2. Fire off all required API calls in parallel where possible
                const [serviceCategoriesRes, pricingRes] = await Promise.all([
                    dispatch(
                        activeServiceCategoyListv1({
                            classType: ClassType.BOOKING,
                        })
                    ).unwrap(),

                    dispatch(
                        PricingListingByUserAndType({
                            userId: clientId,
                            classType: 'bookings',
                        })
                    ).unwrap(),
                ]);

                const packageList = pricingRes?.data?.data || [];
                const selectedPackage = packageList.find(
                    (pkg: any) => pkg._id === purchaseId
                );

                const { packageId, remainingSession, packageName } =
                    selectedPackage;
                form.setFieldsValue({
                    package: {
                        value: selectedPackage._id,
                        label: packageName,
                    },
                });
                if (!selectedPackage) {
                    endLoader();
                    return;
                }

                setRemainingSessions(remainingSession);

                // 3. Fetch services based on packageId
                const servicesRes = await dispatch(
                    serviceBypricingIdV1({ pricingId: packageId })
                ).unwrap();

                const service = servicesRes?.data?.data?.[0];

                const appointmentType = service.appointmentType?.[0];

                // 4. Fetch room list & service category list in parallel
                await Promise.all([
                    dispatch(
                        roomListingByServiceCategory({
                            serviceCategoryId: service._id,
                            facilityId: selectedLocation,
                        })
                    ),
                    dispatch(ServiceCategoryListByPackageId({ packageId })),
                ]);

                // 5. Update form values and state
                const appointmentData = {
                    serviceId: service._id,
                    appointmentId: appointmentType?._id,
                };

                setServiceCategoryId(service._id);
                setSubcategoryId(appointmentType?._id);
                handleAppointmentTypeChange(appointmentType);
                const startTimeValue = getNextTimeSlot(
                    appointmentType.durationInMinutes
                );
                const calculatedEndTime = startTimeValue.add(
                    appointmentType.durationInMinutes,
                    'minute'
                );
                await dispatch(
                    roomListingByScheduling({
                        serviceId: service._id,
                        facilityId: selectedLocation,
                        classType: ClassType.BOOKING,
                        date: dayjs(startDate).format('YYYY-MM-DD'),
                        startTime: dayjs(startTimeValue).format('HH:mm'),
                        endTime: dayjs(calculatedEndTime).format('HH:mm'),
                        // isEdit: true
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        const roomOptions = res?.data?.data?.map(
                            (item: any) => ({
                                value: item._id,
                                label: item.roomName,
                                id: item._id,
                                isAvailable: item.isAvailable,
                                reason: item.reason,
                            })
                        );
                        setRoomData(roomOptions);
                        if (roomOptions?.[0]?.isAvailable) {
                            form.setFieldsValue({
                                roomId: roomOptions?.[0]?.value,
                            });
                        }
                    });
                form.setFieldsValue({
                    serviceType: JSON.stringify(appointmentData),
                    duration: appointmentType?.durationInMinutes,
                });
            } catch (error) {
                console.error('Error in fetchInitialBookingData:', error);
            } finally {
                endLoader();
            }
        };

        fetchInitialBookingData();
    }, [selectedLocation]);

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));
    useEffect(() => {
        setSelectedLocation(FacilityOptions[0]?.id);
        form.setFieldsValue({
            location: FacilityOptions[0]?.id,
        });
    }, [FacilityOptions]);

    const serviceOptions = transformServiceData(
        store.activeServiceCategoyListv1
    );
    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );
    const roomOptions = store.roomList?.map((item: any) => ({
        value: item._id,
        label: item.roomName,
        id: item._id,
    }));

    const handleServiceTypeChange = async (
        value: string,
        customClientData?: any
    ) => {
        const selectedOption = JSON.parse(value);
        const serviceId = selectedOption.serviceId;
        setServiceCategoryId(serviceId);
        const appointmentId = selectedOption.appointmentId;
        setSubcategoryId(appointmentId);
        const duration = selectedOption.duration;
        const service = store.activeServiceCategoyListv1.find(
            (item: any) => item._id === serviceId
        ) || { appointmentType: [], noOfSessions: null };
        // Reset subType selection in the form
        form.setFieldsValue({
            subType: undefined,
            // package: null,
            roomId: null,
        });
        // setRemainingSessions('');

        const appointmentType: any = service.appointmentType?.find(
            (item: any) => item._id === appointmentId
        );
        handleAppointmentTypeChange(appointmentType);
        // Dispatch action with serviceCategoryId (main service ID)
        const startTimeValue = getNextTimeSlot(
            appointmentType.durationInMinutes
        );
        const calculatedEndTime = startTimeValue.add(
            appointmentType.durationInMinutes,
            'minute'
        );

        // Update state with available appointment types
        setAppointmentTypeOption(appointmentType);

        const activeClient = customClientData || clientData;

        if (activeClient && serviceId && appointmentId) {
            const payload = {
                classType: ClassType.BOOKING,
                clientUserId: activeClient.value || activeClient.userId,
                serviceCategoryId: serviceId,
                subTypeId: appointmentId,
                pageSize: 50,
                page: 1,
                search: '',
                isNewBooking: true,
            };

            const response = await dispatch(
                PricingListingByUserAndSubType(payload)
            ).unwrap();
            const packages = response?.data?.data;
            if (packages?.length > 0) {
                const firstPackage = packages[0];
                form.setFieldValue('package', {
                    value: firstPackage.purchaseId,
                    label: firstPackage.packageName,
                });
                setRemainingSessions(firstPackage.remainingSession);
            }
        }
        await dispatch(
            roomListingByScheduling({
                serviceId: serviceId,
                facilityId: selectedLocation,
                classType: ClassType.BOOKING,
                date: dayjs(startDate).format('YYYY-MM-DD'),
                startTime: dayjs(startTimeValue).format('HH:mm'),
                endTime: dayjs(calculatedEndTime).format('HH:mm'),
            })
        )
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                    isAvailable: item.isAvailable,
                    reason: item.reason,
                }));
                setRoomData(roomOptions);
                if (roomOptions?.[0]?.isAvailable) {
                    form.setFieldsValue({
                        roomId: roomOptions?.[0]?.value,
                    });
                }
            });
    };
    const handleClientSelect = async (client: any) => {
        setClientData(client);
        if (!scheduleId) {


            const res: any = await dispatch(
                activeServiceCategoyListv1({
                    classType: ClassType.BOOKING,
                })
            ).unwrap();

            console.log('Res----res------- res-----', res);

            const serviceList = res?.data?.data?.list || [];

            if (!serviceList.length) return;

            const firstService = serviceList?.find(
                (service: any) => service.appointmentType?.length > 0
            );

            if (!firstService) return;

            const firstAppointment = firstService.appointmentType[0];

            const value = JSON.stringify({
                serviceId: firstService._id,
                appointmentId: firstAppointment._id,
            });

            form.setFieldsValue({
                serviceType: value,
            });
            setClientData(client);

            setTimeout(() => {
                handleServiceTypeChange(value, client);
            }, 0);
        }
    };

    const handlePackageChange = (packageId: string) => {
        const packageData = store.pricingListUserAndSubType.find(
            (item: any) => item._id === packageId
        );
        setRemainingSessions(packageData?.remainingSession);
    };

    const fetchRoomData = async ({ date, startsTime, endsTime }: any) => {
        await dispatch(
            roomListingByScheduling({
                serviceId: serviceCategoryId,
                facilityId: selectedLocation,
                classType: ClassType.BOOKING,
                date: dayjs(date).format('YYYY-MM-DD'),
                startTime: startsTime
                    ? dayjs(startsTime).format('HH:mm')
                    : dayjs(startTime).format('HH:mm'),
                endTime: endsTime
                    ? dayjs(endsTime).format('HH:mm')
                    : dayjs(endTime).format('HH:mm'),
            })
        )
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                    isAvailable: item.isAvailable,
                    reason: item.reason,
                }));
                setRoomData(roomOptions);
                form.setFieldsValue({
                    roomId: roomOptions?.[0]?.value,
                });
            });
    };
    const handleDuration = (value: any) => {
        if (startTime) {
            const calculatedEndTime = startTime.add(value, 'minute');
            setEndTime(calculatedEndTime);
            fetchRoomData({
                date: startDate,
                startsTime: startTime,
                endsTime: calculatedEndTime,
            });
        } else {
            setEndTime(null);
        }
    };

    const handleStartTimeChange = (time: any) => {
        setStartTime(time);
        if (time) {
            const duration = form.getFieldValue('duration');
            const calculatedEndTime = dayjs(time).add(
                Number(duration),
                'minute'
            );
            setEndTime(calculatedEndTime);
            fetchRoomData({
                date: startDate,
                startsTime: time,
                endsTime: calculatedEndTime,
            });
        } else {
            setEndTime(null);
        }
    };
    const onFinish = (values: any, checkIn = false) => {
        const serviceData = JSON.parse(values.serviceType);

        // Extract individual values
        const serviceId = serviceData.serviceId;
        const appointmentId = serviceData.appointmentId;
        const purchaseIdValue = values.package?.value || values.package;
        const payload = {
            facilityId: values.location,
            clientId: clientData.value ? clientData.value : clientData.userId,
            classType: 'bookings',
            roomId: values.roomId,
            notes: values.notes,
            dateRange: 'Single',
            purchaseId: purchaseIdValue,
            serviceCategory: serviceId,
            sendConfirmation: values.sendConfirmation,
            subType: appointmentId,
            duration: values.duration,
            date: dayjs(startDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            from: dayjs(startTime).format('HH:mm'),
            to: dayjs(endTime).format('HH:mm'),
            ...(!scheduleId && { checkIn }),
        };

        if (scheduleId) {
            startSubmitLoader();
            dispatch(
                UpdateBookedScheduling({ payload: { ...payload, scheduleId } })
            )
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(endSubmitLoader);
        } else {
            if (checkIn) startSubmitCheckInLoader();
            else startSubmitLoader();
            dispatch(CreateBookScheduling({ payload: payload }))
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(() => {
                    endSubmitLoader();
                    endSubmitCheckInLoader();
                });
        }
    };
    console.log('Selected package id is:::::::', purchaseId);
    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        verticalLabelMargin: '-10px',
                    },
                },
            }}
        >
            <Form
                className=" flex flex-col lg:gap-5"
                name="schedule-form"
                form={form}
                variant="borderless"
                layout="horizontal"
                size="large"
                onFinish={onFinish}
                disabled={!isEdit && scheduleId}
            >
                <div className="flex flex-row">
                    <div className="w-[40%]  lg:-translate-y-16  ">
                        {(selectedLocation || clientData) && (
                            <FormClientDetails
                                facilityId={selectedLocation}
                                onClientSelect={handleClientSelect}
                                clientData={clientData}
                                isEdit={clientId || scheduleId ? true : false}
                            />
                        )}
                    </div>
                    <div className="w-[60%] border-s-2 ps-6">
                        <div className="flex w-full justify-between ">
                            <div className="  lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left  lg:w-[55px]">
                                            Location
                                        </p>
                                    }
                                    name="location"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select location',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        className="border-b-1"
                                        allowClear
                                        defaultValue={selectedLocation}
                                        onChange={(value) => {
                                            setSelectedLocation(value);
                                            form.setFieldsValue({
                                                staff: '',
                                                client: '',
                                                phone: '',
                                                email: '',
                                            });
                                        }}
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={
                                            role === RoleType.TRAINER
                                                ? LocationOptionByStaff
                                                : FacilityOptions
                                        }
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className=" flex w-full justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Service
                                        </p>
                                    }
                                    name="serviceType"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select a service',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1"
                                        showSearch
                                        placeholder="Select Service"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={handleServiceTypeChange}
                                        disabled={
                                            !clientData ||
                                            (!isEdit && scheduleId)
                                        }
                                        style={
                                            !clientData ||
                                                (!isEdit && scheduleId)
                                                ? {
                                                    backgroundColor:
                                                        '#f5f5f5',
                                                    color: '#a0a0a0',
                                                    cursor: 'not-allowed',
                                                }
                                                : {}
                                        }
                                    >
                                        {serviceOptions.map((group: any) => (
                                            <OptGroup
                                                key={group.category}
                                                label={group.category}
                                            >
                                                {group.options.map(
                                                    (service: any) => (
                                                        <Option
                                                            key={service.value}
                                                            value={
                                                                service.value
                                                            }
                                                        >
                                                            {service.label}
                                                        </Option>
                                                    )
                                                )}
                                            </OptGroup>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-full gap-4  lg:w-[95%] @sm:-mt-5">
                            <div className="flex w-full justify-between gap-3  lg:flex-row lg:items-end ">
                                <div className="w-[100%] ">
                                    <Form.Item
                                        className=" w-full"
                                        label={
                                            <p className=" text-left ">
                                                Package
                                            </p>
                                        }
                                        name="package"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select package',
                                            },
                                        ]}
                                    >
                                        <InfiniteScrollSelect
                                            fetchOptions={fetchPackageListing}
                                            className="border-b border-[#d1d5db]"
                                            // options={clientOptions}
                                            onChange={(value, option) => {
                                                handlePackageChange(value);
                                            }}
                                            placeholder="Select Package"
                                            disabled={
                                                !clientData ||
                                                (!isEdit && scheduleId) ||
                                                !serviceCategoryId
                                            }
                                        />
                                        {/* <Select
                                            showSearch
                                            className="border-b-1"
                                            // className="w-[50%]"
                                            placeholder="Select Package"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={PricingOption}
                                            onChange={handlePackageChange}
                                        /> */}
                                    </Form.Item>
                                </div>
                                {/* <div className="w-[30%]">
                                    <Form.Item
                                        label={''}
                                        name="sendConfirmation"
                                        valuePropName="checked"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select client',
                                            },
                                        ]}
                                    >
                                        <Checkbox className="  text-lg ">
                                            Pay Later
                                        </Checkbox>
                                    </Form.Item>
                                </div> */}
                            </div>
                        </div>

                        <div className="  s flex w-full justify-between gap-4 lg:mb-7 lg:w-[95%] @sm:-mt-5">
                            <div className="w-[23%]"></div>
                            <Input
                                type="text"
                                // style={{
                                //     borderBottom: '1px solid #e5e7eb',
                                //     borderRadius: '0px',
                                // }}
                                // className="w-[95%] -translate-y-3"
                                style={{
                                    backgroundColor: '#f5f5f5',
                                    color: '#a0a0a0',
                                    cursor: 'not-allowed',
                                    borderRadius: '0px',
                                }}
                                disabled
                                value={remainingSessions}
                                placeholder="No. of session left"
                            />
                        </div>

                        <div className=" flex justify-between lg:w-[95%]  lg:flex-row lg:items-center @sm:flex-col">
                            <p className=" text-[13px] font-medium text-[#1A3353] lg:ms-2  @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                Select Date
                            </p>
                            <DatePicker
                                popupClassName="custom-datepicker"
                                value={startDate}
                                format="DD/MM/YYYY"
                                onChange={(date) => {
                                    setStartDate(date);
                                    fetchRoomData({ date });
                                }}
                                className="lg:w-[80%] @sm:w-[100%]"
                                style={{
                                    borderBottom: '1px solid #e5e7eb',
                                    borderRadius: '0px',
                                    backgroundColor: !serviceCategoryId
                                        ? '#f5f5f5'
                                        : undefined,
                                    color: !serviceCategoryId
                                        ? '#a0a0a0'
                                        : undefined,
                                    cursor: !serviceCategoryId
                                        ? 'not-allowed'
                                        : undefined,
                                }}
                                disabled={
                                    !serviceCategoryId ||
                                    (!isEdit && scheduleId)
                                }
                            />
                        </div>

                        <div className=" mt-6 flex flex-row items-center ">
                            <div className="flex justify-between  lg:flex-row  lg:items-center @sm:flex-col @lg:w-[21%] @xl:w-[19%] @2xl:w-[19%]">
                                <p className="  text-[13px] font-medium text-[#1A3353] lg:ms-2   lg:w-[65px] @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                    Start Time
                                </p>
                            </div>
                            <div className="flex flex-row items-center  @2xl:w-[76%] ">
                                <div className="w-[20%] ">
                                    <TimePicker
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: !serviceCategoryId
                                                ? '#f5f5f5'
                                                : undefined,
                                            color: !serviceCategoryId
                                                ? '#a0a0a0'
                                                : undefined,
                                            cursor: !serviceCategoryId
                                                ? 'not-allowed'
                                                : undefined,
                                        }}
                                        format="HH:mm"
                                        value={startTime}
                                        minuteStep={5}
                                        // className="lg:w-[80%] @sm:w-[100%]"
                                        onChange={handleStartTimeChange}
                                        disabled={
                                            !serviceCategoryId ||
                                            (!isEdit && scheduleId)
                                        }
                                    />
                                </div>

                                <div className=" w-[38%] lg:translate-y-3 @xl:translate-x-3 ">
                                    <Form.Item
                                        className="PA-form-duration-input"
                                        label={
                                            <p className=" text-left  @sm:w-full">
                                                Duration
                                            </p>
                                        }
                                        name="duration"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select duration',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            className="ms-2 w-[90%] border-b-1"
                                            placeholder="Select Duration"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            onChange={handleDuration}
                                            options={durationOption}
                                            disabled={
                                                !serviceCategoryId ||
                                                (!isEdit && scheduleId)
                                            }
                                            style={{
                                                backgroundColor:
                                                    !serviceCategoryId
                                                        ? '#f5f5f5'
                                                        : undefined,
                                                color: !serviceCategoryId
                                                    ? '#a0a0a0'
                                                    : undefined,
                                                cursor: !serviceCategoryId
                                                    ? 'not-allowed'
                                                    : undefined,
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" flex w-[38%] justify-end lg:flex-row   lg:items-center @sm:flex-col @xl:gap-0 ">
                                    <p className="translate-x-3 font-medium text-[#1A3353] lg:w-[40%] @lg:text-[12px] @xl:w-[35%] @xl:text-[13px]">
                                        End Time
                                    </p>
                                    <TimePicker
                                        format="HH:mm"
                                        disabled
                                        value={endTime}
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                    />
                                </div>
                            </div>
                        </div>

                        <div className=" mt-6 flex w-full justify-between">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Room
                                        </p>
                                    }
                                    name="roomId"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select room',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        placeholder="Select Room"
                                        value={form.getFieldValue('roomId')} // or controlled via state
                                        onChange={(value) => {
                                            form.setFieldValue('roomId', value);
                                            const selected = roomData.find(
                                                (room: any) =>
                                                    room.value === value
                                            );
                                            if (selected) {
                                                setSelectedRoomReason(
                                                    selected.reason || null
                                                );
                                                setSelectedRoomAvailability(
                                                    selected.isAvailable
                                                );
                                            } else {
                                                setSelectedRoomReason(null);
                                                setSelectedRoomAvailability(
                                                    null
                                                );
                                            }
                                        }}
                                        optionLabelProp="label"
                                        className="border-b"
                                    >
                                        {roomData.map((room: any) => (
                                            <Select.Option
                                                key={room.value}
                                                value={room.value}
                                                label={room.label} // this ensures correct label is used in the input
                                            >
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        justifyContent:
                                                            'space-between',
                                                        alignItems: 'center',
                                                        padding: '6px 12px',
                                                        fontWeight: 500,
                                                    }}
                                                >
                                                    <span
                                                        style={{
                                                            color: '#1A3353',
                                                        }}
                                                    >
                                                        {room.label}
                                                    </span>
                                                    <span
                                                        style={{
                                                            fontSize: '12px',
                                                            fontWeight: 600,
                                                            color: room.isAvailable
                                                                ? '#16A34A'
                                                                : 'red',
                                                        }}
                                                    >
                                                        {room.isAvailable
                                                            ? 'Available'
                                                            : 'Not Available'}
                                                    </span>
                                                </div>
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                                {selectedRoomReason &&
                                    !selectedRoomAvailability && (
                                        <div
                                            style={{
                                                color: '#ff4d4f',
                                                marginTop: '4px',
                                                fontSize: '13px',
                                            }}
                                        >
                                            {selectedRoomReason}
                                        </div>
                                    )}
                            </div>
                        </div>
                        <div className=" flex w-full justify-between lg:mb-4">
                            <div className=" lg:w-[95%] @sm:w-full">
                                <Form.Item label="Notes" name="notes">
                                    <TextArea
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                        }}
                                        autoSize={{ minRows: 1, maxRows: 2 }}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                </div>
                {(!scheduleId || (scheduleId && isEdit)) && (
                    <>
                        <div className="flex w-[100%] flex-row gap-5 pe-10 lg:justify-end @sm:justify-center">
                            <Form.Item>
                                <div
                                    className=""
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        loading={submitCheckInLoader}
                                        className="border border-[#1A3353] px-20 py-7 text-2xl"
                                        onClick={() =>
                                            onFinish(
                                                form.getFieldsValue(),
                                                true
                                            )
                                        }
                                    >
                                        Save & Check in
                                    </Button>
                                </div>
                            </Form.Item>
                            <Form.Item>
                                <div
                                    className=""
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        loading={submitLoader}
                                        className="bg-purpleLight px-20 py-7 text-2xl"
                                        type="primary"
                                        htmlType="submit"
                                    >
                                        Save
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </>
                )}
            </Form>
        </ConfigProvider>
    );
};

export default BookingForm;
