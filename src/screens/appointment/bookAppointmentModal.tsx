import React, { useEffect, useMemo, useState } from 'react';
import {
    Select,
    DatePicker,
    TimePicker,
    Input,
    Checkbox,
    Form,
    Button,
    FormProps,
    ConfigProvider,
    Tooltip,
    Radio,
    Switch,
} from 'antd';
// import { Dayjs } from 'dayjs';
import { useSelector } from 'react-redux';
import { RoleType, ClassType, DateRangeType } from '~/types/enums';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { capitalizeFirstLetter } from '~/components/common/function';
import { GetFacilityListByStaffId } from '~/redux/actions/facility-action';
import {
    TrainerListing,
    PricingListingByUserAndSubType,
    StaffAvailabilityListV1,
} from '~/redux/actions/appointment-action';
import { CustomerList } from '~/redux/actions/customer-action';
import { useDebounce } from '~/hooks/useDebounce';
import dayjs, { Dayjs } from 'dayjs';
import TextArea from 'antd/es/input/TextArea';
import {
    ServiceCategoryListByPackageId,
    activeServiceCategoyListv1,
} from '~/redux/actions/serviceCategoryAction';
import {
    roomListingByServiceCategory,
    roomListingByScheduling,
} from '~/redux/actions/room-action';
import {
    BookedSchedulingDetails,
    CreateBookScheduling,
    UpdateBookedScheduling,
    BookedCalendarData,
    CreateAppointmentScheduling,
    UpdateAppointmentScheduling,
} from '~/redux/actions/scheduling-action';
import FormClientDetails from './form-client-detail';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import { InfoCircleOutlined } from '@ant-design/icons';
import WeeklySchedule from '~/components/staff/weeklySchedule';
import WorkingHoursComponent from '../gym/workingHours';
import DaySelector from '~/components/common/day-selector';

interface BookingFormProps {
    visible?: boolean;
    onClose?: any;
    scheduleId?: string;
    isEdit?: boolean;
    clientId?: string;
    facilityId?: string;
    slotSelectedInfo?: any;
    slotUpdateInfo?: any;
}

const daysOfWeek = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

interface TimeSlot {
    from: Dayjs | null | any;
    to: Dayjs | null | any;
    payRateIds: any;
}
interface WorkingHours {
    [key: string]: TimeSlot[];
}
const { Option, OptGroup } = Select;

const BookAppointmentModal: React.FC<BookingFormProps> = ({
    visible,
    onClose,
    scheduleId,
    isEdit,
    clientId,
    facilityId,
    slotSelectedInfo,
}: any) => {
    // Api start
    const { role, user } = useSelector((state: any) => state.auth_store);

    const [dateRange, setDateRange] = useState<'Single' | 'Multiple'>('Single');
    const [datePickerOpen, setDatePickerOpen] = useState(false);
    const [dateSelectedTime, setDateSelectedTime] = useState<string>();

    const [workingHours, setWorkingHours] = useState<WorkingHours>({
        mon: [],
        tue: [],
        wed: [],
        thu: [],
        fri: [],
        sat: [],
        sun: [],
    });
    const [availabilityType, setAvailabilityType] =
        useState<string>('available');
    const [daySelected, setDaySelected] = useState<string>('');

    const [form] = Form.useForm();

    const [selectedLocation, setSelectedLocation] = useState(
        facilityId ? facilityId : null
    );
    const [startDate, setStartDate] = useState<dayjs.Dayjs | null>(dayjs());
    const [isChecked, setIsChecked] = useState(false);
    const [startTime, setStartTime] = useState<dayjs.Dayjs | null>(null);
    const [endTime, setEndTime] = useState<dayjs.Dayjs | null>(null);
    const [startTimeMinuteStep, setStartTimeMinuteStep] = useState<any>(1);
    const [appointmentTypeOption, setAppointmentTypeOption] = useState<any>([]);
    const [durationOption, setDurationOption] = useState<any>([]);
    const [remainingSessions, setRemainingSessions] = useState('');
    const [clientData, setClientData] = useState<any>();
    const [serviceCategoryId, setServiceCategoryId] = useState<any>(null);
    const [subCategoryId, setSubcategoryId] = useState<any>(null);
    const [staffOption, setStaffOption] = useState<any>(null);
    const [roomData, setRoomData] = useState<any>([]);
    const [staffErrorMessage, setStaffErrorMessage] = useState<string>('');

    const [selectedDay, setSelectedDay] = useState<string>('mon');

    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        roomList: state.room_store.roomList,
        user: state.auth_store.user,
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
        trainerListForForms: state.appointment_store.trainerListForForms,
        pricingList: state.appointment_store.pricingList,
        customerListForForms: state.customer_store.customerListForForms,
        ServiceCategoryListByPackageId:
            state.service_category_store.ServiceCategoryListByPackageId,
        activeServiceCategoyListv1:
            state.service_category_store.ActiveServiceCategoryListV1,
        pricingListUserAndSubType:
            state.appointment_store.pricingListUserAndSubType,
    }));

    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [
        submitCheckInLoader,
        startSubmitCheckInLoader,
        endSubmitCheckInLoader,
    ] = useLoader();
    useEffect(() => {
        if (clientId) {
            setClientData({ clientId: clientId });
        }
        if (facilityId) form.setFieldValue('location', facilityId);
    }, []);

    useEffect(() => {
        if (slotSelectedInfo) {
            setStartTime(dayjs(slotSelectedInfo.startDate));
            setStartDate(dayjs(slotSelectedInfo.startDate));
        }
    }, [slotSelectedInfo]);

    useEffect(() => {
        if (role === RoleType.TRAINER) {
            dispatch(GetFacilityListByStaffId({ staffId: user._id })).then(
                (res: any) => {
                    form.setFieldsValue({
                        location: res?.payload?.data?.data?.[0]?._id,
                    });
                }
            );
            form.setFieldsValue({
                staffName: `${store.user?.firstName} ${store.user?.lastName}`,
            });
        }
    }, [visible]);

    useEffect(() => {
        if (selectedLocation) {
            dispatch(
                TrainerListing({ facilityId: selectedLocation, isActive: true })
            );
        }
    }, [selectedLocation]);

    useEffect(() => {
        startLoader();
        const requestParams = {
            page: 1,
            pageSize: 20,
        } as any;

        if (selectedLocation) {
            requestParams.locationId = [selectedLocation];

            debouncedRequest(() => {
                dispatch(
                    CustomerList({
                        ...requestParams,
                        isActive: true,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        if (clientId) {
                            const client = res?.data?.data?.list.find(
                                (item: any) => item.userId === clientId
                            );
                            // dispatch(
                            //     PricingListingByUserAndType({
                            //         userId: clientId,
                            //         classType: 'personalAppointment',
                            //     })
                            // )
                            form.setFieldsValue({
                                email: client?.email,
                                phone: client?.mobile,
                                client: clientId,
                            });
                        }
                    })
                    .finally(endLoader);
            });
            dispatch(
                activeServiceCategoyListv1({
                    classType: ClassType.PERSONAL_APPOINTMENT,
                })
            );
        }
    }, [selectedLocation]);
    const transformServiceData = (apiData: any) => {
        return apiData.map((service: any) => ({
            category: service.name,
            options: service.appointmentType.map((appointment: any) => ({
                label: `${appointment.name} - ${appointment.durationInMinutes} mins`,
                value: JSON.stringify({
                    serviceId: service._id,
                    appointmentId: appointment._id,
                    // duration: appointment.durationInMinutes,
                }),
            })),
        }));
    };
    const serviceOptions = transformServiceData(
        store.activeServiceCategoyListv1
    );
    const PricingOption = store.pricingList?.map((item: any) => ({
        value: item._id,
        label: item.packageName,
        id: item._id,
        packageId: item.packageId,
    }));

    const handleDayClick = (day: string) => {
        setSelectedDay(day);
    };

    useEffect(() => {
        const fetchSchedulingDetails = async () => {
            if (!scheduleId) return;

            try {
                // 1. Fetch booking details
                const res: any = await dispatch(
                    BookedSchedulingDetails({ scheduleId })
                ).unwrap();
                const scheduleData = res?.data?.data;
                if (!scheduleData) return;

                const {
                    clientEmail,
                    clientPhone,
                    trainerId,
                    facilityId,
                    clientId,
                    clientName,
                    serviceCategoryId,
                    subTypeId,
                    subTypeDuration,
                    roomId,
                    notes,
                    purchaseId,
                    duration,
                    from,
                    date,
                    subTypeList,
                    packageId,
                } = scheduleData;

                // 2. Set static state values
                setClientData({ clientId, clientName });
                setSelectedLocation(facilityId);
                setServiceCategoryId(serviceCategoryId);

                const startDateObj = dayjs(date);
                const startTimeObj = dayjs(from, 'HH:mm');
                const endTimeObj = startTimeObj.add(duration, 'minute');

                setStartDate(startDateObj);
                setStartTime(startTimeObj);
                setEndTime(endTimeObj);
                setStartTimeMinuteStep(subTypeDuration);

                // 3. Setup duration options
                setDurationOption(
                    Array.from({ length: 5 }, (_, i) => {
                        const multiple = subTypeDuration * (i + 1);
                        return {
                            label: `${multiple} Minutes`,
                            value: multiple,
                        };
                    })
                );

                // 4. Set appointment types
                const appointmentTypeList =
                    subTypeList?.map((item: any) => ({
                        value: item._id,
                        label: item.name,
                        id: item._id,
                        ...item,
                    })) || [];
                setAppointmentTypeOption(appointmentTypeList);

                // 5. Fetch in parallel: room list, categories, package list
                const [_, pricingRes] = await Promise.all([
                    dispatch(
                        activeServiceCategoyListv1({
                            classType: ClassType.PERSONAL_APPOINTMENT,
                        })
                    ),
                    dispatch(
                        PricingListingByUserAndSubType({
                            classType: ClassType.PERSONAL_APPOINTMENT,
                            clientUserId: clientId,
                            serviceCategoryId,
                            subTypeId,
                            pageSize: 50,
                        })
                    ).unwrap(),
                ]);
                const matchedRoom = store.roomList?.find(
                    (room: any) => room._id === roomId
                );
                const packageList = pricingRes?.data?.data || [];
                const selectedPackage = packageList.find(
                    (pkg: any) => pkg.purchaseId === purchaseId
                );
                if (selectedPackage) {
                    setRemainingSessions(selectedPackage.remainingSession);
                }

                // 6. Optional: Fetch trainer list and set options

                const reqData = {
                    facilityId,
                    classType: ClassType.PERSONAL_APPOINTMENT,
                    serviceId: serviceCategoryId,
                    subTypeId,
                    date: startDateObj.format('YYYY-MM-DDT00:00:00[Z]'),
                    startTime: startTimeObj.format('HH:mm'),
                    endTime: endTimeObj.format('HH:mm'),
                    scheduleId: scheduleId ? scheduleId : undefined,
                };

                const trainerResponse = await dispatch(
                    StaffAvailabilityListV1({ reqData })
                ).unwrap();
                const trainerOptions =
                    trainerResponse?.data?.data?.map((item: any) => {
                        const name = capitalizeFirstLetter(
                            `${item.firstName} ${item.lastName}`
                        );
                        const availability = item?.isAvailable
                            ? 'Available'
                            : 'Not Available';

                        return {
                            value: item._id,
                            label: (
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <span>{name}</span>
                                    <span
                                        style={{
                                            color: item.isAvailable
                                                ? 'green'
                                                : 'red',
                                        }}
                                    >
                                        {availability}
                                    </span>
                                </div>
                            ),
                            name, // 👈 for display after selection
                        };
                    }) || [];

                const trainerExists = trainerOptions.some(
                    (t: any) => t.value === trainerId
                );
                if (!trainerExists && trainerId) {
                    const allTrainerListRes = await dispatch(
                        TrainerListing({
                            facilityId: selectedLocation,
                            isActive: true,
                        })
                    ).unwrap();

                    const bookedTrainer = allTrainerListRes?.data?.data?.find(
                        (t: any) => t._id === trainerId
                    );
                    if (bookedTrainer) {
                        const name = capitalizeFirstLetter(
                            `${bookedTrainer.firstName} ${bookedTrainer.lastName}`
                        );
                        trainerOptions.push({
                            value: bookedTrainer._id,
                            label: (
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <span>{name}</span>
                                    <span style={{ color: 'gray' }}>
                                        Not Available
                                    </span>
                                </div>
                            ),
                            name,
                        });
                    }
                }

                setStaffOption(trainerOptions);

                await dispatch(
                    roomListingByScheduling({
                        serviceId: serviceCategoryId,
                        facilityId: selectedLocation,
                        classType: ClassType.BOOKING,
                        date: dayjs(date).format('YYYY-MM-DD'),
                        startTime: dayjs(startTimeObj).format('HH:mm'),
                        endTime: dayjs(
                            startTimeObj.add(duration, 'minute')
                        ).format('HH:mm'),
                        scheduleId: scheduleId,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        const roomOptions = res?.data?.data?.map(
                            (item: any) => ({
                                value: item._id,
                                label: item.roomName,
                                id: item._id,
                            })
                        );
                        setRoomData(roomOptions);
                    });
                // 7. Set all fields together for minimal re-render
                form.setFieldsValue({
                    email: clientEmail,
                    phone: clientPhone,
                    location: facilityId,
                    client: clientId,
                    roomId: roomId,
                    notes,
                    subType: subTypeId,
                    duration,
                    staff: trainerId,
                    package: selectedPackage
                        ? {
                              value: selectedPackage.purchaseId,
                              label: selectedPackage.packageName,
                          }
                        : undefined,
                    serviceType: JSON.stringify({
                        serviceId: serviceCategoryId,
                        appointmentId: subTypeId,
                    }),
                });
            } catch (error) {
                console.error('Error fetching scheduling details:', error);
            }
        };

        fetchSchedulingDetails();
    }, [dispatch, scheduleId]);

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    const ServiceTypeOption = store.ServiceCategoryListByPackageId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const TrainerOptions = store.trainerListForForms?.map((item: any) => ({
        value: item.userId,
        label: capitalizeFirstLetter(`${item.firstName} ${item.lastName}`),
        id: item._id,
    }));

    const getNextTimeSlot = (step: number) => {
        const now = dayjs(startDate);
        const minutes = now.minute();
        const nextMinutes = Math.ceil(minutes / step) * step;
        return now.minute(nextMinutes).second(0).millisecond(0);
    };
    const trainerList = async ({
        date,
        calculatedEndTime,
        startTimer,
        serviceId,
        subTypeId,
    }: any) => {
        const facilityId = selectedLocation;
        let start = startTimer || startTime;
        if (!start) {
            const fallbackStep = startTimeMinuteStep || 30;
            start = getNextTimeSlot(fallbackStep);
            setStartTime(start);
        }

        let end = calculatedEndTime || endTime;
        if (!end) {
            const duration =
                form.getFieldValue('duration') || startTimeMinuteStep || 30;
            end = dayjs(start).add(duration, 'minute');
            setEndTime(end);
        }
        const reqData = {
            facilityId,
            classType: ClassType.PERSONAL_APPOINTMENT,
            serviceId: serviceId ? serviceId : serviceCategoryId,
            subTypeId: subTypeId ? subTypeId : subCategoryId,
            date: dayjs(date || startDate).format('YYYY-MM-DDT00:00:00[Z]'),
            startTime: dayjs(start).format('HH:mm'),
            endTime: dayjs(end).format('HH:mm'),
            scheduleId: scheduleId ? scheduleId : undefined,
        };
        try {
            const response = await dispatch(
                StaffAvailabilityListV1({ reqData })
            ).unwrap();
            const trainerOptions = response.data.data?.map((item: any) => {
                const name = capitalizeFirstLetter(
                    `${item.firstName} ${item.lastName}`
                );
                const availability = item?.isAvailable
                    ? 'Available'
                    : 'Not Available';

                return {
                    value: item._id,
                    label: (
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                color: item?.isAvailable ? 'green' : 'red',
                            }}
                        >
                            <span>{name}</span>
                            <span>{availability}</span>
                        </div>
                    ),
                    name,
                    id: item._id,
                    availability: item?.isAvailable,
                    reason: !item?.isAvailable ? item?.reason : '',
                };
            });

            setStaffOption(trainerOptions);

            if (trainerOptions?.length > 0 && trainerOptions[0].availability) {
                form.setFieldValue('staff', trainerOptions[0].value);
            } else {
                form.resetFields(['staff']);
            }
        } catch (err) {
            console.error('Trainer fetch failed:', err);
        }
    };

    const handleAppointmentTypeChange = (appointmentData: any) => {
        setDurationOption(
            Array.from({ length: 5 }, (_, i) => {
                const multiple = appointmentData.durationInMinutes * (i + 1);
                return {
                    label: `${multiple} Minutes`,
                    value: multiple,
                };
            })
        );
        const startTimeValue = getNextTimeSlot(
            appointmentData.durationInMinutes
        );
        setStartTime(startTimeValue);
        form.setFieldValue('duration', appointmentData.durationInMinutes);
        const calculatedEndTime = startTimeValue.add(
            appointmentData.durationInMinutes,
            'minute'
        );
        setEndTime(calculatedEndTime);
        setStartTimeMinuteStep(appointmentData.durationInMinutes);
    };

    const handleServiceTypeChange = async (
        value: string,
        customClientData?: any
    ) => {
        setStaffErrorMessage('');
        form.resetFields(['roomId', 'package', 'staff']);
        const selectedOption = JSON.parse(value);

        const serviceId = selectedOption.serviceId;
        const appointmentId = selectedOption.appointmentId;
        const duration = selectedOption.duration;

        if (!serviceId || !appointmentId) return;

        setServiceCategoryId(serviceId);
        setSubcategoryId(appointmentId);
        setRemainingSessions('');

        const service = store.activeServiceCategoyListv1.find(
            (item: any) => item._id === serviceId
        ) || { appointmentType: [] };

        const appointmentType: any = service.appointmentType?.find(
            (item: any) => item._id === appointmentId
        );

        // Step 1: Auto-set duration and time slots
        const fallbackStep = appointmentType?.durationInMinutes || 30;
        const autoStart = getNextTimeSlot(fallbackStep);
        const autoEnd = dayjs(autoStart).add(fallbackStep, 'minute');

        let startTimeToUse = startTime;
        let endTimeToUse = endTime;

        if (!isEdit || !scheduleId) {
            setStartTime(autoStart);
            setEndTime(autoEnd);
            startTimeToUse = autoStart;
            endTimeToUse = autoEnd;
        }

        setStartTimeMinuteStep(fallbackStep);

        form.setFieldsValue({
            duration: fallbackStep,
        });

        // Step 2: Update duration options
        const durationOptions = Array.from({ length: 5 }, (_, i) => {
            const multiple = fallbackStep * (i + 1);
            return { label: `${multiple} Minutes`, value: multiple };
        });
        setDurationOption(durationOptions);
        setAppointmentTypeOption(appointmentType);

        // Step 3: Fetch room list by service category (static)
        await dispatch(
            roomListingByServiceCategory({
                serviceCategoryId: serviceId,
                facilityId: selectedLocation,
            })
        ).unwrap();

        const activeClient = customClientData || clientData;
        // Step 4: Fetch & select first package
        if (activeClient && serviceId && appointmentId) {
            const payload = {
                classType: ClassType.PERSONAL_APPOINTMENT,
                clientUserId: clientData?.value,
                serviceCategoryId: serviceId,
                subTypeId: appointmentId,
                pageSize: 50,
                page: 1,
                search: '',
                isNewBooking: true,
            };

            const response = await dispatch(
                PricingListingByUserAndSubType(payload)
            ).unwrap();
            const packages = response?.data?.data;
            if (packages?.length > 0) {
                const firstPackage = packages[0];
                form.setFieldValue('package', {
                    value: firstPackage.purchaseId,
                    label: firstPackage.packageName,
                });
                setRemainingSessions(firstPackage.remainingSession);

                await trainerList({
                    date: startDate,
                    calculatedEndTime: endTimeToUse,
                    startTimer: startTimeToUse,
                    serviceId,
                    subTypeId: appointmentId,
                });
            }
        } else {
            await trainerList({
                date: startDate,
                calculatedEndTime: endTimeToUse,
                startTimer: startTimeToUse,
                serviceId,
                subTypeId: appointmentId,
            });
        }

        await dispatch(
            roomListingByScheduling({
                serviceId: serviceId,
                facilityId: selectedLocation,
                classType: ClassType.PERSONAL_APPOINTMENT,
                date: dayjs(startDate).format('YYYY-MM-DD'),
                startTime: dayjs(startTimeToUse).format('HH:mm'),
                endTime: dayjs(endTimeToUse).format('HH:mm'),
            })
        )
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                }));
                setRoomData(roomOptions);
                form.setFieldsValue({
                    roomId: roomOptions?.[0]?.value,
                });
            });
    };

    const handleClientSelect = async (client: any) => {
        setClientData(client);
        if (!scheduleId) {
            const res: any = await dispatch(
                activeServiceCategoyListv1({
                    classType: ClassType.PERSONAL_APPOINTMENT,
                })
            ).unwrap();

            const serviceList = res?.data?.data?.list || [];

            if (!serviceList.length) return;

            const firstService = serviceList.find(
                (service: any) => service.appointmentType?.length > 0
            );

            if (!firstService) return;

            const firstAppointment = firstService.appointmentType[0];

            const value = JSON.stringify({
                serviceId: firstService._id,
                appointmentId: firstAppointment._id,
            });

            // form.setFieldsValue({
            //     serviceType: value,
            // });

            setClientData(client);

            setTimeout(() => {
                form.setFieldsValue({
                    serviceType: value,
                });
                handleServiceTypeChange(value, client);
            }, 0);
        }
    };
    const fetchPackageListing = async (searchText = '', page = 1) => {
        if (clientData && serviceCategoryId && subCategoryId) {
            const payload = {
                classType: ClassType.PERSONAL_APPOINTMENT,
                clientUserId: clientData.value,
                serviceCategoryId: serviceCategoryId,
                subTypeId: subCategoryId,
                pageSize: 10,
                page,
                search: searchText,
                isNewBooking: true,
            };
            const response = await dispatch(
                PricingListingByUserAndSubType(payload)
            ).unwrap();

            return (
                response?.data?.data?.map((item: any) => ({
                    value: item.purchaseId,
                    label: `${item.packageName}`.trim(),
                })) || []
            );
        }
    };

    const handlePackageChange = (packageId: string) => {
        setStaffErrorMessage('');
        const packageData = store.pricingListUserAndSubType.find(
            (item: any) => item._id === packageId
        );
        setRemainingSessions(packageData?.remainingSession);
        trainerList({ date: undefined, calculatedEndTime: undefined });
    };
    const fetchRoomData = async ({ date, startsTime, endsTime }: any) => {
        await dispatch(
            roomListingByScheduling({
                serviceId: serviceCategoryId,
                facilityId: selectedLocation,
                classType: ClassType.BOOKING,
                date: dayjs(date).format('YYYY-MM-DD'),
                startTime: startsTime
                    ? dayjs(startsTime).format('HH:mm')
                    : dayjs(startTime).format('HH:mm'),
                endTime: endsTime
                    ? dayjs(endsTime).format('HH:mm')
                    : dayjs(endTime).format('HH:mm'),
            })
        )
            .unwrap()
            .then((res: any) => {
                const roomOptions = res?.data?.data?.map((item: any) => ({
                    value: item._id,
                    label: item.roomName,
                    id: item._id,
                }));
                setRoomData(roomOptions);
                form.setFieldsValue({
                    roomId: roomOptions?.[0]?.value,
                });
            });
    };
    const handleDuration = (value: any) => {
        setStaffErrorMessage('');
        let calculatedEndTime;
        if (startTime) {
            calculatedEndTime = startTime.add(value, 'minute');
            setEndTime(calculatedEndTime);
        } else {
            setEndTime(null);
        }
        const formValue = form.getFieldsValue();
        const parsedServiceType = JSON.parse(formValue.serviceType);
        trainerList({
            date: undefined,
            calculatedEndTime: calculatedEndTime,
            serviceId: parsedServiceType.serviceId,
            subTypeId: parsedServiceType.appointmentId,
        });
        fetchRoomData({
            date: startDate,
            startsTime: startTime,
            endsTime: calculatedEndTime,
        });
    };

    const handleStartTimeChange = (time: any) => {
        setStaffErrorMessage('');
        setStartTime(time);
        let calculatedEndTime;
        if (time) {
            const duration = form.getFieldValue('duration');
            calculatedEndTime = dayjs(time).add(Number(duration), 'minute');
            setEndTime(calculatedEndTime);
        } else {
            setEndTime(null);
        }
        const formValue = form.getFieldsValue();
        const parsedServiceType = JSON.parse(formValue.serviceType);
        trainerList({
            date: undefined,
            calculatedEndTime: calculatedEndTime,
            startTimer: time,
            serviceId: parsedServiceType.serviceId,
            subTypeId: parsedServiceType.appointmentId,
        });
        fetchRoomData({
            date: startDate,
            startsTime: time,
            endsTime: calculatedEndTime,
        });
    };

    const onFinish = (values: any, checkIn = false) => {
        const serviceData = JSON.parse(values.serviceType);
        const purchaseIdValue = values.package?.value || values.package;
        // Extract individual values
        const serviceId = serviceData.serviceId;
        const appointmentId = serviceData.appointmentId;
        const payload = {
            facilityId: values.location,
            trainerId: values.staff,
            // role === RoleType.TRAINER ? store.user?._id : values.staff,
            clientId: clientData.value || clientData.userId,
            classType: 'personalAppointment',
            roomId: values.roomId,
            notes: values.notes,
            dateRange: 'Single',
            purchaseId: purchaseIdValue,
            serviceCategory: serviceId,
            sendConfirmation: values.sendConfirmation,
            subType: appointmentId,
            duration: values.duration,
            date: dayjs(startDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            from: dayjs(startTime).format('HH:mm'),
            to: dayjs(endTime).format('HH:mm'),
            ...(!scheduleId && { checkIn }),
        };

        if (scheduleId) {
            startSubmitLoader();
            dispatch(
                UpdateAppointmentScheduling({
                    payload: { ...payload, scheduleId },
                })
            )
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(endSubmitLoader);
        } else {
            if (checkIn) startSubmitCheckInLoader();
            else startSubmitLoader();
            dispatch(CreateAppointmentScheduling({ payload: payload }))
                .unwrap()
                .then((res: any) => {
                    const status = res?.payload?.status ?? res?.status;

                    if (status === 200 || status === 201) {
                        form.resetFields();
                        setStartDate(null);
                        setStartTime(null);
                        setEndTime(null);
                        setSelectedLocation('');
                        onClose();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [selectedLocation],
                            })
                        );
                    }
                })
                .finally(() => {
                    endSubmitLoader();
                    endSubmitCheckInLoader();
                });
        }
    };
    const handleStaffChange = (value: any) => {
        const selectedStaff = staffOption.find(
            (staff: any) => staff.value === value
        );
        if (selectedStaff) {
            const isAvailable = selectedStaff.availability;
            if (!isAvailable) {
                setStaffErrorMessage(selectedStaff.reason);
            } else {
                setStaffErrorMessage('');
            }
        }
    };

    const handleDateRange = (value: any) => {
        setDateRange(value);
        form.setFieldsValue({
            // startDate: dayjs(slotUpdateInfo?.startDate),
            // endDate: dayjs(slotUpdateInfo?.endDate),
            // selectedDate: dayjs(slotUpdateInfo?.startDate),
        });
        // updateAvailabilityTimeSlots(null, false, value);
    };

    const handleDateChange = (date: any) => {
        if (!date) return;
        const dayOfWeek = date.format('ddd').toLowerCase();
        setDaySelected(dayOfWeek);
    };
    const updateAvailabilityTimeSlots = (
        date: any,
        updateDayOfWeek: boolean,
        range: string
    ) => {
        if (updateDayOfWeek) handleDateChange(date);
        const values = form.getFieldsValue();
        if (
            (isEdit && values?.startDate && values?.endDate) ||
            values.selectedDate
        ) {
            const startDate =
                range === 'Single'
                    ? dayjs(values.selectedDate).startOf('day').toDate()
                    : dayjs(values.startDate).startOf('day').toDate();
            const endDate =
                range === 'Single'
                    ? dayjs(values.selectedDate).endOf('day').toDate()
                    : dayjs(values.endDate).endOf('day').toDate();
            // dispatch(
            //     StaffAvailabilityDetails({
            //         payload: {
            //             facilityId: slotUpdateInfo?.facilityId,
            //             trainerId: slotUpdateInfo?.trainerId,
            //             startDate,
            //             endDate,
            //             from: slotUpdateInfo.from,
            //             to: slotUpdateInfo.to,
            //             dateRange: range,
            //         },
            //     })
            // ).then((res: any) => {
            //     const staffAvailabilityDetails = res.payload?.data?.data;
            //     const { timeSlots } = staffAvailabilityDetails;
            //     if (!timeSlots) return;
            //     setWorkingHours(timeSlots);
            // });
        }
    };
    return (
        <ConfigProvider
            theme={{
                components: {
                    Form: {
                        verticalLabelMargin: '-10px',
                    },
                },
            }}
        >
            <Form
                className=" flex flex-col lg:gap-5 "
                name="schedule-form"
                form={form}
                layout="horizontal"
                variant="borderless"
                size="large"
                onFinish={onFinish}
                disabled={!isEdit && !!scheduleId}
            >
                <div className="flex flex-row items-start">
                    <div className="w-[40%] lg:-translate-y-16  ">
                        <FormClientDetails
                            facilityId={selectedLocation}
                            onClientSelect={handleClientSelect}
                            clientData={clientData}
                            isEdit={clientId || scheduleId ? true : false}
                        />
                    </div>

                    <div className="w-[60%] border-s-2 ps-6">
                        <div className="flex w-full justify-between ">
                            <div className="  lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left">Location</p>
                                    }
                                    name="location"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select location',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        className="border-b-1"
                                        allowClear
                                        value={selectedLocation}
                                        onChange={(value) => {
                                            setSelectedLocation(value);
                                        }}
                                        placeholder="Select facility"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={
                                            role === RoleType.TRAINER
                                                ? LocationOptionByStaff
                                                : FacilityOptions
                                        }
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className=" flex w-full justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className="text-left @sm:w-full">
                                            Service
                                        </p>
                                    }
                                    name="serviceType"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select a service',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1 "
                                        showSearch
                                        placeholder="Select Service"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        onChange={handleServiceTypeChange}
                                        disabled={
                                            !clientData ||
                                            (!isEdit && scheduleId)
                                        }
                                        style={
                                            !clientData ||
                                            (!isEdit && scheduleId)
                                                ? {
                                                      backgroundColor:
                                                          '#f5f5f5',
                                                      color: '#a0a0a0',
                                                      cursor: 'not-allowed',
                                                  }
                                                : {}
                                        }
                                    >
                                        {serviceOptions.map(
                                            (group: any, index: number) => (
                                                <OptGroup
                                                    key={`${group.category}-${index}`}
                                                    label={group.category}
                                                >
                                                    {group.options.map(
                                                        (service: any) => (
                                                            <Option
                                                                key={
                                                                    service.value
                                                                }
                                                                value={
                                                                    service.value
                                                                }
                                                            >
                                                                {service.label}
                                                            </Option>
                                                        )
                                                    )}
                                                </OptGroup>
                                            )
                                        )}
                                    </Select>
                                </Form.Item>
                            </div>
                        </div>

                        <div className="flex w-full gap-4  lg:w-[95%] @sm:-mt-5">
                            <div className="flex w-full justify-between gap-3  lg:flex-row lg:items-end ">
                                <div className="w-[100%] ">
                                    {/* <Tooltip title={!clientData ? "Please select a client" : ""} placement="top"></Tooltip> */}

                                    <Form.Item
                                        className=" w-full"
                                        label={
                                            <p className=" text-left ">
                                                Package
                                            </p>
                                        }
                                        name="package"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select package',
                                            },
                                        ]}
                                    >
                                        <InfiniteScrollSelect
                                            fetchOptions={fetchPackageListing}
                                            // options={clientOptions}
                                            onChange={(value, option) => {
                                                handlePackageChange(value);
                                            }}
                                            placeholder="Select Package"
                                            className="w-[100%] border-b border-[#d1d5db]"
                                            disabled={
                                                !clientData ||
                                                (!isEdit && scheduleId) ||
                                                !serviceCategoryId
                                            }
                                        />
                                    </Form.Item>
                                </div>
                                {/* <div className="w-[30%]">
                                        <Form.Item
                                            label={''}
                                            name="sendConfirmation"
                                            valuePropName="checked"
                                            rules={[
                                                {
                                                    required: false,
                                                    message: 'Please select client',
                                                },
                                            ]}
                                        >
                                            <Checkbox className="  text-lg ">
                                                Pay Later
                                            </Checkbox>
                                        </Form.Item>
                                    </div> */}
                            </div>
                        </div>

                        <div className="  flex w-full justify-between gap-4 rounded-none lg:mb-7 lg:w-[95%] @sm:-mt-5">
                            <div className="w-[23%]"></div>
                            <Input
                                type="text"
                                style={{
                                    backgroundColor: '#f5f5f5',
                                    color: '#a0a0a0',
                                    cursor: 'not-allowed',
                                    borderRadius: '0px',
                                }}
                                // className="w-[95%] -translate-y-3"
                                disabled
                                value={remainingSessions}
                                placeholder="No. of session left"
                            />
                        </div>

                        <div className="mb-8 flex w-full justify-between ">
                            <div className="flex items-center ps-2 lg:w-[100%]">
                                <p className=" text-[13px] font-medium text-[#1A3353] lg:w-[20%]">
                                    Date Range
                                </p>
                                <Radio.Group
                                    value={dateRange}
                                    onChange={(e) =>
                                        handleDateRange(e.target.value)
                                    }
                                >
                                    <Radio
                                        className="text-[#455560]"
                                        value="Single"
                                    >
                                        Single
                                    </Radio>
                                    <Radio
                                        className="text-[#455560]"
                                        value="Multiple"
                                    >
                                        Multiple
                                    </Radio>
                                </Radio.Group>
                            </div>
                        </div>

                        {dateRange === DateRangeType.SINGLE ? (
                            <div className=" flex justify-between lg:w-[95%]  lg:flex-row lg:items-center @sm:flex-col">
                                <p className=" text-[13px] font-medium text-[#1A3353] lg:ms-2  @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                    Select Date
                                </p>
                                <DatePicker
                                    popupClassName="custom-datepicker"
                                    value={startDate}
                                    format="DD/MM/YYYY"
                                    onChange={(date) => {
                                        setStartDate(date);
                                        const value = form.getFieldsValue();
                                        const parsedServiceType = JSON.parse(
                                            value.serviceType
                                        );

                                        setStaffErrorMessage('');
                                        trainerList({
                                            date,
                                            serviceId:
                                                parsedServiceType.serviceId,
                                            subTypeId:
                                                parsedServiceType.appointmentId,
                                        });
                                        fetchRoomData({ date });
                                    }}
                                    className="lg:w-[80%] @sm:w-[100%]"
                                    style={{
                                        borderBottom: '1px solid #e5e7eb',
                                        borderRadius: '0px',
                                        backgroundColor: !serviceCategoryId
                                            ? '#f5f5f5'
                                            : undefined,
                                        color: !serviceCategoryId
                                            ? '#a0a0a0'
                                            : undefined,
                                        cursor: !serviceCategoryId
                                            ? 'not-allowed'
                                            : undefined,
                                    }}
                                    disabled={
                                        !serviceCategoryId ||
                                        (!isEdit && scheduleId)
                                    }
                                />
                            </div>
                        ) : (
                            <>
                                <div className="flex w-[100%] flex-col items-center">
                                    <div className=" lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="Start Date"
                                            name="startDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter start date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                format="DD/MM/YYYY"
                                                className=" lg:w-[95%] "
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor:
                                                        !serviceCategoryId
                                                            ? '#f5f5f5'
                                                            : undefined,
                                                    color: !serviceCategoryId
                                                        ? '#a0a0a0'
                                                        : undefined,
                                                    cursor: !serviceCategoryId
                                                        ? 'not-allowed'
                                                        : undefined,
                                                }}
                                                disabledDate={(currentDate) =>
                                                    currentDate &&
                                                    currentDate.isBefore(
                                                        dayjs().startOf('day')
                                                    )
                                                }
                                                onChange={(date) =>
                                                    updateAvailabilityTimeSlots(
                                                        date,
                                                        true,
                                                        dateRange
                                                    )
                                                }
                                                disabled={
                                                    !serviceCategoryId ||
                                                    (!isEdit && scheduleId)
                                                }
                                            />
                                        </Form.Item>
                                    </div>
                                    <div className="lg:w-[100%] @sm:w-full">
                                        <Form.Item
                                            label="End Date"
                                            name="endDate"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please enter end date',
                                                },
                                            ]}
                                        >
                                            <DatePicker
                                                popupClassName="custom-datepicker"
                                                placeholder="DD/MM/YYYY"
                                                format="DD/MM/YYYY"
                                                className="lg:w-[95%]"
                                                style={{
                                                    borderBottom:
                                                        '1px solid #e5e7eb',
                                                    borderRadius: '0px',
                                                    backgroundColor:
                                                        !serviceCategoryId
                                                            ? '#f5f5f5'
                                                            : undefined,
                                                    color: !serviceCategoryId
                                                        ? '#a0a0a0'
                                                        : undefined,
                                                    cursor: !serviceCategoryId
                                                        ? 'not-allowed'
                                                        : undefined,
                                                }}
                                                disabled={
                                                    !serviceCategoryId ||
                                                    (!isEdit && scheduleId)
                                                }
                                                // disabledDate={(currentDate) =>
                                                //     currentDate &&
                                                //     currentDate.isBefore(
                                                //         dayjs().startOf('day')
                                                //     )
                                                // }
                                                onChange={(date) =>
                                                    updateAvailabilityTimeSlots(
                                                        date,
                                                        false,
                                                        dateRange
                                                    )
                                                }
                                                disabledDate={(current) =>
                                                    current &&
                                                    current <
                                                        dayjs(
                                                            form.getFieldValue(
                                                                'startDate'
                                                            )
                                                        ).startOf('day')
                                                }
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                            </>
                        )}

                        <div className="pt-5">
                            {(dateRange !== DateRangeType.SINGLE ||
                                dateRange === DateRangeType.MULTIPLE) && (
                                <>
                                    <div className="flex flex-row items-center justify-between  pb-9 text-[13px] font-medium text-[#1A3353]">
                                        Select Days
                                        <div className=" flex flex-wrap lg:w-[80%] lg:gap-3  @xl:gap-8">
                                            {daysOfWeek?.map((day) => (
                                                <div className="">
                                                    <Button
                                                        shape="circle"
                                                        className={`p-2   ${
                                                            selectedDay?.includes(
                                                                day
                                                            )
                                                                ? 'bg-[#455560] text-white'
                                                                : 'bg-white'
                                                        }`}
                                                        onClick={() =>
                                                            handleDayClick(day)
                                                        }
                                                    >
                                                        {capitalizeFirstLetter(
                                                            day.slice(0, 3)
                                                        )}
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    <div className="mb-8 flex flex-row">
                                        <div className="w-[20%]"></div>
                                        <Switch
                                            checked={isChecked}
                                            onChange={() =>
                                                setIsChecked((prev) => !prev)
                                            }
                                        />
                                        <span className="ml-3 text-[14px] text-[#1A3353]">
                                            Duplicate for all days
                                        </span>
                                    </div>
                                </>
                            )}
                        </div>

                        {/* <div className=" flex justify-between lg:w-[95%]  lg:flex-row lg:items-center @sm:flex-col">
                            <p className=" text-[13px] font-medium text-[#1A3353] lg:ms-2  @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                Select Date
                            </p>
                            <DatePicker
                                popupClassName="custom-datepicker"
                                value={startDate}
                                format="DD/MM/YYYY"
                                onChange={(date) => {
                                    setStartDate(date);
                                    const value = form.getFieldsValue();
                                    const parsedServiceType = JSON.parse(
                                        value.serviceType
                                    );

                                    setStaffErrorMessage('');
                                    trainerList({
                                        date,
                                        serviceId: parsedServiceType.serviceId,
                                        subTypeId:
                                            parsedServiceType.appointmentId,
                                    });
                                    fetchRoomData({ date });
                                }}
                                className="lg:w-[80%] @sm:w-[100%]"
                                style={{
                                    borderBottom: '1px solid #e5e7eb',
                                    borderRadius: '0px',
                                    backgroundColor: !serviceCategoryId
                                        ? '#f5f5f5'
                                        : undefined,
                                    color: !serviceCategoryId
                                        ? '#a0a0a0'
                                        : undefined,
                                    cursor: !serviceCategoryId
                                        ? 'not-allowed'
                                        : undefined,
                                }}
                                disabled={
                                    !serviceCategoryId ||
                                    (!isEdit && scheduleId)
                                }
                            />
                        </div> */}

                        <div className=" mt-6 flex flex-row items-center bg-white">
                            <div className="flex justify-between  lg:flex-row  lg:items-center @sm:flex-col @lg:w-[21%] @xl:w-[19%] @2xl:w-[19%]">
                                <p className="  text-[13px] font-medium text-[#1A3353] lg:ms-2   lg:w-[65px] @sm:-ms-1.5 @sm:mb-3 @sm:w-[23%]">
                                    Start Time
                                </p>
                            </div>
                            <div className="flex flex-row items-center @2xl:w-[76%]">
                                <div className="w-[20%]">
                                    <TimePicker
                                        format="HH:mm"
                                        value={startTime}
                                        minuteStep={5}
                                        // className="lg:w-[80%] @sm:w-[100%]"
                                        onChange={handleStartTimeChange}
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: !serviceCategoryId
                                                ? '#f5f5f5'
                                                : undefined,
                                            color: !serviceCategoryId
                                                ? '#a0a0a0'
                                                : undefined,
                                            cursor: !serviceCategoryId
                                                ? 'not-allowed'
                                                : undefined,
                                        }}
                                        disabled={
                                            !serviceCategoryId ||
                                            (!isEdit && scheduleId)
                                        }
                                    />
                                </div>

                                <div className=" w-[38%] lg:translate-y-3 @xl:translate-x-3  ">
                                    <Form.Item
                                        className="PA-form-duration-input"
                                        label={
                                            <p className=" text-left  @sm:w-full">
                                                Duration
                                            </p>
                                        }
                                        name="duration"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select duration',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            className="ms-2 w-[90%] border-b-1"
                                            placeholder="Select Duration"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            onChange={handleDuration}
                                            options={durationOption}
                                            disabled={
                                                !serviceCategoryId ||
                                                (!isEdit && scheduleId)
                                            }
                                            style={{
                                                backgroundColor:
                                                    !serviceCategoryId
                                                        ? '#f5f5f5'
                                                        : undefined,
                                                color: !serviceCategoryId
                                                    ? '#a0a0a0'
                                                    : undefined,
                                                cursor: !serviceCategoryId
                                                    ? 'not-allowed'
                                                    : undefined,
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                                <div className=" flex w-[38%] justify-end lg:flex-row   lg:items-center @sm:flex-col @xl:gap-0 ">
                                    <p className="translate-x-3 font-medium text-[#1A3353] lg:w-[40%] @lg:text-[12px] @xl:w-[35%] @xl:text-[13px]">
                                        End Time
                                    </p>
                                    <TimePicker
                                        format="HH:mm"
                                        disabled
                                        value={endTime}
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                    />
                                </div>
                            </div>
                        </div>

                        <div className=" mt-6 flex w-full justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                {/* {role === RoleType.TRAINER ? (
                                    <Form.Item
                                        label="Staff"
                                        name="staffName"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select the Staff name',
                                            },
                                        ]}
                                    >
                                        <Input
                                            style={{
                                                borderBottom:
                                                    '1px solid #e5e7eb',
                                                borderRadius: '0px',
                                            }}
                                            defaultValue={store.user?.firstName}
                                            disabled
                                        />
                                    </Form.Item>
                                ) : ( */}
                                <Form.Item
                                    label={<p className="text-left">Staff</p>}
                                    name="staff"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Please select staff',
                                        },
                                    ]}
                                >
                                    <Select
                                        className="border-b-1"
                                        showSearch
                                        placeholder="Select instructor"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={staffOption}
                                        onChange={handleStaffChange}
                                        disabled={
                                            !serviceCategoryId ||
                                            (!isEdit && scheduleId)
                                        }
                                        style={{
                                            backgroundColor: !serviceCategoryId
                                                ? '#f5f5f5'
                                                : undefined,
                                            color: !serviceCategoryId
                                                ? '#a0a0a0'
                                                : undefined,
                                            cursor: !serviceCategoryId
                                                ? 'not-allowed'
                                                : undefined,
                                        }}
                                        optionLabelProp="name"
                                        // labelRender={(value) => {
                                        //     const selected = staffOption.find(opt => opt.value === value);
                                        //     return selected?.title || '';
                                        // }}
                                    />
                                </Form.Item>
                                {/* )} */}
                                {staffErrorMessage && (
                                    <>
                                        <div className="flex flex-row items-center">
                                            <div className=" @sm:flex-col @lg:w-[20%] @xl:w-[20%] @2xl:w-[20%]"></div>
                                            <div className=" -translate-y-3 text-lg font-semibold text-red-500">
                                                <InfoCircleOutlined className="text-red-500" />
                                                &nbsp;
                                                {staffErrorMessage}
                                            </div>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>

                        {/* <div className="">
                            <div className="flex w-full justify-between ">
                                <div className=" lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left">Client</p>
                                        }
                                        name="client"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select client',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            placeholder="Select Client"
                                            filterOption={(input, option) =>
                                                String(option?.label ?? '')
                                                    .toLowerCase()
                                                    .includes(
                                                        input.toLowerCase()
                                                    )
                                            }
                                            options={ClientOptions}
                                            onChange={handleClientChange}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="flex w-full justify-between lg:mt-5 ">
                                <div className="  lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left lg:w-[55px]">
                                                Phone
                                            </p>
                                        }
                                        name="phone"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select client',
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder="Phone Number"
                                            readOnly
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="flex w-full justify-between lg:mt-5 ">
                                <div className=" lg:w-[95%] @sm:w-full">
                                    <Form.Item
                                        label={
                                            <p className="text-left lg:w-[55px]">
                                                Email
                                            </p>
                                        }
                                        name="email"
                                        rules={[
                                            {
                                                required: false,
                                                message: 'Please select client',
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder="Email"
                                            type="email"
                                            readOnly
                                        />
                                    </Form.Item>
                                    <div className="lg:-mt-8 @sm:-mt-6">
                                <Form.Item
                                    label={''}
                                    name="sendConfirmation"
                                    valuePropName="checked"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select client',
                                        },
                                    ]}
                                >
                                    <Checkbox className="  text-lg lg:ps-[140px]">
                                        Send a confirmation
                                    </Checkbox>
                                </Form.Item>
                            </div>
                                </div>
                            </div>
                        </div> */}

                        <div className=" flex w-full justify-between ">
                            <div className="lg:w-[95%] @sm:w-full">
                                <Form.Item
                                    label={
                                        <p className=" text-left  @sm:w-full">
                                            Room
                                        </p>
                                    }
                                    name="roomId"
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select room',
                                        },
                                    ]}
                                >
                                    <Select
                                        showSearch
                                        className="border-b-1"
                                        placeholder="Select Room"
                                        filterOption={(input, option) =>
                                            String(option?.label ?? '')
                                                .toLowerCase()
                                                .includes(input.toLowerCase())
                                        }
                                        options={roomData}
                                        disabled={
                                            !serviceCategoryId ||
                                            (!isEdit && scheduleId)
                                        }
                                        style={{
                                            backgroundColor: !serviceCategoryId
                                                ? '#f5f5f5'
                                                : undefined,
                                            color: !serviceCategoryId
                                                ? '#a0a0a0'
                                                : undefined,
                                            cursor: !serviceCategoryId
                                                ? 'not-allowed'
                                                : undefined,
                                        }}
                                    />
                                </Form.Item>
                            </div>
                        </div>

                        <div className=" flex w-full justify-between lg:mb-4">
                            <div className=" lg:w-[95%] @sm:w-full">
                                <Form.Item label="Notes" name="notes">
                                    <TextArea
                                        style={{
                                            borderBottom: '1px solid #e5e7eb',
                                            borderRadius: '0px',
                                        }}
                                        autoSize={{ minRows: 1, maxRows: 2 }}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                </div>
                {(!scheduleId || (scheduleId && isEdit)) && (
                    <>
                        <div className="flex w-[100%] flex-row gap-5 pe-10 lg:justify-end @sm:justify-center">
                            <Form.Item>
                                <div
                                    className=""
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        loading={submitCheckInLoader}
                                        className="border border-[#1A3353] px-20 py-7 text-2xl"
                                        onClick={() =>
                                            onFinish(
                                                form.getFieldsValue(),
                                                true
                                            )
                                        }
                                    >
                                        Save & Check in
                                    </Button>
                                </div>
                            </Form.Item>
                            <Form.Item>
                                <div
                                    className=""
                                    style={{ display: 'flex', gap: '10px' }}
                                >
                                    <Button
                                        loading={submitLoader}
                                        className="bg-purpleLight px-20 py-7 text-2xl"
                                        type="primary"
                                        htmlType="submit"
                                    >
                                        Save
                                    </Button>
                                </div>
                            </Form.Item>
                        </div>
                    </>
                )}
            </Form>
        </ConfigProvider>
    );
};

export default BookAppointmentModal;
