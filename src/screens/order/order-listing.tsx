import { MoreOutlined } from '@ant-design/icons';
import {
    ConfigProvider,
    Dropdown,
    Menu,
    Pagination,
    Table,
    Modal,
    Form,
    Input,
    Button,
    Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import {
    ConfimredButtonChip,
    PendingButtonChip,
    RejectedButtonChip,
} from '~/components/common/chip-component';
import CustomTable from '~/components/common/customTable';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import {
    DownloadInvoice,
    OrderInvoiceList,
    markOrderAsPaid,
    orderExport,
} from '~/redux/actions/purchased-action';
import { ServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import {
    PaymentStatus,
    PERMISSIONS_ENUM,
    RoleType,
    SUBJECT_TYPE,
} from '~/types/enums';
import { formatDate } from '~/utils/formatDate';
import { getQueryParams } from '~/utils/getQueryParams';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import PaymentStatusModal from './paymetStatus-modal';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import Alertify from '~/services/alertify';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
const { TextArea } = Input;

const OrderListing = () => {
    const [form] = Form.useForm();

    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const [_, setLocation] = useLocation();
    const [loader, startLoader, endLoader] = useLoader(true);
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [search, setSearch] = useState(params.search);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [selectedServiceCategories, setSelectedServiceCategories] = useState(
        []
    );
    const [selectedCity, setSelectedCity] = useState<any>([]);
    const [selectedDateRange, setSelectedDateRange] = useState([]);
    const [selectePaymentStatus, setSelectedPaymentStatus] = useState<any>();
    const [isStatusUpdate, setUpdateSatus] = useState<any>();
    const [isConfirmModal, setIsConfirmModal] = useState<boolean>(false);
    const [currentRecord, setCurrentRecord] = useState<any>();
    const [voidPaymentModel, setVoidPaymentModel] = useState<boolean>(false);
    const [paymentStatus, setPaymentStatus] = useState<any>('');
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);
    const [requirePinValidation, setRequirePinValidation] = useState(false);

    const [invoiceId, setInvoiceId] = useState<any>('');
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPurchaseExportPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.ORDERS_ORDER_EXPORT &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PURCHASE_EXPORT
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        orderInvoiceList: state.purchased_store.orderInvoiceList,
        orderInvoiceCount: state.purchased_store.orderInvoiceCount,
    }));
    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }

    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 10 }))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    if (store.role !== RoleType.ORGANIZATION) {
                        const firstFacilityId = [
                            res?.data?.data?.list?.[0]?._id,
                        ];
                        setSelectedCity(firstFacilityId);
                    }
                }
            });
        dispatch(
            ServiceCategoryList({
                page: 1,
                pageSize: 30,
                // classType: 'bookings',
            })
        ).unwrap();
    }, []);

    // const handleClear = ()=>{

    // }

    const hasOrdersReadPermission = useMemo(() => {
        if (store.role === RoleType.ORGANIZATION) {
            return true;
        } else {
            return all_permissions_for_role?.some((module: any) =>
                module.subjects?.some(
                    (subject: any) =>
                        subject.type === SUBJECT_TYPE.ORDER &&
                        subject.actions?.some((action: any) =>
                            action.permissions?.some(
                                (permission: any) =>
                                    permission.type ===
                                    PERMISSIONS_ENUM.PURCHASE_INVOICE_READ
                            )
                        )
                )
            );
        }
    }, [all_permissions_for_role]);
    const hasOrdersActionPermission = useMemo(() => {
        if (store.role === RoleType.ORGANIZATION) {
            return true;
        } else {
            return all_permissions_for_role?.some((module: any) =>
                module.subjects?.some(
                    (subject: any) =>
                        subject.type === SUBJECT_TYPE.ORDER &&
                        subject.actions?.some((action: any) =>
                            action.permissions?.some(
                                (permission: any) =>
                                    permission.type ===
                                    PERMISSIONS_ENUM.PURCHASE_INVOICE_WRITE
                            )
                        )
                )
            );
        }
    }, [all_permissions_for_role]);
    // console.log('hasOrdersReadPermission', hasOrdersReadPermission);
    useEffect(() => {
        startLoader();

        if (!hasOrdersReadPermission) {
            // Clear the order list in the store
            dispatch({
                type: 'purchased/clearOrderInvoiceList',
            });
            endLoader();
            // console.log(
            //     "Sorry, you don't have the necessary permissions to perform this action"
            // );
            return Alertify.error(
                "Sorry, you don't have the necessary permissions to perform this action"
            );
        }

        const payload: Record<string, any> = {
            page: currentPage,
            pageSize: pageSizes,
            ...(search && { search: search }),
            ...(selectePaymentStatus && {
                paymentStatus: selectePaymentStatus,
            }),
            ...(selectedCity?.length > 0 && { facilityId: selectedCity }),
            ...(selectedServiceCategories?.length > 0 && {
                serviceCategory: selectedServiceCategories,
            }),
            ...(selectedDateRange?.length > 0 && {
                startDate: dayjs(selectedDateRange[0])
                    .startOf('day')
                    .toISOString(),
                endDate: dayjs(selectedDateRange[1]).endOf('day').toISOString(),
            }),
        };

        debouncedRequest(() => {
            dispatch(OrderInvoiceList(payload))
                .unwrap()
                .then(() => {})
                .finally(endLoader);
        });
    }, [
        currentPage,
        pageSizes,
        search,
        selectedCity,
        selectedDateRange,
        selectePaymentStatus,
        selectedServiceCategories,
        isStatusUpdate,
        voidPaymentModel,
    ]);

    const handleConfirmStatusChange = () => {
        const payload: Record<string, any> = {
            invoiceId: currentRecord._id,
            status: 'completed',
        };
        dispatch(markOrderAsPaid(payload))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    setUpdateSatus(res?.status);
                }
            })
            .finally(endLoader);
        setIsConfirmModal(false);
    };
    const handleCancelStatusChange = () => {
        setIsConfirmModal(false);
    };
    const handleDownloadInvoice = async (orderId: any) => {
        if (!orderId) return;

        try {
            const response = await dispatch(
                DownloadInvoice({ orderId })
            ).unwrap();

            if (!response) {
                throw new Error('No URL received from API.');
            }

            window.open(response, '_blank', 'noopener,noreferrer');
        } catch (error) {
            console.error('Error downloading invoice:', error);
        }
    };
    const columns: any = [
        {
            title: 'Order ID',
            dataIndex: '',
            align: 'center',
            key: 'orderId',
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
            render: (record: any) => {
                return (
                    <Link to={`/order-detail?orderId=${record?._id}`}>
                        {record?.orderId}
                    </Link>
                );
            },
        },
        {
            title: 'Client Name',
            dataIndex: '',
            key: 'userName',
            render: (record: any) => {
                return (
                    <Link to={`/order-detail?orderId=${record?._id}`}>
                        <p className="truncate-text cursor-pointer">
                            {record?.userName}
                        </p>
                    </Link>
                );
            },
        },
        {
            title: 'Location',
            dataIndex: 'facilityName',
            key: 'facilityName',
            // onCell: () => ({ style: { minWidth: 120, width: 120 } }),
            // width: 200,
        },
        {
            title: 'Order Date',
            dataIndex: '',
            // width: 150,
            key: 'invoiceDate',
            render: (record: any) => {
                return formatDate(record?.invoiceDate);
            },
        },
        {
            title: 'Payment Date',
            dataIndex: '',
            // width: 150,
            key: 'paymentDate',
            render: (record: any) => {
                return formatDate(record?.paymentDate);
            },
        },
        {
            title: 'Time',
            dataIndex: '',
            // width: 150,
            key: 'invoiceTime',
            render: (record: any) => {
                return dayjs(record?.invoiceDate).format('HH:mm A');
            },
        },
        {
            title: 'Amount',
            dataIndex: '',
            // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
            // width: 150,
            key: 'total',
            render: (record: any) => {
                return Math.floor(record?.total).toFixed(2);
            },
        },
        {
            title: 'Staff',
            dataIndex: '',
            key: 'createdByName',
            // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
            render: (record: any) => {
                return record?.createdByName ? record?.createdByName : '-';
            },
        },
        {
            title: 'Payment Method',
            align: 'center',
            dataIndex: 'updatedPaymentDetails',
            // width: 200,
            render: (text: string) => {
                const displayText = text?.trim() || '';

                return (
                    <Tooltip title={displayText}>
                        <div
                            style={{
                                whiteSpace: 'nowrap',
                                textAlign: 'center',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                maxWidth: 100,
                                cursor: 'pointer',
                            }}
                        >
                            {displayText}
                        </div>
                    </Tooltip>
                );
            },
        },

        {
            title: 'Payment Status',
            dataIndex: 'paymentStatus',
            key: 'paymentStatus',
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
            render: (text: string, record: any) => {
                const handlePendingClick = () => {
                    setCurrentRecord(record);
                    setIsConfirmModal(true);
                };
                switch (text?.toLowerCase()) {
                    case PaymentStatus.PENDING:
                        return (
                            <div
                                onClick={() => {
                                    if (hasOrdersActionPermission) {
                                        setLocation(
                                            `/payment?orderId=${record?._id}`
                                        );
                                    }
                                }}
                                className="cursor-pointer"
                            >
                                <PendingButtonChip />
                            </div>
                        );
                    case PaymentStatus.COMPLETED:
                        return <ConfimredButtonChip />;
                    case PaymentStatus.FAILED:
                        return <RejectedButtonChip />;
                    default:
                        return (
                            <button className="h-[20px] rounded bg-red-100 px-4 text-[13px] font-[600] text-red-500">
                                {text === 'refund' ? 'Refunded' : 'Cancelled'}
                            </button>
                        );
                }
            },
        },

        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
            render: (record: any) => {
                const markAsPaid = (record: any) => {
                    console.log(record);
                };
                const menuItems = [
                    ...(hasOrdersActionPermission &&
                    record.paymentStatus === 'completed'
                        ? [
                              {
                                  key: '1',
                                  label: 'Refund',
                                  onClick: () => {
                                      setVoidPaymentModel(true);
                                      setPaymentStatus('refund');
                                      setInvoiceId(record._id);
                                  },
                              },
                          ]
                        : []),
                    ...(record.paymentStatus === 'pending'
                        ? [
                              ...(hasOrdersActionPermission
                                  ? [
                                        {
                                            key: '2',
                                            label: 'Void Payment',
                                            onClick: () => {
                                                setVoidPaymentModel(true);
                                                setPaymentStatus('cancel');
                                                setInvoiceId(record._id);
                                            },
                                        },
                                    ]
                                  : []),
                              {
                                  key: '3',
                                  label: 'Complete Payment',
                                  onClick: () =>
                                      setLocation(
                                          `/payment?orderId=${record?._id}`
                                      ),
                              },
                          ]
                        : []),
                    ...(record.paymentStatus === 'completed'
                        ? [
                              {
                                  key: '1',
                                  label: 'Download Invoice',
                                  onClick: () => {
                                      handleDownloadInvoice(record._id);
                                  },
                              },
                          ]
                        : []),
                ];

                return (
                    <Dropdown menu={{ items: menuItems }} trigger={['click']}>
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    const handleExport = () => {
        if (requirePinValidation) {
            setRequirePinValidation(false);
        } else if (store.role !== RoleType.ORGANIZATION) {
            return;
        }
        const payload: Record<string, any> = {
            ...(selectePaymentStatus && {
                paymentStatus: selectePaymentStatus,
            }),
            ...(selectedCity?.length > 0 && { facilityId: selectedCity }),
            ...(selectedServiceCategories?.length > 0 && {
                serviceCategory: selectedServiceCategories,
            }),
            ...(selectedDateRange?.length > 0 && {
                startDate: selectedDateRange[0],
                endDate: selectedDateRange[1],
            }),
        };

        dispatch(orderExport(payload))
            .unwrap()
            .then((res: any) => {
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    const blob = new Blob([res.data], { type: 'text/csv' });
                    const url = window.URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute(
                        'download',
                        `order_export_${new Date().toISOString()}.csv`
                    );

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
            });
        setPinModalVisible(false);
    };

    const handleExportWithCheck = () => {
        if (store.role === RoleType.ORGANIZATION) {
            handleExport();
            return;
        }

        dispatch(GetSettingActiveStatus({ settingKey: 'settings_pin' }))
            .unwrap()
            .then((response: any) => {
                const settingData = response?.data?.data;
                const isEnabled = settingData?.isEnabled;
                const isActive = settingData?.isActive;

                if (isEnabled && isActive) {
                    setRequirePinValidation(true);
                    setPinModalVisible(true);
                } else {
                    handleExport();
                }
            });
    };

    const handleClose = () => {
        setPaymentStatus('');
        setInvoiceId('');
        setVoidPaymentModel(false);
    };
    return (
        <div>
            <CustomTable
                heading="Orders"
                search={search}
                showSearch={true}
                showDateRange={true}
                showExport={
                    hasPurchaseExportPermission ||
                    store.role === RoleType.ORGANIZATION
                }
                showStaffLocation={true}
                showServiceCategory={true}
                onSearch={handleSearch}
                showPaymentStatus={true}
                showAddButton={false}
                showClearButton={true}
                {...{
                    selectedCity,
                    setSelectedCity,
                    selectedServiceCategories,
                    setSelectedServiceCategories,
                    selectedDateRange,
                    setSelectedDateRange,
                    selectePaymentStatus,
                    setSelectedPaymentStatus,
                    handleExport: handleExportWithCheck,
                }}
            />
            <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                borderColor: '#0000001A',
                                cellFontSize: 13,
                                headerBg: '#fff',
                                headerColor: '#1A3353',
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Table
                        id=""
                        className="m-2 rounded-[6px] border-1 "
                        // pagination={false}
                        columns={columns}
                        dataSource={
                            hasOrdersReadPermission
                                ? store.orderInvoiceList.map((item: any) => ({
                                      ...item,
                                      key:
                                          item._id ||
                                          Math.random()
                                              .toString(36)
                                              .substr(2, 9),
                                  }))
                                : []
                        }
                        // scroll={{ x: 1700 }}
                        loading={loader}
                        pagination={false}
                    />
                    <div className="flex justify-center  py-10">
                        <Pagination
                            current={currentPage}
                            total={
                                hasOrdersReadPermission
                                    ? store.orderInvoiceCount
                                    : 0
                            }
                            pageSizeOptions={['10', '20', '50']}
                            pageSize={pageSizes}
                            onChange={paginate}
                            hideOnSinglePage
                        />
                    </div>
                </ConfigProvider>
            </div>
            <CommonConfirmationModal
                visible={isConfirmModal}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to Mark it as Paid?"
            />
            {voidPaymentModel && (
                <>
                    <PaymentStatusModal
                        visible={voidPaymentModel}
                        onClose={handleClose}
                        paymentStatus={paymentStatus}
                        invoiceId={invoiceId}
                    />
                </>
            )}
            {pinModalVisible && (
                <ModulePinConfirmationModal
                    visible={pinModalVisible}
                    onConfirm={handleExport}
                    onCancel={() => setPinModalVisible(false)}
                    module={SUBJECT_TYPE.ORDERS_ORDER_EXPORT}
                    subModule={PERMISSIONS_ENUM.PURCHASE_EXPORT}
                />
            )}
        </div>
    );
};

export default OrderListing;
