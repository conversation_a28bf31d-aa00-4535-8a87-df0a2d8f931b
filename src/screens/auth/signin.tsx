import React, { useEffect, useState } from 'react';
import { Form, Input, Button, ConfigProvider, Select } from 'antd';
import 'antd/dist/reset.css';
import Alertify from '~/services/alertify';
import { useDispatch } from 'react-redux';
import { LoginUser, organizationList } from '~/redux/actions/auth-actions';
import { useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import store, { AppDispatch } from '~/redux/store';
import { RoleType } from '~/types/enums';
import { useAppSelector } from '~/hooks/redux-hooks';
import { GetAllPermissions } from '~/redux/actions/permission-action';
import { useDebounce } from '~/hooks/useDebounce';

const { Item: FormItem } = Form;

interface SignInValues {
    email: string;
    password: string;
}

const SignIn: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [_, setLocation] = useLocation();
    const [loader, startLoader, endLoader] = useLoader();
    const [organizationOptions, setOrganizationOptions] = useState<any[]>([]);
    const [selectedOrgId, setSelectedOrgId] = useState<string | null>(null);
    const [orgFetching, setOrgFetching] = useState(false);
    const debouncedRequest = useDebounce((callback) => callback(), 300);

    const searchOrganizations = async (search: string) => {
        if (!search || search.length < 2) return;
        setOrgFetching(true);
        const payload = {
            search: search,
        };
        try {
            const res = await dispatch(organizationList({ payload })).unwrap();
            console.log('Res----------', res);
            setOrganizationOptions(
                res?.data?.data?.list?.map((org: any) => ({
                    label: org.organizationName,
                    value: org._id,
                })) || []
            );
        } catch (err) {
            Alertify.error('Failed to fetch organizations');
        } finally {
            setOrgFetching(false);
        }
    };

    const onFinish = (values: SignInValues) => {
        console.log('Success:', values);
        startLoader();
        const data = {
            email: values.email,
            type: 'email',
            password: values.password,
        };
        dispatch(LoginUser(data))
            .unwrap()
            .then((res: any) => {
                console.log('Res----------', res);
                // GET PERMISSIONS HERE
                // Get userId from auth store after login is successful
                const state = store.getState();
                const userId = state.auth_store.userId;
                const role = state.auth_store.role;
                console.log('Role is: ', role);
                if (
                    role != RoleType.ORGANIZATION &&
                    role != RoleType.SUPER_ADMIN
                ) {
                    dispatch(GetAllPermissions({ userId })).then(
                        (response: any) => {
                            const state = store.getState();
                            const all_permissions_for_role =
                                state.permission_store.all_permissions_for_role;
                            // console.log(
                            //     'All permissions for role:',
                            //     all_permissions_for_role
                            // );
                        }
                    );
                }

                if (res?.status === 200 || res?.status === 201) {
                    Alertify.success('Logged in successfully');
                    if (
                        res?.data?.data?.user?.role?.type ===
                        RoleType.ORGANIZATION
                    ) {
                        setLocation('/setup-checklist');
                    } else if (
                        res?.data?.data?.user?.role?.type ===
                        RoleType.SUPER_ADMIN
                    ) {
                        setLocation('/organizations');
                    } else if (
                        res?.data?.data?.user?.role?.type === RoleType.TRAINER
                    ) {
                        // setLocation('/appointments');
                        setLocation('/dashboard');
                    } else {
                        setLocation('/dashboard');
                    }
                }
            })
            .finally(endLoader);
    };

    return (
        <div className="h-screen  lg:flex">
            <div
                className="w-1/2 @sm:hidden"
                style={{
                    background: 'linear-gradient(to bottom, #5B2F93, #8143D1)',
                }}
            >
                <div
                    className="flex h-full w-full self-center"
                    style={{
                        background:
                            'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%) center no-repeat',
                    }}
                >
                    <img
                        src="/icons/hopfit-logo.svg"
                        alt="Sign In"
                        className="mx-auto w-[50%]"
                    />
                </div>
            </div>
            <div className="  lg:w-[50%] lg:px-40 @sm:h-screen  @sm:px-10 ">
                <div className="flex h-full w-full flex-col justify-center  ">
                    <div className="lg:hidden">
                        <img
                            // src="/menuIcons/logo_svg.svg "
                            src="menuIcons/logo.svg"
                            alt="Sign In"
                            className="mx-auto w-[40%]  @sm:py-10"
                        />
                    </div>
                    <h2 className="text-4xl font-bold text-[#1A3353] lg:mb-6 @sm:py-8 @sm:text-center">
                        Welcome
                    </h2>
                    <h2 className="mb-10 text-3xl text-[#455560] lg:font-medium @sm:text-center">
                        Login to your Account
                    </h2>
                    <ConfigProvider
                        theme={{
                            components: {
                                Input: {
                                    activeBg: 'transparent',
                                    hoverBorderColor: '#E6EBF1',
                                },
                            },
                        }}
                    >
                        <Form
                            form={form}
                            name="sign_in"
                            onFinish={onFinish}
                            layout="vertical"
                            // className="space-y-4"
                        >
                            {/* <FormItem
                                label={<p className="">Organization</p>}
                                className="@sm:w-full"
                                name="organizationId"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please select an organization!',
                                    },
                                ]}
                            >
                                <Select
                                    showSearch
                                    placeholder="Search and select organization"
                                    className="h-16 rounded-md"
                                    filterOption={false}
                                    onSearch={searchOrganizations}
                                    onChange={(value) =>
                                        setSelectedOrgId(value)
                                    }
                                    options={organizationOptions}
                                    loading={orgFetching}
                                />
                            </FormItem> */}

                            <FormItem
                                label={<p className="">Email Address</p>}
                                className="@sm:w-full"
                                name="email"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter your email!',
                                    },
                                    {
                                        type: 'email',
                                        message:
                                            'The input is not valid E-mail!',
                                    },
                                ]}
                            >
                                <div className="">
                                    <Input
                                        name="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem
                                label={<p className="">Password</p>}
                                name="password"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter your password!',
                                    },
                                ]}
                            >
                                <div className="">
                                    <Input.Password
                                        placeholder="Enter your password"
                                        className="h-16 rounded-md"
                                    />
                                </div>
                            </FormItem>
                            <FormItem>
                                <Button
                                    type="primary"
                                    loading={loader}
                                    htmlType="submit"
                                    className="mt-10  w-full border-0 bg-[#8143d1] py-8  text-xl  text-[#ffffff] @sm:w-full"
                                >
                                    Login
                                </Button>
                            </FormItem>
                            <div className="flex justify-between">
                                <FormItem>
                                    {/* <p className=" text-[#455560]">
                                        Don't have an account?{' '}
                                        <span
                                            className="cursor-pointer text-[#8143d1]"
                                            onClick={() =>
                                                setLocation('/signup')
                                            }
                                        >
                                            Sign Up
                                        </span>
                                    </p> */}
                                </FormItem>
                                <FormItem>
                                    <a
                                        href="/reset-password"
                                        className=" text-[#455560]"
                                    >
                                        Forgot password?
                                    </a>
                                </FormItem>
                            </div>
                        </Form>
                    </ConfigProvider>
                </div>
            </div>
        </div>
    );
};

export default SignIn;
