import React, { useEffect, useState } from 'react';
import { Form, Input, Button, ConfigProvider } from 'antd';
import 'antd/dist/reset.css';
import { useDispatch } from 'react-redux';
import { useLocation } from 'wouter';
import { getQueryParams } from '~/utils/getQueryParams';
import { SetNewPassword } from '~/redux/actions/auth-actions';
import { useLoader } from '~/hooks/useLoader';
import { AppDispatch } from '~/redux/store';
import PasswordChecklist from '~/components/common/password-checklist';

const { Item: FormItem } = Form;

interface SignInValues {
    password: string;
    confirmPassword: string;
}

const SetPasswordScreen: React.FC = () => {
    const [form] = Form.useForm();
    const [password, setPassword] = useState(undefined);
    const dispatch = useDispatch<AppDispatch>();
    const [_, setLocation] = useLocation();
    const params = getQueryParams();
    const [loader, startLoader, endLoader] = useLoader();

    const { email, firstName, lastName, role, id, uid } = params;
    console.log('Role is : ', role, id, uid);

    const onFinish = (values: SignInValues) => {
        // startLoader();
        const data = {
            // token: token,
            uid: Number(uid),
            id: id,
            email: email,
            password: values.password,
            // confirmPassword: values.confirmPassword,
        };
        console.log('Success:', data);
        // if (token) {
        dispatch(SetNewPassword(data))
            .unwrap()
            .then((res: any) => {
                console.log('Res----------', res);
                if (
                    res?.response?.status === 200 ||
                    res?.response?.status === 201
                ) {
                    setLocation('/signin');
                }
            })
            .finally(endLoader);
        // }
    };

    return (
        <div className="h-full lg:flex">
            <div
                className="w-1/2 @sm:hidden"
                style={{
                    background: 'linear-gradient(to bottom, #5B2F93, #8143D1)',
                }}
            >
                <div
                    className="flex h-full w-full self-center"
                    style={{
                        background:
                            'radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 60%) center no-repeat',
                    }}
                >
                    <img
                        // src="/assets/knox-logo.png"
                        src="/icons/hopfit-logo.svg"
                        alt="Sign In"
                        className="mx-auto w-[50%]"
                    />
                </div>
            </div>

            <div className="my-auto flex h-full items-center overflow-y-auto p-8 px-[6%] lg:w-1/2">
                <div className="flex h-full w-full flex-col justify-center p-6">
                    <div className="lg:hidden">
                        <img
                            src="/assets/knox-logo.png"
                            alt="Sign In"
                            className="mx-auto w-[40%]  @sm:pb-10"
                        />
                    </div>
                    <h2 className="text-4xl font-bold text-[#455560] @sm:text-center">
                        Set Password
                    </h2>
                    <h2 className="py-5 text-xl text-[#455560] @sm:text-center @sm:text-2xl">
                        Choose a new password for your account
                    </h2>
                    <ConfigProvider
                        theme={{
                            components: {
                                Input: {
                                    activeBg: 'transparent',
                                },
                                Form: {
                                    itemMarginBottom: 22,
                                    verticalLabelMargin: -3,
                                },
                            },
                        }}
                    >
                        <Form
                            form={form}
                            name="sign_in"
                            onFinish={onFinish}
                            initialValues={{ email, firstName, lastName }}
                            layout="vertical"
                            className="flex flex-col gap-2.5  py-5"
                        >
                            {role !== 'organization' ? (
                                <>
                                    <Form.Item
                                        name="firstName"
                                        label="First Name"
                                        rules={[{ required: true }]}
                                    >
                                        <Input
                                            className="h-16 rounded-md"
                                            placeholder="Enter your first name"
                                            disabled
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        name="lastName"
                                        label="Last Name"
                                        rules={[{ required: true }]}
                                    >
                                        <Input
                                            className="h-16 rounded-md"
                                            placeholder="Enter your last name"
                                            disabled
                                        />
                                    </Form.Item>
                                </>
                            ) : (
                                <></>
                            )}
                            <Form.Item
                                name="email"
                                label="Email"
                                rules={[{ required: true }]}
                            >
                                <Input
                                    className="h-16 rounded-md"
                                    placeholder="Enter your email"
                                    disabled
                                />
                            </Form.Item>
                            <Form.Item
                                label="Password"
                                name="password"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter your password!',
                                    },
                                ]}
                            >
                                <Input.Password
                                    onChange={(e: any) =>
                                        setPassword(e.target.value)
                                    }
                                    className="h-16 rounded-md"
                                    placeholder="Enter your password"
                                />
                            </Form.Item>
                            <Form.Item
                                label="Confirm Password"
                                name="confirmPassword"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please confirm your password!',
                                    },
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (
                                                !value ||
                                                getFieldValue('password') ===
                                                    value
                                            ) {
                                                return Promise.resolve();
                                            }
                                            return Promise.reject(
                                                new Error(
                                                    'Passwords do not match!'
                                                )
                                            );
                                        },
                                    }),
                                ]}
                            >
                                <Input.Password
                                    className="h-16 rounded-md"
                                    placeholder="Enter your confirm password"
                                />
                            </Form.Item>
                            <Form.Item>
                                <Button
                                    loading={loader}
                                    type="primary"
                                    htmlType="submit"
                                    className="mt-3 h-16 w-full border-0 bg-[#8143d1] text-xl font-semibold text-[#ffffff] @sm:w-full"
                                >
                                    Continue
                                </Button>
                            </Form.Item>
                        </Form>
                    </ConfigProvider>
                    <PasswordChecklist password={password} />
                </div>
            </div>
        </div>
    );
};

export default SetPasswordScreen;
