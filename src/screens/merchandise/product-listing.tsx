import { MoreOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';

import {
    ConfigProvider,
    Dropdown,
    Switch,
    Table,
    Menu,
    Pagination,
} from 'antd';
import { Link } from 'wouter';
import CustomTable from '~/components/common/customTable';
import { useLoader } from '~/hooks/useLoader';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';
import {
    ProductListData,
    updateProductStatus, DeleteProduct, ExportProduct,
    BulkUploadProduct, bulkupdateProduuct
} from '~/redux/actions/merchandise/product-action';
import { useAppSelector } from '~/hooks/redux-hooks';
import { navigate } from 'wouter/use-location';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import DeleteModal from '~/components/common/deleteModal';
import BulkUploadModal from './bulk-importModal';
import Alertify from '~/services/alertify';
import ErrorTableModal from './productimportErrorModal';
const ProductListing = () => {
    const dispatch = useDispatch<AppDispatch>();
    const params = getQueryParams();
    const [loader] = useLoader(true);
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [search, setSearch] = useState(params.search);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [currentProduct, setCurrentProduct] = useState(null);
    const [isDeleteModalVisible, setDeleteIsModalVisible] = useState(false);
    const [productId, setProductId] = useState(null);
    const [addBulkModal, setAddBulkModal] = useState(false);
    const [errorModalVisible, setErrorModalVisible] = useState(false);
    const [error_data, setErrorData] = useState([]);

    const [bulk_upload_loader, setBulkUploadLoader] = useState(false);
    const [csvFile, setCsvFile] = useState<File | null>(null);
    const [resetFile, setResetFile] = useState(false);


    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    function handleSearch(value: string) {
        console.log(value, 'value');
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }
    useEffect(() => {
        console.log(search);
        const payload = {
            search,
            page: Number(currentPage),
            pageSize: Number(pageSizes),
        };
        dispatch(ProductListData(payload)).unwrap();
    }, [currentPage, pageSizes, search, confirmationModalVisible, isDeleteModalVisible, errorModalVisible, addBulkModal]);
    const store = useAppSelector((state: any) => ({
        productList: state.product_store.productList,
        productListCount: state.product_store.productListCount,
    }));
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };
    const handleStatusChange = (record: any) => {
        setCurrentProduct(record);
        setConfirmationModalVisible(true);
    };
    const columns = [
        {
            title: 'Product Name ',
            dataIndex: '',
            key: '',
            render: (record: any) => (
                <Link to={`/create-product/${record._id}?view=true`}>
                    {record.name}
                </Link>
            ),
        },
        {
            title: 'SKU',
            dataIndex: 'sku',
        },
        {
            title: 'Type',
            dataIndex: 'type',
        },
        {
            title: 'Category',
            dataIndex: 'category',
        },
        {
            title: 'HSN',
            dataIndex: 'hsn',
        },
        { title: 'GST', dataIndex: 'gst' },
        {
            title: 'Status',
            dataIndex: '',
            key: '',
            render: (record: any) => {
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    colorPrimaryBorder: '#8143D1',
                                    colorPrimary: '#8143D1',
                                    colorTextQuaternary: 'gray',
                                    colorFillQuaternary: 'gray',
                                },
                            },
                        }}
                    >
                        <Switch
                            id="swtich-off"
                            checkedChildren="ON"
                            unCheckedChildren="OFF"
                            checked={record?.status}
                            onChange={() => handleStatusChange(record)}
                        />
                    </ConfigProvider>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                const menu = (
                    <Menu>
                        <Menu.Item
                            key="1"
                            onClick={() =>
                                navigate(`/create-product/${record._id}`)
                            }
                        >
                            Edit
                        </Menu.Item>
                        <Menu.Item key="2" onClick={() => {
                            setDeleteIsModalVisible(true);
                            setProductId(record._id);
                        }}>Delete</Menu.Item>
                    </Menu>
                );

                return (
                    <Dropdown
                        overlay={menu}
                        trigger={['click']}
                        visible={openDropdownKey === record.key}
                        onOpenChange={(visible) => {
                            setOpenDropdownKey(visible ? record.key : null);
                        }}
                    >
                        <MoreOutlined
                            style={{
                                fontSize: '20px',
                                cursor: 'pointer',
                            }}
                        />
                    </Dropdown>
                );
            },
        },
    ];

    const handleConfirmStatusChange = async () => {
        if (currentProduct) {
            try {
                await dispatch(
                    updateProductStatus({
                        productId: currentProduct._id,
                        status: !currentProduct.status,
                    })
                ).unwrap();

                // Fetch updated product list
                dispatch(
                    ProductListData({
                        search,
                        page: currentPage,
                        pageSize: pageSizes,
                    })
                ).unwrap();
            } catch (error) {
                console.error('Error updating product status', error);
            }
        }
        setConfirmationModalVisible(false);
    };

    const handleCancelStatusChange = () => {
        setConfirmationModalVisible(false);
    };

    const deleteProduct = async () => {
        if (!productId) return;
        try {
            await dispatch(DeleteProduct(productId)).unwrap();

            // setSubAttributeList((prevList: any) => prevList.filter((item: any) => item._id !== attributeId));
        } catch (error) {
            console.error("Error deleting attribute:", error);
        } finally {
            setDeleteIsModalVisible(false);

        }
    };
    const handleCSVFile = (file: File) => {
        const normalizeCsvFileType = (file: File): File => {
            const extension = file.name.split('.').pop()?.toLowerCase();
            const correctedType = extension === 'csv' ? 'text/csv' : file.type;

            return new File([file], file.name, {
                type: correctedType,
                lastModified: file.lastModified,
            });
        };
        const normalizedFile = normalizeCsvFileType(file);
        if (normalizedFile.type !== "text/csv") {
            console.error("Invalid file type. Please upload a CSV file.");
            return;
        }

        setCsvFile(normalizedFile);
    };



    const bulkUpload = async (confirmUpdate: boolean) => {
        try {

            if (!csvFile) {
                Alertify.error("Please upload a CSV file before submitting.");
                return;
            }
            setBulkUploadLoader(true);
            const formData = new FormData();
            formData.append("productFile", csvFile);
            if (confirmUpdate) {
                await dispatch(bulkupdateProduuct(formData)).unwrap().then((res: any) => {
                    console.log(res?.data?.errors, "bulk update reswponse")
                    if (res) {
                        Alertify.success("Bulk update successful");
                        if (res?.data?.uploadReport.errors.length > 0) {
                            setErrorData(res?.data?.uploadReport.errors);
                            setErrorModalVisible(true);
                        }

                    }
                })
            } else {
                await dispatch(BulkUploadProduct(formData)).unwrap().then((res: any) => {
                    console.log(res?.data?.uploadReport, "bulk upload response")
                    if (res) {
                        Alertify.success("Bulk upload successful");
                        if (res?.data?.uploadReport.errors.length > 0) {
                            setErrorData(res?.data?.uploadReport.errors);
                            setErrorModalVisible(true);
                        }

                    }
                })
            }

        } catch (err) {
            console.error(err);
        } finally {
            setBulkUploadLoader(false);
            setAddBulkModal(false); // close modal
            setCsvFile(null); // reset file
            setResetFile(true);
        }
    };

    const handleExport = async () => {
        const payload = {
            fileType: 'csv',
            productType: 'both',
            responseType: 'stream',
        };
        await dispatch(ExportProduct(payload)).unwrap();
    }
    return (
        <div>
            <CustomTable
                heading="Products"
                search={search}
                showSearch={true}
                onSearch={handleSearch}
                addNewTitle="Add Products"
                // showProductScreenButtons={true}
                addNewLink={`/create-product/0`}
                bulkImport={true}
                onBulkImportClick={() => setAddBulkModal(true)}
                showExport={true}
                handleExport={handleExport}
            />
            <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                borderColor: '#0000001A',
                                cellFontSize: 13,
                                headerBg: '#fff',
                                headerColor: '#1A3353',
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Table
                        id=""
                        className="m-2 overflow-x-auto rounded-[6px] border-1 "
                        columns={columns}
                        dataSource={store.productList}
                        pagination={false}
                    />
                </ConfigProvider>
            </div>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={store.productListCount}
                    pageSize={pageSizes}
                    onChange={handlePageChange}
                    hideOnSinglePage
                />
            </div>
            <CommonConfirmationModal
                visible={confirmationModalVisible}
                onConfirm={handleConfirmStatusChange}
                onCancel={handleCancelStatusChange}
                message="Are you sure you want to change the status?"
            />
            {isDeleteModalVisible && (
                <DeleteModal
                    title="Confirm Delete"
                    message={`Do you want to delete this Product?`}
                    isVisible={isDeleteModalVisible}
                    onDelete={deleteProduct}
                    onCancel={() => {
                        setDeleteIsModalVisible(false);
                    }}
                />
            )}
            <BulkUploadModal
                open={addBulkModal}
                onClose={() => setAddBulkModal(false)}
                handleCSVFile={handleCSVFile}
                error_data={error_data}
                bulkUpload={bulkUpload}
                bulk_upload_loader={bulk_upload_loader}
                resetFile={resetFile}
                setResetFile={setResetFile}
            />
            {
                errorModalVisible && (

                    <ErrorTableModal
                        visible={errorModalVisible}
                        data={error_data}
                        onClose={() => setErrorModalVisible(false)}
                    />
                )
            }
        </div>
    );
};

export default ProductListing;