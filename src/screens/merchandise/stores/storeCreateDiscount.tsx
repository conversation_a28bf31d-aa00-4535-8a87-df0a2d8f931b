import {
    Button,
    ConfigProvider,
    Form,
    Input,
    Modal,
    Radio,
    RadioChangeEvent,
    Select,
    Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import {
    CreateDiscount,
    DiscountList,
    UpdateDiscount,
} from '~/redux/actions/createDiscountAction';
import Alertify from '~/services/alertify';

const { Text } = Typography;

const InventoryCreateDiscountModal = (props: any) => {
    const [form] = Form.useForm();
    // const [selectedPackages, setSelectedPackages] = useState<string[]>([]);
    const dispatch = useDispatch();
    // const [pricingList, setPricingList] = useState<any[]>([]);
    const [discountTypeState, setDiscountTypeState] = useState('flat');
    const [discountValueState, setDiscountValueState] = useState(0);
    // Reset function to clear all states
    const resetAll = () => {
        form.resetFields();
        setDiscountTypeState('flat');
        setDiscountValueState(0);
        if (props.setDiscountDetails) {
            props.setDiscountDetails(null);
        }
    };
    useEffect(() => {
        if (props.newDiscountModal && props.discountDetails) {
            // Populate form with discount details
            form.setFieldsValue({
                ...props.discountDetails,
                // pricingPackages: props.discountDetails.items?.map(
                //     (item: any) => item.itemId
                // ),
            });
            setDiscountTypeState(props.discountDetails.type || 'percentage');
            setDiscountValueState(props.discountValueState || 0);
        }
    });

    return (
        <Modal
            open={props.newDiscountModal}
            closable={true}
            centered
            onCancel={() => {
                props.setNewDiscountModal(false);
                props.setEditId('');
                props.setEdit(false);

                resetAll();
            }}
            title={props.edit ? 'Edit Discount' : 'Create Discount'}
            footer={[
                <Button
                    key="save"
                    className="bg-purpleLight text-white"
                    onClick={() => {
                        const currentValues = form.getFieldsValue();
                        const formData = {
                            name: currentValues.name,
                            type: currentValues.type,
                            facilityIds: [props.storeId],
                            // items:currentValues.items,
                            value: Number(currentValues.value),
                            itemType: 'product',
                            target: currentValues.target,
                        };
                        form.setFieldsValue(formData);
                        form.validateFields()
                            .then((values: any) => {
                                // Use allSelectedItems instead of transforming pricingPackages
                                // const formData = { currentValues };
                                if (!props.edit) {
                                    dispatch(CreateDiscount(formData))
                                        .unwrap()
                                        .then((response: any) => {
                                            Alertify.success(
                                                'Discount created successfully'
                                            );
                                            props.setNewDiscountModal(false);
                                            resetAll();
                                            dispatch(
                                                DiscountList({
                                                    page: 1,
                                                    perPage: 10,
                                                    itemType: 'product',
                                                    includedPricingIds:
                                                        props.storeId,
                                                })
                                            )
                                                .then((response: any) => {
                                                    const data =
                                                        response?.payload?.data?.data.map(
                                                            (item: any) => ({
                                                                ...item,
                                                                key: item._id,
                                                            })
                                                        );
                                                    props.setDataSource(data);
                                                    props.setTotalItems(
                                                        response?.payload?.data
                                                            ?._metadata
                                                            ?.pagination
                                                            ?.total || 0
                                                    );
                                                })
                                                .catch((error: any) => {
                                                    console.log(
                                                        'Error in fetch:',
                                                        error
                                                    );
                                                });
                                        })
                                        .catch((error: any) => {
                                            console.log(
                                                'Error in creating::::',
                                                error
                                            );
                                            Alertify.error(error.message[0]);
                                        });
                                } else {
                                    dispatch(
                                        UpdateDiscount({
                                            id: props.editId,
                                            formData,
                                        })
                                    )
                                        .unwrap()
                                        .then((response: any) => {
                                            Alertify.success(
                                                'Discount updated successfully'
                                            );
                                            props.setNewDiscountModal(false);
                                            props.setEditId('');
                                            props.setEdit(false);
                                            resetAll();
                                            dispatch(
                                                DiscountList({
                                                    page: 1,
                                                    perPage: 10,
                                                    itemType: 'product',
                                                    includedPricingIds:
                                                        props.storeId,
                                                })
                                            )
                                                .then((response: any) => {
                                                    const data =
                                                        response?.payload?.data?.data.map(
                                                            (item: any) => ({
                                                                ...item,
                                                                key: item._id,
                                                            })
                                                        );
                                                    props.setDataSource(data);
                                                    props.setTotalItems(
                                                        response?.payload?.data
                                                            ?._metadata
                                                            ?.pagination
                                                            ?.total || 0
                                                    );
                                                })
                                                .catch((error: any) => {
                                                    console.log(
                                                        'Error in fetch:',
                                                        error
                                                    );
                                                });
                                        })
                                        .catch((error: any) => {
                                            console.log(
                                                'Error in update::::',
                                                error
                                            );
                                            Alertify.error(error.message[0]);
                                        });
                                }
                            })
                            .catch((info: any) => {
                                console.log('Validate Failed:', info);
                            });
                    }}
                >
                    Save
                </Button>,
                <Button
                    key="cancel"
                    onClick={() => {
                        props.setNewDiscountModal(false);
                        props.setEditId('');
                        props.setEdit(false);

                        resetAll();
                    }}
                >
                    Cancel
                </Button>,
            ]}
            width={800}
        >
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            itemMarginBottom: 0,
                        },
                    },
                }}
            >
                <Form
                    form={form}
                    layout="horizontal"
                    size="large"
                    preserve={false}
                    className="create-discount-form"
                >
                    <div className="mb-8 flex w-full items-center">
                        <Text className="w-[20%] font-medium text-[#1a3353]">
                            Discount Name
                        </Text>

                        <div className="w-[80%]">
                            <Form.Item
                                // label="Discount Name"
                                name="name"
                                style={{ width: '100%', marginBottom: 0 }}
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter discount name',
                                    },
                                ]}
                            >
                                <Input placeholder="Discount Name" />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="mb-8 flex w-full items-center">
                        <Text className="w-[20%] font-medium text-[#1a3353]">
                            Discount
                        </Text>

                        <div className="flex w-[80%] gap-8">
                            <Form.Item
                                name="type"
                                className="w-1/3"
                                initialValue="percentage"
                            >
                                <Select
                                    onChange={() => {
                                        setDiscountTypeState(
                                            form.getFieldValue('type')
                                        );
                                    }}
                                    disabled
                                    options={[
                                        // {
                                        //     label: 'Flat',
                                        //     value: 'flat',
                                        // },
                                        {
                                            label: 'Percentage',
                                            value: 'percentage',
                                        },
                                    ]}
                                />
                            </Form.Item>

                            <Form.Item
                                name="value"
                                className="w-2/3"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter discount value',
                                    },
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (!value || value < 0) {
                                                return Promise.reject(
                                                    'Value cannot be negative'
                                                );
                                            }
                                            if (
                                                getFieldValue('type') ===
                                                    'percentage' &&
                                                value > 100
                                            ) {
                                                return Promise.reject(
                                                    'Percentage cannot exceed 100'
                                                );
                                            }
                                            if (discountTypeState === 'flat') {
                                                setDiscountValueState(value);
                                            } else {
                                                setDiscountValueState(0);
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <Input
                                    type="number"
                                    min={0}
                                    max={
                                        form.getFieldValue('type') ===
                                        'percentage'
                                            ? 100
                                            : undefined
                                    }
                                    placeholder="Enter discount value"
                                />
                            </Form.Item>
                        </div>
                    </div>

                    <Text className="mb-0 font-medium text-[#1a3353]">
                        Can Apply this discount to
                    </Text>
                    <Form.Item name="target" initialValue="all_users">
                        <Radio.Group>
                            <Radio value="all_users">All Users</Radio>
                            <Radio value="members_only">Members</Radio>
                        </Radio.Group>
                    </Form.Item>
                </Form>
            </ConfigProvider>
        </Modal>
    );
};

export default InventoryCreateDiscountModal;
