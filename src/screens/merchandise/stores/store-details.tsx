import { MoreOutlined } from '@ant-design/icons';
import {
    Checkbox,
    ConfigProvider,
    Dropdown,
    Table,
    Tag,
    Pagination,
    Menu,
} from 'antd';
import { useState, useEffect } from 'react';
import { useParams, useLocation } from 'wouter';
import CustomTable from '~/components/common/customTable';
import { useLoader } from '~/hooks/useLoader';
import SkuInventoryModal from '~/screens/merchandise/stores/sku-inventory-modal';
import { getQueryParams } from '~/utils/getQueryParams';
import {
    InventoryList,
    _ManipulateInventoryList,
} from '~/redux/actions/merchandise/inventory-action';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import { FacilityDetails } from '~/redux/actions/facility-action';
import dayjs from 'dayjs';

const StoreDetail = () => {
    const dispatch = useDispatch<AppDispatch>();
    const params = getQueryParams();
    const [, setLocation] = useLocation();
    const [loader] = useLoader(true);

    const pageParam = parseInt(params.page, 10) || 1;
    const pageSizeParam = parseInt(params.pageSize, 10) || 10;
    const searchParam = params.search || '';

    const [search, setSearch] = useState(searchParam);
    const [currentPage, setCurrentPage] = useState(pageParam);
    const [pageSize, setPageSize] = useState(pageSizeParam);
    const [modalVisible, setModalVisible] = useState(false);

    const { storeId } = useParams<{ storeId: string }>();
    const [inventoryList, setInventoryList] = useState([]);
    const [inventoryCount, setInventoryCount] = useState<number>(0);
    const [storeName, setStoreName] = useState<string>('');
    const [productType, setProductType] = useState<string>('');
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [inventoryDetail, setInventoryDetail] = useState<any>(null);

    const showModal = () => setModalVisible(true);
    const handleClose = () => {
        setModalVisible(false);
        setInventoryDetail(null);
    };

    const handleSearch = (value: string) => {
        setSearch(value);
        setCurrentPage(1);
        setPageSize(10);
    };
    useEffect(() => {
        dispatch(FacilityDetails({ facilityId: storeId }))
            .unwrap()
            .then((res: any) => {
                setStoreName(res.data.data.facilityName);
            });
    }, []);
    useEffect(() => {
        dispatch(
            InventoryList({
                page: currentPage,
                pageSize,
                storeId,
                search,
                productType,
            })
        )
            .unwrap()
            .then((res: any) => {
                const inventoryDetails = _ManipulateInventoryList(
                    res.data.list
                );
                setInventoryCount(res.data.count);
                setInventoryList(inventoryDetails);
            });
    }, [
        dispatch,
        currentPage,
        pageSize,
        search,
        storeId,
        productType,
        modalVisible,
    ]);
    const handleInventoryEdit = (record: any) => {
        console.log(record);
        setInventoryDetail(record);
        showModal();
    };
    const columns = [
        {
            title: 'SKU',
            dataIndex: 'sku',
            key: 'sku',
        },
        {
            title: 'Product Name',
            dataIndex: 'name',
        },
        {
            title: 'Type',
            dataIndex: 'productType',
        },

        {
            title: 'Inventory',
            dataIndex: 'quantity',
        },
        {
            title: 'HSN',
            dataIndex: 'hsn',
        },
        {
            title: 'GST %',
            dataIndex: 'tax',
        },
        {
            title: 'MRP',
            dataIndex: 'mrp',
        },
        {
            title: 'Sale Price',
            dataIndex: 'salePrice',
        },
        {
            title: 'Discount Price',
            dataIndex: 'price',
        },
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                let color = 'yellow';
                let statusText = 'Low';

                if (record.quantity === 0) {
                    color = 'red';
                    statusText = 'Out of Stock';
                } else if (record.quantity > 100) {
                    color = 'green';
                    statusText = 'Available';
                } else if (record.quantity < 50) {
                    color = 'yellow';
                    statusText = 'Low';
                }

                return <Tag color={color}>{statusText}</Tag>;
            },
        },
        {
            title: 'Expiry Date',
            dataIndex: '',
            key: 'expiry',
            render: (record: any) => {
                return (
                    <div>
                        {record?.expiryDate &&
                            dayjs(record?.expiryDate).format('YYYY-MM-DD')}
                    </div>
                );
            },
        },
        {
            title: 'Action',
            dataIndex: '',
            key: 'action',
            render: (record: any) => {
                return (
                    <>
                        <span className="flex gap-2">
                            <div
                                className="cursor-pointer"
                                onClick={() => handleInventoryEdit(record)}
                            >
                                <img
                                    src="/icons/common/edit.svg"
                                    alt="edit"
                                    className="h-[20px] cursor-pointer"
                                />
                            </div>
                        </span>
                    </>
                );
            },
        },
    ];
    const handleTypeChange = (value: any) => {
        setProductType(value);
        console.log('Selected Type:', value); // You can use this value as needed
    };
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };
    return (
        <div>
            <CustomTable
                heading={storeName}
                search={search}
                showSearch={true}
                onSearch={handleSearch}
                showStoreDetailScreenButtons={true}
                storeId={storeId}
                discountManagementButtonForInventory={true}
                addNewModal={true}
                openModal={showModal}
                addNewTitle="Add New"
                onTypeChange={handleTypeChange}
            />
            <div className="rounded-xl border bg-white px-4 pb-16 pt-4 shadow-md">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                borderColor: '#0000001A',
                                cellFontSize: 13,
                                headerBg: '#fff',
                                headerColor: '#1A3353',
                                colorText: '#455560',
                            },
                        },
                    }}
                >
                    <Table
                        className="m-2 rounded-[6px] border-1"
                        columns={columns}
                        dataSource={inventoryList}
                        pagination={false}
                    />
                </ConfigProvider>
            </div>
            <div className="flex justify-center py-10">
                <Pagination
                    current={currentPage}
                    total={inventoryCount}
                    pageSize={pageSize}
                    onChange={handlePageChange}
                    hideOnSinglePage
                />
            </div>
            <SkuInventoryModal
                onClose={handleClose}
                visible={modalVisible}
                storeId={storeId}
                inventoryId={inventoryDetail ? inventoryDetail._id : '0'}
                inventroyDetail={inventoryDetail}
                inventoryList={inventoryList}
            />
        </div>
    );
};

export default StoreDetail;
