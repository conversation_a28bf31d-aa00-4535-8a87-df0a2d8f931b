import React, { useState, useEffect } from 'react';
import {
    ConfigProvider,
    DatePicker,
    Form,
    Input,
    Modal,
    Select,
    Button,
} from 'antd';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '~/redux/store';
import {
    SearchProductBySkuDetails,
    CreateNewInventory,
    updateInventory,
} from '~/redux/actions/merchandise/inventory-action';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import dayjs, { Dayjs } from 'dayjs';

interface SkuInventoryModalProps {
    visible: boolean;
    onClose: () => void;
    storeId: string;
    inventoryId: string;
    inventroyDetail?: any;
    inventoryList?: any;
}

const SkuInventoryModal: React.FC<SkuInventoryModalProps> = ({
    visible,
    onClose,
    storeId,
    inventoryId,
    inventroyDetail,
    inventoryList,
}) => {
    const INITIAL_STATE = {
        productType: 'simple',
        productName: '',
        sku: '',
        status: 'active',
        expiryDate: '',
        storeId,
        discount: '',
        mrp: '',
        salePrice: '',
        gst: '',
        discountPrice: '',
        productVariantId: undefined,
        id: inventoryId,
    };
    const [form] = Form.useForm();
    const dispatch = useDispatch<AppDispatch>();
    const [selectedProduct, setSelectedProduct] = useState(INITIAL_STATE);
    const [selectedProductType, setSelectedProductType] = useState('simple');
    const [inventoryIds, setInventoryIds] = useState(inventoryId);
    const [productId, setProductId] = useState(
        inventroyDetail?.productId || null
    );
    const [skuOptions, setSkuOptions] = useState<
        { value: string; label: string }[]
    >([]);
    const handleInventoryDetails = (key: string, value: any) => {
        let data = { ...selectedProduct, [key]: value };
        console.log(data);
        if ((key === 'discount' || key === 'salePrice') && !!data.salePrice) {
            if (data.discount) {
                console.log('husfsfsafrrry');
                const discountPrice =
                    Number(data.salePrice) -
                    (Number(data.salePrice) * Number(data.discount)) / 100;
                const afterGst =
                    (discountPrice * Number(data.gst)) / 100 + discountPrice;

                data = {
                    ...data,
                    discountPrice: Number(discountPrice.toFixed(2)),
                };

                form.setFieldsValue({
                    discountPrice: Number(discountPrice.toFixed(2)),
                });
            } else {
                form.setFieldsValue({
                    discountPrice: Number(data.salePrice).toFixed(2),
                });
            }
        }

        setSelectedProduct(data);
    };

    useEffect(() => {
        if (inventroyDetail) {
            setSelectedProductType(inventroyDetail.productType);
            form.setFieldsValue({
                productType: inventroyDetail.productType,
                sku: inventroyDetail.sku || '',
                productName: inventroyDetail.name || '',
                mrp: inventroyDetail.mrp || '',
                salePrice: inventroyDetail.salePrice || '',
                discountPercentage: inventroyDetail.discount || '',
                discountPrice: inventroyDetail.discountPrice || '',
                quantity: inventroyDetail.quantity,
                expiryDate: inventroyDetail.expiryDate
                    ? dayjs(inventroyDetail.expiryDate)
                    : null,
            });
            setSelectedProduct({
                ...inventroyDetail,
                storeId: storeId,
            });
        }
    }, [inventroyDetail]);
    const searchProductBySku = async (searchText = '', page: number) => {
        try {
            const response = await dispatch(
                SearchProductBySkuDetails({
                    page,
                    pageSize: 10,
                    search: searchText,
                    productType: selectedProductType,
                })
            ).unwrap();

            return (
                response?.data?.data?.map((item: any, index: any) => ({
                    value: item._id,
                    label: item.sku + ' - ' + item.name,
                    productName: item.name,
                    sku: item.sku,
                    batch: item.batch,
                    gst: item.gst,
                    id: item._id,
                    productId: item.productId ?? item._id,
                    productVariantId: item._id,
                    index: index,
                    productType: item.type ?? 'simple',
                    discount: item.discount,
                    status: item.status,
                    mrp: item.mrp,
                    salePrice: item.salePrice,
                    expiryDate: item.expiryDate,
                })) || []
            );
        } catch (error) {
            console.error('Error fetching SKU:', error);
            return [];
        }
    };

    const handleProductTypeChange = async (value: string) => {
        form.setFieldsValue({
            sku: null,
            productName: null,
        });
        setSelectedProductType(value);
        handleInventoryDetails('productType', value);
        setSelectedProduct(INITIAL_STATE);
    };

    const disabledDate = (current: Dayjs) => {
        // Disable dates before today
        return current && current < dayjs().startOf('day');
    };
    const onFinish = (values: any) => {
        if (selectedProduct.productType === 'simple') {
            delete selectedProduct.productVariantId;
        }
        console.log(inventoryIds);
        const payload = {
            productType: values.productType || selectedProduct.productType,
            mrp: Number(values.mrp),
            salePrice: Number(values.salePrice),
            discount: Number(values.discountPercentage),
            discountPrice: Number(values.discountPrice),
            expiryDate: values.expiryDate
                ? values.expiryDate.toISOString()
                : null,
            storeId: selectedProduct.storeId,
            productId: selectedProduct.productId,
            quantity: Number(values.quantity),
            productVariantId:
                selectedProduct?.productVariantId ||
                inventroyDetail?.productVariantId ||
                undefined,
        };
        if (payload.productType === 'simple') {
            delete payload.productVariantId;
        }

        if (inventoryId === '0' && inventoryIds === '0') {
            dispatch(CreateNewInventory(payload))
                .unwrap()
                .then(() => {
                    onClose();
                    form.resetFields();
                    setSelectedProduct(INITIAL_STATE);
                    setSelectedProductType('simple');
                });
        } else {
            payload.productId = inventroyDetail?.productId
                ? inventroyDetail?.productId
                : productId;
            dispatch(
                updateInventory({
                    reqData: payload,
                    inventoryId: inventroyDetail?._id
                        ? inventroyDetail?._id
                        : inventoryIds,
                })
            )
                .unwrap()
                .then(() => {
                    onClose();
                    form.resetFields();
                    setSelectedProduct(INITIAL_STATE);
                    setSelectedProductType('simple');
                });
        }
    };
    const handleCancel = () => {
        form.resetFields();
        setSelectedProduct(INITIAL_STATE);
        setSelectedProductType('simple');
        onClose();
    };

    return (
        <Modal
            title={
                <p className="w-fit border-b-2 border-primary text-2xl text-[#1A3353]">
                    {inventroyDetail ? ' Edit Inventory' : 'Add Inventory'}{' '}
                </p>
            }
            className="lg:w-[55%]"
            footer={false}
            centered
            visible={visible}
            onCancel={handleCancel}
            onOk={onClose}
        >
            <ConfigProvider
                theme={{
                    components: {
                        Input: {},
                        Form: {
                            verticalLabelMargin: -5,
                        },
                    },
                }}
            >
                <Form
                    name="gymCreate"
                    layout="vertical"
                    size="large"
                    form={form}
                    onFinish={onFinish}
                    autoComplete="off"
                >
                    <div className="flex flex-row  justify-between pt-8">
                        <Form.Item
                            className="w-[48%]"
                            label="Product Type"
                            name="productType"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select Product',
                                },
                            ]}
                        >
                            <Select
                                className="h-16"
                                placeholder="Select Product"
                                options={[
                                    {
                                        value: 'simple',
                                        label: 'Simple Product',
                                    },
                                    { value: 'variable', label: 'Variable' },
                                ]}
                                value={selectedProductType}
                                onChange={handleProductTypeChange}
                                disabled={inventroyDetail ? true : false}
                            />
                        </Form.Item>
                    </div>

                    <div className="flex flex-row items-center justify-between pt-8">
                        {/* <Form.Item
                            className="w-[48%]"
                            label="SKU"
                            name="sku"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select SKU',
                                },
                            ]}
                        >
                            <InfiniteScrollSelect
                                fetchOptions={searchProductBySku}
                                onChange={(value, option) => {
                                    setSelectedProduct({
                                        ...option,
                                        storeId: storeId,
                                    });
                                    const extractedProductName =
                                        option?.productName ||
                                        option?.label?.split(' - ')[1] ||
                                        '';
                                    form.setFieldsValue({
                                        sku: value,
                                        productName: extractedProductName,
                                    });
                                }}
                                placeholder="Select SKU"
                                disabled={inventroyDetail ? true : false}
                            />
                        </Form.Item> */}
                        <Form.Item
                            className="w-[48%]"
                            label="SKU"
                            name="sku"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select SKU',
                                },
                            ]}
                        >
                            <InfiniteScrollSelect
                                className="border-b border-[#d1d5db]"
                                fetchOptions={searchProductBySku}
                                onChange={(value, option) => {
                                    const extractedProductName =
                                        option?.productName ||
                                        option?.label?.split(' - ')[1] ||
                                        '';

                                    let filteredInventory = [];

                                    if (option.productType === 'variable') {
                                        filteredInventory =
                                            inventoryList?.filter(
                                                (item: any) =>
                                                    item.productVariantId ===
                                                    option.productVariantId
                                            );
                                    } else if (
                                        option.productType === 'simple'
                                    ) {
                                        filteredInventory =
                                            inventoryList?.filter(
                                                (item: any) =>
                                                    item.productId ===
                                                    option.productId
                                            );
                                    }

                                    if (filteredInventory?.length) {
                                        // If SKU exists in inventoryList, populate the form with existing inventory details
                                        const data = filteredInventory[0];
                                        console.log(data, inventoryId);
                                        form.setFieldsValue({
                                            productType: data.productType,
                                            sku: data.sku || '',
                                            productName:
                                                data.name ||
                                                extractedProductName,
                                            mrp: data.mrp || '',
                                            salePrice: data.salePrice || '',
                                            discountPercentage:
                                                data.discount || '',
                                            discountPrice:
                                                data.discountPrice || '',
                                            quantity: data.quantity || '',
                                            expiryDate: data.expiryDate
                                                ? dayjs(data.expiryDate)
                                                : null,
                                        });
                                        setInventoryIds(data.inventoryId);
                                        setProductId(data.productId);
                                        setSelectedProduct({
                                            ...data,
                                            storeId: storeId,
                                        });
                                    } else {
                                        // If SKU does not exist in inventoryList, set only the selected SKU details
                                        setSelectedProduct({
                                            ...option,
                                            storeId: storeId,
                                        });

                                        form.setFieldsValue({
                                            sku: value,
                                            productName: extractedProductName,
                                        });
                                    }
                                }}
                                placeholder="Select SKU"
                                disabled={inventroyDetail ? true : false}
                            />
                        </Form.Item>

                        <Form.Item
                            className="w-[48%]"
                            name="productName"
                            label="Product Name"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter product name',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Product name"
                                className="h-16"
                                disabled
                            />
                        </Form.Item>
                    </div>
                    <div className="flex flex-row items-center justify-between gap-16">
                        <Form.Item
                            className="w-[48%]"
                            name="mrp"
                            label={
                                <label className="text-[#1A3353]">MRP</label>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter MRP',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Product MRP"
                                className="h-16"
                                onInput={(e: any) => {
                                    e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                    );
                                }}
                            />
                        </Form.Item>
                        <Form.Item
                            className="w-[48%]"
                            name="salePrice"
                            label="Sale Price"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter Sale price',
                                },
                                ({ getFieldValue }) => ({
                                    validator(_, value) {
                                        if (
                                            !value ||
                                            Number(value) <=
                                                Number(getFieldValue('mrp'))
                                        ) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(
                                            new Error(
                                                'Sale Price cannot be greater than MRP'
                                            )
                                        );
                                    },
                                }),
                            ]}
                        >
                            <Input
                                placeholder="Add product sale price"
                                className="h-16"
                                onInput={(e: any) => {
                                    e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                    );
                                }}
                                onChange={(e) =>
                                    handleInventoryDetails(
                                        'salePrice',
                                        e.target.value
                                    )
                                }
                            />
                        </Form.Item>
                    </div>
                    <div className="flex flex-row items-center justify-between gap-16">
                        <Form.Item
                            className="w-[48%]"
                            name="discountPercentage"
                            label="Percentage Discount"
                            rules={[
                                {
                                    required: false,
                                    message: 'Please enter Discount',
                                },
                                ({ getFieldValue }) => ({
                                    validator(_, value) {
                                        if (!value || Number(value) <= 100) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject(
                                            new Error(
                                                'Discount Percentage can not be greater than 100'
                                            )
                                        );
                                    },
                                }),
                            ]}
                        >
                            <Input
                                placeholder="Add discount"
                                className="h-16"
                                onInput={(e: any) => {
                                    e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                    );
                                }}
                                onChange={(e) =>
                                    handleInventoryDetails(
                                        'discount',
                                        e.target.value
                                    )
                                }
                            />
                        </Form.Item>

                        <Form.Item
                            className="w-[48%]"
                            name="discountPrice"
                            label="Discount price"
                        >
                            <Input
                                placeholder="Discount Price"
                                className="h-16"
                            />
                        </Form.Item>
                    </div>
                    <div className="flex flex-row items-center justify-between gap-16">
                        <Form.Item
                            className="w-[48%]"
                            name="quantity"
                            label={
                                <label className="text-[#1A3353]">
                                    Quantity
                                </label>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter inventory',
                                },
                            ]}
                        >
                            <Input
                                placeholder="Add inventory"
                                className="h-16"
                            />
                        </Form.Item>

                        <Form.Item
                            label="Expiry Date"
                            name="expiryDate"
                            rules={[
                                {
                                    required: false,
                                    message: 'Please enter Expiry Date',
                                },
                            ]}
                            className="w-[48%]"
                        >
                            <DatePicker
                                placeholder="DD/MM/YYYY"
                                format="DD/MM/YYYY"
                                style={{ width: '100%' }}
                                popupClassName="custom-datepicker"
                                className="h-16"
                                disabledDate={disabledDate}
                            />
                        </Form.Item>
                    </div>
                    <Form.Item className="flex justify-end py-10">
                        <Button
                            htmlType="button"
                            className="me-6 w-[110px] border-[#1A3353] bg-[#fff] text-xl text-[#1A3353]"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                        <Button
                            htmlType="submit"
                            className="w-[110px] border-purpleLight bg-purpleLight text-xl text-white"
                        >
                            Save
                        </Button>
                    </Form.Item>
                </Form>
            </ConfigProvider>
        </Modal>
    );
};

export default SkuInventoryModal;
