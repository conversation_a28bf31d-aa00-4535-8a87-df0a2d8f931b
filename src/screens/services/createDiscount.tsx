import {
    Button,
    ConfigProvider,
    Form,
    Input,
    Modal,
    Radio,
    RadioChangeEvent,
    Select,
    Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import BranchSelector from './branchSelector';
import {
    AllServiceCategoryListForDiscounts,
    CreateDiscount,
    DiscountList,
    PricingListForDiscountId,
    PricingListForDiscounts,
    UpdateDiscount,
} from '~/redux/actions/createDiscountAction';
import { useDispatch } from 'react-redux';
import Alertify from '~/services/alertify';
import PricingSelector from './pricingSelectorforDiscount';
const { Text } = Typography;

const CreateDiscountModal = (props: any) => {
    const [form] = Form.useForm();
    const [selectedPackages, setSelectedPackages] = useState<string[]>([]);
    const dispatch = useDispatch();
    const [pricingList, setPricingList] = useState<any[]>([]);
    const [discountTypeState, setDiscountTypeState] = useState('flat');
    const [discountValueState, setDiscountValueState] = useState(0);
    const [selectedOption, setSelectedOption] = useState('');
    const [selected, setSelected] = useState<string[]>([]);
    const [allSelectedItems, setAllSelectedItems] = useState<
        Array<{ itemType: string; itemId: string; serviceType: string }>
    >([]);
    const [allServiceCategory, setAllServiceCategory] = useState<any[]>([]);

    // Reset function to clear all states
    const resetAll = () => {
        form.resetFields();
        setSelectedPackages([]);
        setDiscountTypeState('flat');
        setDiscountValueState(0);
        setSelectedOption('');
        setSelected([]);
        setAllSelectedItems([]);
        setPricingList([]);
        if (props.setSelectedBranches) {
            props.setSelectedBranches([]);
        }
        if (props.setDiscountDetails) {
            props.setDiscountDetails(null);
        }
    };

    useEffect(() => {
        if (props.newDiscountModal && props.discountDetails) {
            // Populate form with discount details
            form.setFieldsValue({
                ...props.discountDetails,
                pricingPackages: props.discountDetails.items?.map(
                    (item: any) => item.itemId
                ),
            });

            setDiscountTypeState(props.discountDetails.type || 'flat');
            setDiscountValueState(props.discountValueState || 0);
            // setSelectedPackages(
            //     props.discountDetails.items?.map((item: any) => item.itemId) ||
            //         []
            // );
        } else {
            resetAll();
            // Set default values
            form.setFieldsValue({
                target: 'all_users',
                type: 'flat',
                facility: [],
            });
        }
        console.log('props.editId is', props.editId);
        if (props.editId) {
            dispatch(
                PricingListForDiscountId({
                    promotionId: props.editId,
                    itemType: 'service',
                })
            )
                .then((response: any) => {
                    console.log(
                        'pricing packages:::::::',
                        response?.payload?.data?.data
                    );
                    const transformedItems = response?.payload?.data?.data
                        .filter((item: any) => item.selected === true)
                        .map((item: any) => item._id);
                    const transformedAllItems = response?.payload?.data?.data
                        .filter((item: any) => item.selected === true)
                        .map((item: any) => ({
                            itemId: item._id,
                            itemType: 'service',
                        }));

                    setAllSelectedItems(transformedAllItems);
                    setSelectedPackages(transformedItems);
                })
                .catch((error: any) => {
                    Alertify.error(error);
                });
        }
    }, [
        props.newDiscountModal,
        props.discountDetails,
        props.editId,
        setAllSelectedItems,
    ]);

    // Get Pricing List
    const handleServiceTypeChange = (e: RadioChangeEvent) => {
        const newServiceType = e.target.value;
        setSelectedOption(newServiceType);

        dispatch(PricingListForDiscounts({ serviceId: newServiceType }))
            .then((response: any) => {
                const pricingData = response?.payload?.data?.data || [];
                setPricingList(pricingData);

                // Get all package IDs for this service type
                const packageIds = pricingData.map((pkg: any) => pkg._id);

                // Find selected items matching these packageIds
                const previouslySelectedIds = allSelectedItems
                    .filter((item) => packageIds?.includes(item.itemId))
                    .map((item) => item.itemId);

                console.log(
                    'previouslySelectedIds----------',
                    allSelectedItems
                );

                // Set the selected packages and form field value
                setSelectedPackages(previouslySelectedIds);
                form.setFieldsValue({ pricingPackages: previouslySelectedIds });
            })
            .catch((error: any) => {
                Alertify.error(error);
            });
    };
    useEffect(() => {
        if (props.newDiscountModal && props.discountDetails) {
            // Only set fields if the modal is open and form is mounted
            form.setFieldsValue({
                ...props.discountDetails,
                pricingPackages: props.discountDetails.items?.map(
                    (item: any) => item.itemId
                ),
            });

            // Set the discount type and value states
            setDiscountTypeState(props.discountDetails.type || 'flat');
            setDiscountValueState(props.discountValueState || 0);

            // // Set the selected packages
            // setSelectedPackages(
            //     props.discountDetails.items?.map((item: any) => item.itemId) ||
            //         []
            // );

            // Pre-select the facilities in BranchSelector
            const facilityIds = props.discountDetails.facilityIds?.map(
                (facility: any) => facility._id
            );
            if (facilityIds) {
                props.setSelectedBranches(facilityIds);
            }
        } else {
            // Reset all form fields and states when modal is closed
            form.resetFields();
            setSelectedPackages([]);
            setDiscountTypeState('flat');
            setDiscountValueState(0);
            setSelectedOption('');
            setSelected([]);
            setAllSelectedItems([]);
            setPricingList([]);
            if (props.setSelectedBranches) {
                props.setSelectedBranches([]);
            }
            if (props.setDiscountDetails) {
                props.setDiscountDetails(null); // Changed from [] to null
            }
            // Set default values
            form.setFieldsValue({
                target: 'all_users',
                type: 'flat',
                facility: [], // Explicitly reset facility field
            });
        }
    }, [props.newDiscountModal, props.discountDetails, form]);

    useEffect(() => {
        dispatch(AllServiceCategoryListForDiscounts())
            .unwrap()
            .then((response: any) => {
                setAllServiceCategory(response?.data?.data?.list);
            })
            .catch(() => {})
            .finally();
    }, [setPricingList, setAllServiceCategory]);
    // New function to handle pricing package selection
    const handlePricingPackageChange = (selectedIds: string[]) => {
        setSelectedPackages(selectedIds);

        // Get all current itemIds in allSelectedItems that match the current pricingList
        const currentPricingIds = pricingList.map((item) => item._id);

        // Filter allSelectedItems to separate current service type items and other items
        const currentServiceItems = allSelectedItems.filter((item) =>
            currentPricingIds.includes(item.itemId)
        );
        const otherServiceItems = allSelectedItems.filter(
            (item) => !currentPricingIds.includes(item.itemId)
        );

        // Find which items were unchecked by comparing with new selection
        const uncheckedIds = currentServiceItems
            .map((item) => item.itemId)
            .filter((id) => !selectedIds.includes(id));

        // Keep items that weren't unchecked from current service
        const remainingCurrentItems = currentServiceItems.filter(
            (item) => !uncheckedIds.includes(item.itemId)
        );

        // Find truly new items that don't exist in allSelectedItems
        const existingIds = allSelectedItems.map((item) => item.itemId);
        const newItemIds = selectedIds.filter(
            (id) => !existingIds.includes(id)
        );

        // Add only the new items
        const newItems = newItemIds.map((id) => ({
            itemType: 'service',
            itemId: id,
            serviceType: selectedOption || '',
        }));

        // Combine other service items with remaining current items and new items
        setAllSelectedItems([
            ...otherServiceItems,
            ...remainingCurrentItems,
            ...newItems,
        ]);

        // Update the form field value
        form.setFieldsValue({ pricingPackages: selectedIds });
    };
    // console.log('setAllSelectedItems', allSelectedItems);
    // Modify the form submission to use allSelectedItems
    const handleSubmit = (values: any) => {
        const formData = {
            ...values,
            facilityIds: props.selectBranches,
            items: allSelectedItems.map(({ itemId, itemType }) => ({
                itemId,
                itemType,
            })),
            value: Number(values.value),
            itemType: 'service',
        };
        // ... rest of your submit logic
    };
    // console.log('Services category list is:', allServiceCategory);
    return (
        <Modal
            open={props.newDiscountModal}
            closable={true}
            centered
            onCancel={() => {
                props.setNewDiscountModal(false);
                props.setEditId('');
                props.setEdit(false);

                resetAll();
            }}
            title={props.edit ? 'Edit Discount' : 'Create Discount'}
            footer={[
                <Button
                    key="save"
                    className="bg-purpleLight text-white"
                    onClick={() => {
                        const currentValues = form.getFieldsValue();
                        const defaultValues = {
                            target: 'all_users',
                            type: 'flat',
                        };
                        const mergedValues = {
                            ...defaultValues,
                            ...currentValues,
                        };
                        if (!mergedValues.target) {
                            mergedValues.target = defaultValues.target;
                        }
                        if (!mergedValues.type) {
                            mergedValues.type = defaultValues.type;
                        }
                        if (!mergedValues.pricingPackages) {
                            mergedValues.pricingPackages = [];
                        }

                        form.setFieldsValue(mergedValues);

                        form.validateFields()
                            .then((values: any) => {
                                const {
                                    facility,
                                    pricingPackages,
                                    value,
                                    serviceType,
                                    ...valuesWithoutFacility
                                } = values;

                                // Use allSelectedItems instead of transforming pricingPackages
                                const formData = {
                                    ...valuesWithoutFacility,
                                    facilityIds: props.selectBranches,
                                    // Transform allSelectedItems to match the required format
                                    items: allSelectedItems.map(
                                        ({ itemId }) => ({
                                            itemType: 'service',
                                            itemId,
                                        })
                                    ),
                                    value: Number(value),
                                    itemType: 'service',
                                };
                                if (!props.edit) {
                                    dispatch(CreateDiscount(formData))
                                        .unwrap()
                                        .then((response: any) => {
                                            Alertify.success(
                                                'Discount created successfully'
                                            );
                                            props.setNewDiscountModal(false);

                                            dispatch(
                                                DiscountList({
                                                    page: 1,
                                                    perPage: 10,
                                                })
                                            )
                                                .then((response: any) => {
                                                    const data =
                                                        response?.payload?.data?.data.map(
                                                            (item: any) => ({
                                                                ...item,
                                                                key: item._id,
                                                                facilityIds: (
                                                                    (item?.facilityIds as []) ||
                                                                    []
                                                                )
                                                                    .map(
                                                                        (
                                                                            cc: any
                                                                        ) =>
                                                                            cc.facilityName
                                                                    )
                                                                    .join(', '),
                                                            })
                                                        );
                                                    props.setDataSource(data);
                                                    props.setTotalItems(
                                                        response?.payload?.data
                                                            ?._metadata
                                                            ?.pagination
                                                            ?.total || 0
                                                    );
                                                })
                                                .catch((error: any) => {
                                                    console.log(
                                                        'Error in fetch:',
                                                        error
                                                    );
                                                });
                                        })
                                        .catch((error: any) => {
                                            console.log(
                                                'Error in creating::::',
                                                error
                                            );
                                            Alertify.error(error.message[0]);
                                        });
                                } else {
                                    dispatch(
                                        UpdateDiscount({
                                            id: props.editId,
                                            formData,
                                        })
                                    )
                                        .unwrap()
                                        .then((response: any) => {
                                            Alertify.success(
                                                'Discount updated successfully'
                                            );
                                            props.setNewDiscountModal(false);
                                            props.setEditId('');
                                            props.setEdit(false);
                                            dispatch(
                                                DiscountList({
                                                    page: 1,
                                                    perPage: 10,
                                                })
                                            )
                                                .then((response: any) => {
                                                    const data =
                                                        response?.payload?.data?.data.map(
                                                            (item: any) => {
                                                                return {
                                                                    ...item,
                                                                    facilityIds:
                                                                        (
                                                                            (item?.facilityIds as []) ||
                                                                            []
                                                                        )
                                                                            .map(
                                                                                (
                                                                                    cc: any
                                                                                ) =>
                                                                                    cc.facilityName
                                                                            )
                                                                            .join(
                                                                                ', '
                                                                            ),
                                                                };
                                                            }
                                                        );
                                                    props.setDataSource(data);
                                                })
                                                .catch((error: any) => {
                                                    console.log(
                                                        'Error in fetch:',
                                                        error
                                                    );
                                                });
                                        })
                                        .catch((error: any) => {
                                            console.log(
                                                'Error in creating::::',
                                                error
                                            );
                                            Alertify.error(error.message[0]);
                                        });
                                }
                            })
                            .catch((info: any) => {
                                console.log('Validate Failed:', info);
                            });
                    }}
                >
                    Save
                </Button>,
                <Button
                    key="cancel"
                    onClick={() => {
                        props.setNewDiscountModal(false);
                        props.setEditId('');
                        props.setEdit(false);

                        resetAll();
                    }}
                >
                    Cancel
                </Button>,
            ]}
            width={800}
        >
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            itemMarginBottom: 0,
                        },
                    },
                }}
            >
                <Form
                    form={form}
                    layout="horizontal"
                    size="large"
                    preserve={false}
                    className="create-discount-form"
                >
                    <BranchSelector
                        form={form}
                        setSelected={setSelected}
                        selected={selected}
                        preSelectBranches={
                            props.discountDetails?.facilityIds || []
                        } // Add null check
                        setSelectedBranches={props.setSelectedBranches}
                    />
                    <div className="mb-8 flex w-full items-center">
                        <Text className="w-[20%] font-medium text-[#1a3353]">
                            Discount Name
                        </Text>

                        <div className="w-[80%]">
                            <Form.Item
                                // label="Discount Name"
                                name="name"
                                style={{ width: '100%', marginBottom: 0 }}
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter discount name',
                                    },
                                ]}
                            >
                                <Input placeholder="Discount Name" />
                            </Form.Item>
                        </div>
                    </div>

                    <div className="mb-8 flex w-full items-center">
                        <Text className="w-[20%] font-medium text-[#1a3353]">
                            Discount
                        </Text>

                        <div className="flex w-[80%] gap-8">
                            <Form.Item
                                name="type"
                                className="w-1/3"
                                initialValue="flat"
                            >
                                <Select
                                    onChange={() => {
                                        setDiscountTypeState(
                                            form.getFieldValue('type')
                                        );
                                    }}
                                    options={[
                                        {
                                            label: 'Flat',
                                            value: 'flat',
                                        },
                                        {
                                            label: 'Percentage',
                                            value: 'percentage',
                                        },
                                    ]}
                                />
                            </Form.Item>

                            <Form.Item
                                name="value"
                                className="w-2/3"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter discount value',
                                    },
                                    ({ getFieldValue }) => ({
                                        validator(_, value) {
                                            if (!value || value < 0) {
                                                return Promise.reject(
                                                    'Value cannot be negative'
                                                );
                                            }
                                            if (
                                                getFieldValue('type') ===
                                                    'percentage' &&
                                                value > 100
                                            ) {
                                                return Promise.reject(
                                                    'Percentage cannot exceed 100'
                                                );
                                            }
                                            if (discountTypeState === 'flat') {
                                                setDiscountValueState(value);
                                            } else {
                                                setDiscountValueState(0);
                                            }
                                            return Promise.resolve();
                                        },
                                    }),
                                ]}
                            >
                                <Input
                                    type="number"
                                    min={0}
                                    max={
                                        form.getFieldValue('type') ===
                                        'percentage'
                                            ? 100
                                            : undefined
                                    }
                                    placeholder="Enter discount value"
                                />
                            </Form.Item>
                        </div>
                    </div>

                    <Text className="mb-0 font-medium text-[#1a3353]">
                        Can Apply this discount to
                    </Text>
                    <Form.Item name="target" initialValue="all_users">
                        <Radio.Group>
                            <Radio value="all_users">All Users</Radio>
                            <Radio value="members_only">Members</Radio>
                        </Radio.Group>
                    </Form.Item>

                    <div className="mt-4">
                        <p className="mb-4 text-gray-500">
                            Please link your discount to pricing packages by
                            selecting the category and selecting the pricing you
                            want to associate this with
                        </p>

                        <div className="flex gap-4">
                            <div className="w-1/2 pe-4 shadow-sm">
                                <p className="mb-2 font-medium text-[#1A3353]">
                                    Service Type
                                </p>
                                <div className="h-64 overflow-y-scroll">
                                    <Form.Item
                                        name="serviceType"
                                        // rules={[
                                        //     {
                                        //         required: true,
                                        //         message:
                                        //             'Please select a service type',
                                        //     },
                                        // ]}
                                    >
                                        <Radio.Group
                                            className="custom-radio flex flex-col gap-2"
                                            onChange={handleServiceTypeChange}
                                        >
                                            {allServiceCategory.map(
                                                (category) => (
                                                    <Radio
                                                        key={category._id}
                                                        value={category._id}
                                                        className="[&>span:last-child]:!ml-0"
                                                    >
                                                        <div
                                                            className={`rounded-md px-4 py-2 transition-colors ${
                                                                selectedOption ===
                                                                category._id
                                                                    ? 'bg-[rgba(129,67,209,0.1)] text-primary'
                                                                    : 'bg-gray-100'
                                                            }`}
                                                        >
                                                            {category.name}
                                                        </div>
                                                    </Radio>
                                                )
                                            )}
                                        </Radio.Group>
                                    </Form.Item>
                                </div>
                            </div>

                            <div className="w-1/2 ps-4 shadow-sm">
                                <p className="mb-2 font-medium text-[#1a3353]">
                                    Select Pricing Packages
                                </p>
                                <Form.Item
                                    name="pricingPackages"
                                    // rules={[
                                    //     {
                                    //         validator: async (_, value) => {
                                    //             if (
                                    //                 allSelectedItems.length ===
                                    //                 0
                                    //             ) {
                                    //                 // return Promise.reject(
                                    //                 //     'Please select at least one pricing package'
                                    //                 // );
                                    //             }
                                    //             return Promise.resolve();
                                    //         },
                                    //     },
                                    // ]}
                                >
                                    <PricingSelector
                                        form={form}
                                        selectedPackages={selectedPackages}
                                        pricingList={pricingList}
                                        setSelectedPackages={
                                            handlePricingPackageChange
                                        }
                                        discountValueState={discountValueState}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </div>
                    {/* Display selected items summary */}
                    {allSelectedItems?.length > 0 && (
                        <div className="mt-4 hidden">
                            h.
                            <p className="font-medium">Selected Packages:</p>
                            <div>
                                {allSelectedItems?.map((item, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center justify-between py-1"
                                    >
                                        <span>
                                            {pricingList.find(
                                                (p) => p._id === item.itemId
                                            )?.name || item.itemId}
                                            <span className="ml-2 text-gray-500">
                                                ({item.serviceType})
                                            </span>
                                        </span>
                                        <Button
                                            type="text"
                                            danger
                                            onClick={() => {
                                                const newItems =
                                                    allSelectedItems?.filter(
                                                        (_, i) => i !== index
                                                    );
                                                setAllSelectedItems(newItems);
                                                if (
                                                    item.serviceType ===
                                                    selectedOption
                                                ) {
                                                    const currentServiceTypeItems =
                                                        newItems
                                                            .filter(
                                                                (i) =>
                                                                    i.serviceType ===
                                                                    selectedOption
                                                            )
                                                            .map(
                                                                (i) => i.itemId
                                                            );
                                                    setSelectedPackages(
                                                        currentServiceTypeItems
                                                    );
                                                    form.setFieldsValue({
                                                        pricingPackages:
                                                            currentServiceTypeItems,
                                                    });
                                                }
                                            }}
                                        >
                                            Remove
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </Form>
            </ConfigProvider>
        </Modal>
    );
};

export default CreateDiscountModal;
