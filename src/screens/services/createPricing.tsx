import {
    Button,
    ConfigProvider,
    Form,
    Input,
    Typography,
    Select,
    Checkbox,
    Radio,
    FormProps,
    Table,
    Spin,
    Tabs,
    TimePicker,
} from 'antd';
import React, {
    useEffect,
    useState,
    useRef,
    useCallback,
    useMemo,
} from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLocation, useParams } from 'wouter';
import {
    CreatePricingAPI,
    PricingDetails,
    UpdatePricingAPI,
} from '~/redux/actions/pricing-actions';
import { ServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import { useLoader } from '~/hooks/useLoader';
// import FullLoader from '~/components/library/loader/full-loader';
import { MembershipList } from '~/redux/actions/membership-action';
import { capitalizeFirstLetter } from '~/components/common/function';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { getQueryParams } from '~/utils/getQueryParams';
// import CommonTable from '~/components/common/commonTable';
// import CustomTable from '~/components/common/customTable';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import { useSelector } from 'react-redux';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import AddDiscountModal from '~/components/modals/addDiscountOnPricing';
import { DiscountListOnPricePage } from '~/redux/actions/createDiscountAction';
import Alertify from '~/services/alertify';
import { RevenueCategoryList } from '~/redux/actions/revenue-category-action';
import dayjs, { Dayjs } from 'dayjs';
// import { CloseOutlined } from '@ant-design/icons'; // Commented out if unused

const { Title, Paragraph } = Typography;
const { Option, OptGroup } = Select;

function goBack() {
    window.history.back();
}

interface TimeSlot {
    from: Dayjs | null | any;
    to: Dayjs | null | any;
    payRateIds: any;
}
interface WorkingHours {
    [key: string]: TimeSlot[];
}

// const InitialWorkingHours = { ... }; // Comment out if unused
const CreatePricing: React.FC = () => {
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();
    const [_, setLocation] = useLocation();
    const [addtionalOptions, setAddtionalOptions] = useState(true);
    // const [loading, setLoading] = useState(false);
    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const { id } = useParams<{ id: string }>();
    const [discountData, setDiscountData] = useState<any[]>([]);
    const [selectedDiscount, setSelectedDiscount] = useState<string[]>([]);
    const [comingDiscounts, setComingDiscounts] = useState<string[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const [totalPages, setTotalPages] = useState(0);
    const [trialPricingClass, setTrialPricingClass] = useState<boolean>(false);
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const [requirePinModal, setRequirePinModal] = useState<boolean>(false);

    const pageSize = 10;
    const store = useAppSelector((state) => ({
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        ServiceCategoryListCount:
            state.service_category_store.ServiceCategoryListCount,
        pricingDetailData: state.pricing_store.pricingDetailData,
        membershipList: state.membership_store.membershipList,
        role: state.auth_store.role,
    }));
    const [isEditable, setIsEditable] = useState<boolean>(false);
    const params = getQueryParams();
    const { type, view } = params;

    const [serviceCategoryData, setServiceCategoryData] = useState<any[]>([]);
    const [primaryServiceType, setPrimaryServiceType] = useState<any>(null);
    const [isServiceCategoryReady, setIsServiceCategoryReady] = useState(false);
    const [selectedSecondaryValues, setSelectedSecondaryValues] = useState<
        string[]
    >([]);
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);
    const [isCourseEnabled, setIsCourseEnabled] = useState(false);
    const [addDiscountModal, setAddDiscountModal] = useState<boolean>(false);
    const [selectedServiceCategoryIndices, setSelectedServiceCategoryIndices] =
        useState<number>(0);
    const [autoApplyId, setAutoApplyId] = useState<string>('');
    const [revenueCategoryOptions, setRevenueCategoryOptions] = useState<any[]>(
        []
    );
    const [timeFrameType, setTimeFrameType] = useState<string>('multiple');
    const [datePickerOpen, setDatePickerOpen] = useState(false);

    const [activeTimeFrames, setActiveTimeFrames] = useState<any[]>([]);
    const [selectedDay, setSelectedDay] = useState<string>('mon');
    const [activeTabKey, SetActiveTabKey] = useState<string>('pricingDetails');
    const dayMap: Record<string, string> = {
        mon: 'monday',
        tue: 'tuesday',
        wed: 'wednesday',
        thu: 'thursday',
        fri: 'friday',
        sat: 'saturday',
        sun: 'sunday',
    };
    useEffect(() => {
        // Clear localStorage when component mounts if we're on the create pricing page
        if (id === '0') {
            localStorage.removeItem('createPricingDiscounts');
            console.log('Cleared createPricingDiscounts from localStorage');
        }
    }, []);
    // Function to fetch discount data with scroll pagination
    const fetchDiscountData = useCallback(
        (pageNumber: number, isScroll: boolean = false) => {
            if (id === '0') return;
            if (isScroll && (loading || !hasMore)) return;

            setLoading(true);
            dispatch(
                DiscountListOnPricePage({
                    pricingId: id,
                    currentPage: pageNumber,
                    pageSize: pageSize,
                })
            )
                .then((response: any) => {
                    const newData = response?.payload?.data?.data || [];
                    const metadata =
                        response?.payload?.data?._metadata?.pagination;
                    const totalPagesFromAPI = metadata?.totalPages || 0;
                    // Update total pages from API response
                    setTotalPages(totalPagesFromAPI);

                    if (isScroll) {
                        // Append new data for scroll pagination
                        setDiscountData((prev: any[]) => {
                            return [...prev, ...newData];
                        });
                    } else {
                        setDiscountData(newData);
                    }

                    // Check if there's more data based on totalPages from API
                    if (totalPagesFromAPI > 0) {
                        // Use API metadata to determine if there are more pages
                        const hasMorePages = pageNumber < totalPagesFromAPI;
                        setHasMore(hasMorePages);
                    } else {
                        // Fallback to data length check if no metadata
                        if (newData.length < pageSize) {
                            setHasMore(false);
                        }
                    }

                    // Merge new discount IDs with existing comingDiscounts, avoiding duplicates
                    // const newDiscounts = newData.map((item: any) => item._id);
                    // setComingDiscounts((prev: string[]) =>
                    //     Array.from(new Set([...prev, ...newDiscounts]))
                    // );
                })
                .catch((error: any) => {
                    Alertify.error(
                        'Could not get the discounts list. Please try again later.'
                    );
                    console.log('Error in fetch discounts list:', error);
                })
                .finally(() => {
                    setLoading(false);
                });
        },
        [id, loading, hasMore, pageSize, dispatch]
    );

    // Scroll handler for infinite scroll
    const handleScroll = useCallback(() => {
        const container = tableContainerRef.current;
        if (container) {
            const { scrollTop, scrollHeight, clientHeight } = container;
            if (
                scrollTop + clientHeight >= scrollHeight - 10 &&
                hasMore &&
                !loading
            ) {
                const nextPage = currentPage + 1;
                setCurrentPage(nextPage);
                fetchDiscountData(nextPage, true);
            }
        }
    }, [hasMore, loading, currentPage, fetchDiscountData]);

    // Add scroll event listener
    useEffect(() => {
        const container = tableContainerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
        }
        return () => {
            if (container) {
                container.removeEventListener('scroll', handleScroll);
            }
        };
    }, [handleScroll]);

    useEffect(() => {
        if (id !== '0') {
            // Reset pagination state when id changes
            setCurrentPage(1);
            setHasMore(true);
            setTotalPages(0);
            setDiscountData([]);
            fetchDiscountData(1, false);
        }
    }, [id, selectedDiscount, addDiscountModal]);

    // Debug effect to check scroll container
    useEffect(() => {
        const container = tableContainerRef.current;
    }, [discountData]);

    const handleServiceCategoryChange = (
        value?: any,
        option?: any,
        sectionIndex?: number
    ) => {
        // setSelectedServiceCategoryIndices((prevIndices) => ({
        //     ...prevIndices,
        //     [sectionIndex]: option.index,
        // }));

        setSelectedServiceCategoryIndices(option.index);
    };
    // Get details for view
    useEffect(() => {
        if (id !== '0') {
            startLoader();
            dispatch(PricingDetails({ pricingId: id }))
                .unwrap()
                .finally(endLoader);
        }
    }, [id]);
    // Details for view END

    const { role } = useSelector((state: any) => state.auth_store);

    useEffect(() => {
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_setup_courses',
            })
        ).then((response: any) => {
            setIsCourseEnabled(response?.payload?.data?.data?.isEnabled);
        });

        dispatch(MembershipList({ isActive: true }))
            .unwrap()
            .then(() => {})
            .finally();

        const accessFlag = sessionStorage.getItem('accessViaSecureFlow');
        if (role === RoleType.ORGANIZATION || (id || '0') !== '0') return;
        else if (accessFlag === 'true')
            sessionStorage.removeItem('accessViaSecureFlow');
        else setPinModalVisible(true);
    }, []);

    const handleClose = () => {
        setLocation('/pricing?pricingType=regularPricing&page=1&pageSize=10');
    };

    const handleDiscountModalClose = () => {
        setAddDiscountModal(false);
    };

    const getLabelFromValue = (value: string) => {
        const [categoryId, appointmentId] = value.split('|');
        const category = serviceCategoryData.find(
            (cat) => cat._id === categoryId
        );
        const appt = category?.appointmentType?.find(
            (a: any) => a._id === appointmentId
        );

        if (category && appt) {
            return `${category.name} - ${appt.name}`;
        }
        return category?.name
            ? `${category.name} - `
            : appt?.name
            ? ` - ${appt.name}`
            : value;
    };

    const fetchAndSetServiceCategories = async (
        serviceType: string,
        serviceCategory: any,
        appointmentTypes: any[] = [],
        relatedServiceCategoryIds: any[] = []
    ) => {
        setIsServiceCategoryReady(false); // mark loading
        const response = await dispatch(
            ServiceCategoryList({
                classType: serviceType,
                serviceCategoryId: [
                    serviceCategory?.value,
                    ...relatedServiceCategoryIds,
                ],
                appointmentTypes,
                relatedServiceCategoryIds,
            })
        );
        const categories = response.payload.data.data?.list || [];
        setServiceCategoryData(categories);
        setIsServiceCategoryReady(true); // mark ready

        setTimeout(() => {
            const categoryIndex = categories.findIndex(
                (item: any) => item._id === serviceCategory?.value
            );

            let selectedCategoryIndex = categoryIndex;

            if (selectedCategoryIndex === -1 && categories.length > 0) {
                selectedCategoryIndex = 0;
            }

            if (selectedCategoryIndex !== -1) {
                const selectedCategory = categories[selectedCategoryIndex];
                const firstAppt = selectedCategory?.appointmentType?.[0];

                if (firstAppt) {
                    const value = `${selectedCategory._id}|${firstAppt._id}`;

                    // Now serviceCategoryData is guaranteed to be ready
                    const label = `${selectedCategory.name} - ${firstAppt.name}`;
                    if (id === '0') {
                        form.setFieldsValue({
                            category_appointment: {
                                value,
                                label,
                            },
                            serviceCategory: {
                                value: selectedCategory._id,
                                label: selectedCategory.name,
                            },
                        });
                    } else {
                        form.setFieldsValue({
                            // category_appointment: {
                            //     value,
                            //     label,
                            // },
                            serviceCategory: {
                                value: selectedCategory._id,
                                label: selectedCategory.name,
                            },
                        });
                    }
                    setPrimaryServiceType(value);
                }

                handleServiceCategoryChange(null, {
                    index: selectedCategoryIndex,
                });
            }
        }, 0);
    };

    useEffect(() => {
        if (id === '0') {
            const defaultServiceType = type || 'personalAppointment';

            form.setFieldsValue({
                serviceType: defaultServiceType,
            });

            fetchAndSetServiceCategories(defaultServiceType, null);
        }
    }, [id]);

    const onValuesChange = (changedValues: any, allValues: any) => {
        const actualPrice = Number(allValues.price) || 0;
        const discountValue = Number(allValues.discountValue) || 0;
        let finalPrice = actualPrice;

        if (changedValues.tax === '0') {
            form.setFieldValue('isInclusiveofGst', false);
        }

        if (
            changedValues.discountValue !== undefined ||
            changedValues.discountType
        ) {
            if (changedValues.discountType && !discountValue) {
                form.setFields([
                    {
                        name: 'discountValue',
                        errors: ['Please enter discount value!'],
                    },
                ]);
                return;
            }
            if (allValues.discountType === 'Percentage') {
                if (discountValue > 100) {
                    form.setFields([
                        {
                            name: 'discountValue',
                            errors: ['Percentage cannot exceed 100!'],
                        },
                    ]);
                    return;
                }
                finalPrice = actualPrice - (actualPrice * discountValue) / 100;
            } else if (allValues.discountType === 'Flat') {
                finalPrice = actualPrice - discountValue;
            }

            if (finalPrice < 0 || finalPrice > actualPrice) {
                form.setFields([
                    {
                        name: 'discountValue',
                        errors: [
                            'Invalid discount! Final price cannot be negative or greater than the actual price.',
                        ],
                    },
                ]);
            } else {
                form.setFields([
                    {
                        name: 'discountValue',
                        errors: [],
                    },
                ]);
            }
        }
        if (changedValues.serviceType) {
            fetchAndSetServiceCategories(changedValues.serviceType, null);
            form.setFieldsValue({
                serviceCategory: undefined,
                appointmentType: undefined,
            });
        }
        if (changedValues.serviceCategory) {
            form.setFieldsValue({
                appointmentType: undefined,
            });
        }
    };
    const buildSecondaryServiceOptions = (
        relationShip: any[],
        getLabelFromValue: (value: string) => string
    ): { value: string; label: string }[] => {
        if (!relationShip || !Array.isArray(relationShip)) return [];

        return relationShip.flatMap((rel) =>
            rel.subTypeIds.map((subtypeId: string) => {
                const value = `${rel.serviceCategory}|${subtypeId}`;
                return {
                    value,
                    label: getLabelFromValue(value) || value,
                };
            })
        );
    };

    // const [priceToCompare, setPriceToCompare] = useState(0);
    useEffect(() => {
        const populateFormAfterServiceCategoryLoad = async () => {
            if (id && id !== '0' && store.pricingDetailData) {
                const service = store.pricingDetailData.services || {};
                let appointmentTypes = service.appointmentType || [];
                let secondaryAppointmentTypeIds: string[] = [];

                if (
                    Array.isArray(service.relationShip) &&
                    service.relationShip.length > 0
                ) {
                    secondaryAppointmentTypeIds = service.relationShip.flatMap(
                        (rel: any) => rel.subTypeIds || []
                    );
                    appointmentTypes = [
                        ...appointmentTypes,
                        ...secondaryAppointmentTypeIds,
                    ];
                }

                const relatedServiceCategoryIds: string[] = (
                    service.relationShip || []
                ).map((rel: any) => rel.serviceCategory);

                // wait for service category data to load
                await fetchAndSetServiceCategories(
                    service.type,
                    {
                        value: service.serviceCategory,
                        label: service.serviceCategoryName,
                    },
                    appointmentTypes,
                    relatedServiceCategoryIds
                );

                // set form values *after* data is ready
                const primaryValue = `${service.serviceCategory}|${
                    appointmentTypes[0] || ''
                }`;
                const categoryAppointment = {
                    value: primaryValue,
                    label: getLabelFromValue(primaryValue),
                };

                const secondaryServiceTypeOptions =
                    buildSecondaryServiceOptions(
                        service.relationShip || [],
                        getLabelFromValue
                    );
                setAutoApplyId(store.pricingDetailData?.promotion?._id);
                // setPriceToCompare(
                //     store.pricingDetailData.isInclusiveofGst
                //         ? store.pricingDetailData.finalPrice
                //         : store.pricingDetailData.price
                // );
                form.setFieldsValue({
                    name: store.pricingDetailData.name,
                    price: store.pricingDetailData.isInclusiveofGst
                        ? store.pricingDetailData.finalPrice
                        : store.pricingDetailData.price,
                    isSellOnline: store.pricingDetailData.isSellOnline,
                    tax: store.pricingDetailData.tax?.toString(),
                    discountType: store.pricingDetailData.discount?.type,
                    discountValue: Number(
                        store.pricingDetailData.discount?.value
                    ),
                    expireInput: store.pricingDetailData.expiredInDays,
                    expirationUnit: store.pricingDetailData.durationUnit,
                    membership: store.pricingDetailData.membershipId,
                    serviceType: service.type,
                    serviceCategory: {
                        value: service.serviceCategory,
                        label: service.serviceCategoryName,
                    },
                    sessions: service.sessionType,
                    category_appointment: categoryAppointment,
                    appointmentType: appointmentTypes,
                    secondary_service_type: secondaryServiceTypeOptions,
                    sessionCount: service.sessionCount,
                    dayPassLimit: service.dayPassLimit,
                    isOffer: service.introductoryOffer,
                    revenueCategory: service.revenueCategory,
                    maxCapacity: service.sessionPerDay,
                    hsnOrSacCode: store.pricingDetailData.hsnOrSacCode,
                    isInclusiveofGst: store.pricingDetailData.isInclusiveofGst,
                });

                setAddtionalOptions(!!store.pricingDetailData?.membershipId);
            }
        };

        populateFormAfterServiceCategoryLoad();
    }, [id, store.pricingDetailData]);
    // console.log('Auto apply:', autoApplyId);
    useEffect(() => {
        if (
            isServiceCategoryReady &&
            id &&
            id !== '0' &&
            store.pricingDetailData
        ) {
            const currentServiceType = form.getFieldValue('serviceType');
            const backendServiceType = store.pricingDetailData?.services?.type;

            if (
                currentServiceType &&
                currentServiceType !== backendServiceType
            ) {
                return;
            }

            const service = store.pricingDetailData?.services || {};
            const appointmentTypes = service.appointmentType || [];

            const primaryValue = `${service.serviceCategory}|${
                appointmentTypes[0] || ''
            }`;
            const categoryAppointment = {
                value: primaryValue,
                label: getLabelFromValue(primaryValue),
            };

            const secondaryServiceTypeOptions = buildSecondaryServiceOptions(
                service.relationShip || [],
                getLabelFromValue
            );

            setActiveTimeFrames(
                store.pricingDetailData?.activeTimeFrames || []
            );
            console.log(
                'Fetched time frame:',
                store.pricingDetailData?.activeTimeFrames
            );

            form.setFieldsValue({
                name: store.pricingDetailData?.name,
                price: store.pricingDetailData.isInclusiveofGst
                    ? store.pricingDetailData.finalPrice
                    : store.pricingDetailData.price,
                isSellOnline: store.pricingDetailData?.isSellOnline,
                tax: store.pricingDetailData?.tax?.toString(),
                discountType: store.pricingDetailData?.discount?.type,
                discountValue: Number(store.pricingDetailData?.discount?.value),
                expireInput: store.pricingDetailData?.expiredInDays,
                expirationUnit: store.pricingDetailData?.durationUnit,
                membership: store.pricingDetailData?.membershipId,
                serviceType: service.type,
                serviceCategory: {
                    value: service.serviceCategory,
                    label: service.serviceCategoryName,
                },
                sessions: service.sessionType,
                category_appointment: categoryAppointment,
                appointmentType: appointmentTypes,
                secondary_service_type: secondaryServiceTypeOptions,
                sessionCount: service.sessionCount,
                dayPassLimit: service.dayPassLimit,
                isOffer: service.introductoryOffer,
                revenueCategory: service.revenueCategory,
                maxCapacity: service.sessionPerDay,
                hsnOrSacCode: store.pricingDetailData?.hsnOrSacCode,
                isInclusiveofGst: store.pricingDetailData?.isInclusiveofGst,
            });

            setAddtionalOptions(!!store.pricingDetailData?.membershipId);
        }
    }, [isServiceCategoryReady, setActiveTimeFrames]);
    const handleDayClick = (day: string) => {
        setSelectedDay(day);

        // Reset time values in the form
        form.setFieldsValue({
            startTime: null,
            endTime: null,
        });

        // Check if this day already has a time frame
        const existingTimeFrame = activeTimeFrames.find(
            (frame) => frame.dayOfWeek === dayMap[day]
        );

        if (existingTimeFrame) {
            // If this day already has a time frame, set the form values to match
            const startTime = existingTimeFrame.startTime
                ? dayjs(existingTimeFrame.startTime, 'HH:mm')
                : null;
            const endTime = existingTimeFrame.endTime
                ? dayjs(existingTimeFrame.endTime, 'HH:mm')
                : null;

            form.setFieldsValue({
                startTime: startTime,
                endTime: endTime,
            });

            console.log('Restored existing time frame for day:', day, {
                startTime: startTime?.format('HH:mm'),
                endTime: endTime?.format('HH:mm'),
            });
        } else {
            console.log('Reset time values for new day:', day);
        }
    };

    const onFinish: FormProps['onFinish'] = async (values) => {
        // Check for days with start time but no end time

        console.log('Values------------', values);

        const incompleteTimeFrame = activeTimeFrames.find(
            (frame) => frame.startTime && !frame.endTime
        );

        if (incompleteTimeFrame) {
            // Get the day name with first letter capitalized
            const dayName = capitalizeFirstLetter(
                incompleteTimeFrame.dayOfWeek
            );
            Alertify.error(`For ${dayName} the end time is not defined`);

            // Find the day abbreviation to switch to that day
            const dayAbbr = Object.keys(dayMap).find(
                (key) => dayMap[key] === incompleteTimeFrame.dayOfWeek
            );

            if (dayAbbr) {
                handleDayClick(dayAbbr);
            }

            return; // Prevent form submission
        }

        const [primaryCategoryId, primaryAppointmentTypeId] =
            values.category_appointment.value.split('|');
        let relationShip: any = [];
        if (values.secondary_service_type) {
            const relationShipMap: any = {};
            values.secondary_service_type.forEach((val: any) => {
                const [categoryId, appointmentTypeId] = val.value.split('|');

                if (!relationShipMap[categoryId]) {
                    relationShipMap[categoryId] = new Set();
                }
                relationShipMap[categoryId].add(appointmentTypeId);
            });

            relationShip = Object.entries(relationShipMap).map(
                ([categoryId, subtypeSet]) => ({
                    serviceCategory: categoryId,
                    subTypeIds: Array.from(subtypeSet as Set<unknown>),
                })
            );
        }
        const service: any = {
            type: values.serviceType,
            serviceCategory: primaryCategoryId,
            revenueCategory: values.revenueCategory,
            introductoryOffer: values.isOffer,
            sessionType: values.sessions,
        };

        if (primaryAppointmentTypeId) {
            service.appointmentType = [primaryAppointmentTypeId];
        }
        if (relationShip.length > 0) {
            service.relationShip = relationShip;
        }
        // Conditionally add `sessionCount` if `sessionType` is `multiple`
        if (values.sessions === 'multiple') {
            service.sessionCount = Number(values.sessionCount);
        } else if (values.sessions === 'single') {
            service.sessionCount = Number(1);
        } else if (values.sessions === 'unlimited') {
            service.sessionCount = Number(99999);
            service.sessionPerDay = values.maxCapacity || 1;
        } else if (values.sessions === 'day_pass') {
            service.dayPassLimit = Number(values.dayPassLimit || 1);
        }

        const payload: any = {
            name: values.name,
            price: Number(values.price),
            isSellOnline: values.isSellOnline || false,
            tax: Number(values.tax),
            expiredInDays: Number(values.expireInput),
            durationUnit: values.expirationUnit,
            membershipId: values.membership,
            hsnOrSacCode: values.hsnOrSacCode,
            revenueCategory: values.revenueCategory,
            services: service,
            promotionId: autoApplyId,
            applyPromotion: selectedDiscount,
            isInclusiveofGst: values.isInclusiveofGst,
            isTrialPricing: values?.isTrialPricing
                ? values.isTrialPricing
                : false,
            // timeFrameType: timeFrameType,
            // selectedDate: values.selectedDate,
            activeTimeFrames: activeTimeFrames.filter(
                (frame) => frame.startTime && frame.endTime // Only include frames with both times set
            ),
        };

        if (values.discountType && values.discountValue) {
            payload.discount = {
                type: values.discountType,
                value: Number(values.discountValue),
            };
        }
        // Update API hit
        if (id && id !== '0') {
            payload.pricingId = id;

            try {
                startSubmitLoader();
                await dispatch(UpdatePricingAPI(payload)).then((res: any) => {
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        setLocation('/pricing');
                        // setIsEditable(false);
                        // view = 'true';
                    }
                });
            } catch (error) {
                console.error('Error updating pricing:', error);
            } finally {
                endSubmitLoader();
            }
        }
        // Create API hit
        else {
            try {
                startSubmitLoader();
                await dispatch(CreatePricingAPI(payload)).then((res: any) => {
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        if (type === 'courses') setLocation('/courses-listing');
                        else setLocation('/pricing');
                    }
                });
            } catch (error) {
                console.error('Error creating pricing:', error);
            } finally {
                endSubmitLoader();
            }
        }
    };

    const toggleEditable = () => {
        setIsEditable((prev) => !prev);
    };
    const discountColumns = [
        {
            key: 'name',
            title: 'Name',
            dataIndex: 'name',
        },
        {
            key: 'type',
            title: 'Type',
            dataIndex: 'type',
        },
        {
            key: 'value',
            title: 'Value',
            dataIndex: 'value',
        },
        {
            key: 'action',
            title: 'Action',
            render: (record: any) => {
                return (
                    <Button
                        key={record._id}
                        className={`h-[30px] rounded-md p-2 py-0 text-xl ${
                            (view === 'true' && !isEditable) ||
                            autoApplyId === record._id
                                ? 'text-grey bg-[#ccc]'
                                : 'bg-purpleLight text-white'
                        }`}
                        disabled={
                            (view === 'true' && !isEditable ? true : false) ||
                            autoApplyId === record._id
                        }
                        onClick={() => {
                            setAutoApplyId(record._id);
                            // if (id != '0') {
                            //     form.submit();
                            // }
                        }}
                    >
                        Auto Apply
                    </Button>
                );
            },
        },
    ];
    // console.log('selectedDiscount is :', discountData);
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );

    useEffect(() => {
        if (role === RoleType.ORGANIZATION) return;

        dispatch(
            GetSettingActiveStatus({
                settingKey: 'settings_pin',
            })
        ).then((response: any) => {
            const settingData = response?.payload?.data?.data;
            const isEnabled = settingData?.isEnabled;
            const isActive = settingData?.isActive;

            if (isEnabled && isActive) {
                setRequirePinModal(true);
            } else {
                setRequirePinModal(false);
                setPinModalVisible(false);
            }
        });
    }, [role, dispatch]);

    const hasPricingUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.PRICING_PRICING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.PRICING_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasDiscountPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SERVICES_DISCOUNTS
            )
        );
    }, [all_permissions_for_role]);

    useEffect(() => {
        dispatch(RevenueCategoryList({}))
            .unwrap()
            .then((response: any) => {
                setRevenueCategoryOptions(response?.data?.data);
            })
            .catch((error: any) => {
                Alertify.error('Could not fetch Revenue Categories');
            });
    }, [setRevenueCategoryOptions]);

    // for time frame
    const handleTimeFrameType = (e: any) => {
        setTimeFrameType(e.target.value);
    };

    const daysOfWeek = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

    const handleTimeChange = (type: 'startTime' | 'endTime', time: any) => {
        const formValues = form.getFieldsValue();

        // Validate end time is not before start time
        if (type === 'endTime' && time && formValues.startTime) {
            if (time.isBefore(formValues.startTime)) {
                // Show error message
                Alertify.error('End time cannot be earlier than start time');
                return; // Don't update the form or state
            }
        }

        // Update the form field
        form.setFieldValue(type, time);

        if (!selectedDay) return;

        // Update activeTimeFrames if a day is selected
        setActiveTimeFrames((prev) => {
            // Handle null time values safely
            const formValues = form.getFieldsValue();
            const startTime =
                type === 'startTime'
                    ? time
                        ? time.format('HH:mm')
                        : ''
                    : formValues.startTime
                    ? formValues.startTime.format('HH:mm')
                    : '';

            const endTime =
                type === 'endTime'
                    ? time
                        ? time.format('HH:mm')
                        : ''
                    : formValues.endTime
                    ? formValues.endTime.format('HH:mm')
                    : '';

            // Find if the current day already has a time frame
            const existingIndex = prev.findIndex(
                (frame) => frame.dayOfWeek === dayMap[selectedDay]
            );

            if (existingIndex >= 0) {
                // Update existing time frame
                const updated = [...prev];
                updated[existingIndex] = {
                    ...updated[existingIndex],
                    startTime,
                    endTime,
                };

                console.log('Updated time frame for day:', selectedDay, {
                    startTime,
                    endTime,
                });

                return updated;
            } else {
                // Add new time frame
                const newTimeFrame = {
                    // date: '',
                    dayOfWeek: dayMap[selectedDay],
                    startTime,
                    endTime,
                };

                console.log(
                    'Added new time frame for day:',
                    selectedDay,
                    newTimeFrame
                );

                return [...prev, newTimeFrame];
            }
        });
    };
    // console.log('Current activeTimeFrames:', activeTimeFrames);

    useEffect(() => {
        if (activeTimeFrames.length > 0 && selectedDay) {
            handleDayClick(selectedDay);
        }
    }, [activeTimeFrames, selectedDay]);
    console.log('Active time frames:', activeTabKey);
    const onFinishFailed = (errorInfo: any) => {
        if (!errorInfo || !Array.isArray(errorInfo.errorFields)) return;
        const incompleteTimeFrame = activeTimeFrames.find(
            (frame) => frame.startTime && !frame.endTime
        );

        if (incompleteTimeFrame) {
            // Get the day name with first letter capitalized
            const dayName = capitalizeFirstLetter(
                incompleteTimeFrame.dayOfWeek
            );
            Alertify.error(`For ${dayName} the end time is not defined`);

            // Find the day abbreviation to switch to that day
            const dayAbbr = Object.keys(dayMap).find(
                (key) => dayMap[key] === incompleteTimeFrame.dayOfWeek
            );

            if (dayAbbr) {
                handleDayClick(dayAbbr);
            }

            return; // Prevent form submission
        }
        const activeTimeFrameFields = ['startTime', 'endTime'];
        const pricingDetailsFields = [
            'name',
            'price',
            'isInclusiveofGst',
            'tax',
            'hsnOrSacCode',
            'expireInput',
            'expirationUnit',
            'serviceType',
            'category_appointment',
            'sessions',
            'sessionCount',
            'dayPassLimit',
            'maxCapacity',
            'revenueCategory',
            'membership',
            'secondary_service_type',
        ];

        // Find errors for each tab
        const hasActiveTimeFrameError = errorInfo.errorFields.some(
            (field: any) => activeTimeFrameFields.includes(field.name[0])
        );
        const hasPricingDetailsError = errorInfo.errorFields.some(
            (field: any) => pricingDetailsFields.includes(field.name[0])
        );

        // If on activeTimeFrame tab, and no error in activeTimeFrame, but error in pricingDetails
        if (
            activeTabKey === 'activeTimeFrame' &&
            !hasActiveTimeFrameError &&
            hasPricingDetailsError
        ) {
            Alertify.success('Your Active Time Frame is saved');
            SetActiveTabKey('pricingDetails');
        } else if (
            activeTabKey === 'pricingDetails' &&
            !hasPricingDetailsError &&
            hasActiveTimeFrameError
        ) {
            Alertify.success('Your Pricing Details is saved');
            SetActiveTabKey('activeTimeFrame');
        }
    };
    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                    },
                    Form: {
                        verticalLabelMargin: -6,
                    },
                },
            }}
        >
            <div className="flex items-center justify-between  lg:pe-5">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="Back"
                        className="h-[10px] cursor-pointer"
                        onClick={() => {
                            if (activeTabKey === 'pricingDetails') {
                                goBack();
                            } else {
                                SetActiveTabKey('pricingDetails');
                            }
                        }}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {view === 'true' && !isEditable
                            ? 'View Pricing'
                            : id === '0'
                            ? 'Add a Pricing Option'
                            : 'Edit Pricing Option'}
                    </Title>
                </div>
                {view === 'true' && !isEditable && (
                    <div onClick={() => toggleEditable()}>
                        {(hasPricingUpdatePermission ||
                            store.role === RoleType.ORGANIZATION) && (
                            <img
                                src="/icons/common/edit.svg"
                                alt="edit"
                                className="ms-auto h-[20px] cursor-pointer"
                            />
                        )}
                    </div>
                )}
            </div>

            {/* {loader ? (
                <FullLoader state={true} />
            ) : ( */}
            <div className="lg:mt-11 @sm:mt-5">
                <div className="">
                    <Form
                        name="PricingCreate"
                        layout="horizontal"
                        size="large"
                        form={form}
                        initialValues={{
                            remember: true,
                            sections: [{}],
                            isSellOnline: false,
                            serviceType: type || null,
                        }}
                        onFinish={onFinish}
                        onFinishFailed={onFinishFailed}
                        autoComplete="off"
                        onValuesChange={onValuesChange}
                        disabled={view === 'true' && !isEditable}
                    >
                        <Tabs
                            activeKey={activeTabKey}
                            onChange={(key) => SetActiveTabKey(key)}
                            items={[
                                {
                                    label: 'Pricing Details',
                                    key: 'pricingDetails',
                                    children: (
                                        <div className="rounded-3xl border lg:p-16 @sm:p-5">
                                            <Form.Item
                                                label="Pricing option Name"
                                                name="name"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please enter pricing option name!',
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    maxLength={50}
                                                    placeholder="Enter Pricing option Name"
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                label="Price"
                                                name="price"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please enter a valid price!',
                                                    },
                                                ]}
                                            >
                                                <Input
                                                    type="text"
                                                    placeholder="Enter Price"
                                                    maxLength={10}
                                                    onKeyPress={(event) => {
                                                        if (
                                                            !/^\d$/.test(
                                                                event.key
                                                            )
                                                        ) {
                                                            event.preventDefault();
                                                        }
                                                    }}
                                                    onInput={(e: any) => {
                                                        e.target.value =
                                                            e.target.value.replace(
                                                                /[^0-9]/g,
                                                                ''
                                                            );
                                                    }}
                                                />
                                            </Form.Item>

                                            <Form.Item
                                                noStyle
                                                shouldUpdate={(prev, current) =>
                                                    prev.tax !== current.tax
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    return getFieldValue(
                                                        'tax'
                                                    ) !== '0' ? (
                                                        <Form.Item
                                                            label="Inclusive GST"
                                                            name="isInclusiveofGst"
                                                            valuePropName="checked"
                                                        >
                                                            <Checkbox>
                                                                Inclusive GST
                                                            </Checkbox>
                                                        </Form.Item>
                                                    ) : null;
                                                }}
                                            </Form.Item>

                                            {/* <Form.Item
                                                label="Trial Class"
                                                name="isTrialPricing"
                                                valuePropName="checked"
                                            >
                                                <Checkbox
                                                    onChange={(e) => {
                                                        const isChecked =
                                                            e.target.checked;
                                                        setTrialPricingClass(
                                                            isChecked
                                                        );
                                                        if (isChecked) {
                                                            // Optionally reset sessions selection
                                                            form.setFieldsValue(
                                                                {
                                                                    sessions:
                                                                        'single',
                                                                }
                                                            );
                                                        }
                                                    }}
                                                >
                                                    Trial Class
                                                </Checkbox>
                                            </Form.Item> */}

                                            {(hasDiscountPermission ||
                                                store.role ===
                                                    RoleType.ORGANIZATION) && (
                                                <div className="mb-5 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                                                    <div className="checkbox-custom">
                                                        <Paragraph className="ant-form-item-label mb-0">
                                                            <label>
                                                                Discount:
                                                            </label>
                                                        </Paragraph>
                                                    </div>
                                                    <div className="flex items-center gap-8 lg:w-[80%]  lg:flex-row @sm:flex-col">
                                                        <div className="w-1/2 rounded-lg border">
                                                            <div
                                                                ref={
                                                                    tableContainerRef
                                                                }
                                                                // className="max-h-[300px] overflow-y-auto"
                                                                // style={{
                                                                //     maxHeight:
                                                                //         '300px',
                                                                //     overflowY:
                                                                //         'auto',
                                                                // }}
                                                            >
                                                                <Table
                                                                    columns={
                                                                        discountColumns
                                                                    }
                                                                    dataSource={
                                                                        discountData
                                                                    }
                                                                    pagination={
                                                                        false
                                                                    }
                                                                    rowKey="_id"
                                                                    scroll={{
                                                                        // x: 'calc(300px + 50%)',
                                                                        y:
                                                                            47 *
                                                                            5,
                                                                    }}
                                                                    size="small"
                                                                />
                                                                {loading && (
                                                                    <div className="flex justify-center py-4">
                                                                        <Spin size="small" />
                                                                    </div>
                                                                )}
                                                                {!hasMore &&
                                                                    discountData.length >
                                                                        0 && (
                                                                        <div className="flex hidden justify-center py-2 text-sm text-gray-500">
                                                                            No
                                                                            more
                                                                            data
                                                                            to
                                                                            load
                                                                        </div>
                                                                    )}
                                                                <div className="flex hidden justify-center py-2">
                                                                    <Button
                                                                        size="small"
                                                                        onClick={() => {
                                                                            const nextPage =
                                                                                currentPage +
                                                                                1;
                                                                            setCurrentPage(
                                                                                nextPage
                                                                            );
                                                                            fetchDiscountData(
                                                                                nextPage,
                                                                                true
                                                                            );
                                                                        }}
                                                                        disabled={
                                                                            loading ||
                                                                            !hasMore
                                                                        }
                                                                    >
                                                                        Load
                                                                        More
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <Button
                                                            className={`fw-500 flex items-center rounded-lg border px-8 py-3 text-xl ${
                                                                view ===
                                                                    'true' &&
                                                                !isEditable
                                                                    ? 'text-grey bg-[#ccc]'
                                                                    : 'bg-purpleLight text-white'
                                                            } `}
                                                            onClick={() =>
                                                                setAddDiscountModal(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            Apply Discount
                                                        </Button>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="mb-5 flex justify-between">
                                                <div className="@sm:hidden"></div>
                                                <div className="lg:w-[80%]">
                                                    <ConfigProvider
                                                        theme={{
                                                            token: {
                                                                // colorBorder: '#8143D1',
                                                                colorPrimary:
                                                                    '#8143D1',
                                                                colorPrimaryHover:
                                                                    '#8143D1',
                                                            },
                                                        }}
                                                    >
                                                        <Form.Item
                                                            label=""
                                                            name="isSellOnline"
                                                            valuePropName="checked"
                                                        >
                                                            <Checkbox>
                                                                Sell Online
                                                            </Checkbox>
                                                        </Form.Item>
                                                    </ConfigProvider>
                                                </div>
                                            </div>
                                            <div className="mb-5 flex items-center lg:justify-between @sm:gap-5">
                                                <div className="checkbox-custom">
                                                    <Paragraph className="ant-form-item-label mb-0">
                                                        <label>
                                                            Tax:
                                                            <span className="ms-1 text-2xl text-red-400">
                                                                *
                                                            </span>
                                                        </label>
                                                    </Paragraph>
                                                </div>
                                                <div className="lg:grid  lg:w-[80%]">
                                                    <Form.Item
                                                        label=""
                                                        name="tax"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    'Please select tax !',
                                                            },
                                                        ]}
                                                    >
                                                        <Radio.Group className="lg:grid">
                                                            <Radio value="18">
                                                                {' '}
                                                                GST 18 %{' '}
                                                            </Radio>
                                                            <Radio value="12">
                                                                {' '}
                                                                GST 12 %
                                                            </Radio>
                                                            <Radio value="5">
                                                                {' '}
                                                                GST 5 %
                                                            </Radio>
                                                            <Radio value="0">
                                                                {' '}
                                                                No GST{' '}
                                                            </Radio>
                                                        </Radio.Group>
                                                    </Form.Item>
                                                </div>
                                            </div>

                                            <Form.Item
                                                noStyle
                                                shouldUpdate={(
                                                    prevValues,
                                                    currentValues
                                                ) =>
                                                    prevValues?.tax !==
                                                    currentValues?.tax
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    return getFieldValue(
                                                        'tax'
                                                    ) !== '0' ? (
                                                        <Form.Item
                                                            label="HSN / SAC"
                                                            name="hsnOrSacCode"
                                                            rules={[
                                                                {
                                                                    required:
                                                                        true,
                                                                    message:
                                                                        'Please enter HSN / SAC Code!',
                                                                },
                                                            ]}
                                                        >
                                                            <Input
                                                                maxLength={15}
                                                                placeholder="Enter HSN / SAC Code"
                                                            />
                                                        </Form.Item>
                                                    ) : null;
                                                }}
                                            </Form.Item>

                                            <div className="mb-5 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                                                <div className="checkbox-custom">
                                                    <Paragraph className="ant-form-item-label mb-0">
                                                        <label>
                                                            Expire:
                                                            <span className="ms-1 text-2xl text-red-400">
                                                                *
                                                            </span>
                                                        </label>
                                                    </Paragraph>
                                                </div>
                                                <div className="flex items-center lg:w-[80%] lg:flex-row @sm:flex-col">
                                                    <Form.Item
                                                        label=""
                                                        name="expireInput"
                                                        className="w-[40%]"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    'Please enter a expiry!',
                                                            },
                                                        ]}
                                                    >
                                                        <Input placeholder="Will expire in days" />
                                                    </Form.Item>

                                                    <Form.Item
                                                        label=""
                                                        name="expirationUnit"
                                                        className="w-[40%]"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    'Please select duration type!',
                                                            },
                                                        ]}
                                                    >
                                                        <Select
                                                            showSearch
                                                            placeholder="Select Duration"
                                                            filterOption={(
                                                                input,
                                                                option
                                                            ) =>
                                                                typeof option?.label ===
                                                                    'string' &&
                                                                option.label
                                                                    .toLowerCase()
                                                                    .includes(
                                                                        input.toLowerCase()
                                                                    )
                                                            }
                                                            options={[
                                                                {
                                                                    label: 'Days',
                                                                    value: 'days',
                                                                },
                                                                {
                                                                    label: 'Months',
                                                                    value: 'months',
                                                                },
                                                                {
                                                                    label: 'Years',
                                                                    value: 'years',
                                                                },
                                                            ]}
                                                        />
                                                    </Form.Item>
                                                </div>
                                            </div>
                                            <Form.Item
                                                label="Type of Services"
                                                name="serviceType"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please select service type!',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    showSearch
                                                    placeholder="Select type of service"
                                                    disabled={
                                                        type ||
                                                        (view === 'true' &&
                                                            !isEditable)
                                                    }
                                                    filterOption={(
                                                        input,
                                                        option
                                                    ) =>
                                                        (option?.label ?? '')
                                                            .toLowerCase?.()
                                                            .includes(
                                                                input.toLowerCase()
                                                            )
                                                    }
                                                    options={[
                                                        {
                                                            label: 'Personal Appointment',
                                                            value: 'personalAppointment',
                                                        },
                                                        {
                                                            label: 'Classes',
                                                            value: 'classes',
                                                        },
                                                        {
                                                            label: 'Bookings',
                                                            value: 'bookings',
                                                        },
                                                        ...(isCourseEnabled
                                                            ? [
                                                                  {
                                                                      label: 'Courses',
                                                                      value: 'courses',
                                                                  },
                                                              ]
                                                            : []),
                                                    ]}
                                                    onChange={() => {
                                                        form.resetFields([
                                                            'category_appointment',
                                                            'secondary_service_type',
                                                        ]);
                                                    }}
                                                />
                                            </Form.Item>

                                            {serviceCategoryData.length > 0 && (
                                                <Form.Item
                                                    label="Primary Service Type"
                                                    name="category_appointment"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please select a service type',
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        labelInValue
                                                        optionLabelProp="label"
                                                        placeholder="Select service type"
                                                        onChange={(value) => {
                                                            setPrimaryServiceType(
                                                                value.value
                                                            );
                                                            form.setFieldValue(
                                                                'secondary_service_type',
                                                                undefined
                                                            );
                                                            form.setFieldValue(
                                                                'category_appointment',
                                                                value
                                                            );
                                                        }}
                                                    >
                                                        {serviceCategoryData.map(
                                                            (category) => {
                                                                const uniqueAppointments =
                                                                    Array.from(
                                                                        new Map(
                                                                            category.appointmentType.map(
                                                                                (
                                                                                    appt: any
                                                                                ) => [
                                                                                    appt._id,
                                                                                    appt,
                                                                                ]
                                                                            )
                                                                        ).values()
                                                                    );

                                                                return (
                                                                    <OptGroup
                                                                        key={
                                                                            category._id
                                                                        }
                                                                        label={
                                                                            category.name
                                                                        }
                                                                    >
                                                                        {uniqueAppointments.map(
                                                                            (
                                                                                appt: any
                                                                            ) => {
                                                                                const fullValue = `${category._id}|${appt._id}`;
                                                                                return (
                                                                                    !selectedSecondaryValues.includes(
                                                                                        fullValue
                                                                                    ) && (
                                                                                        <Option
                                                                                            key={
                                                                                                fullValue
                                                                                            }
                                                                                            value={
                                                                                                fullValue
                                                                                            }
                                                                                            label={getLabelFromValue(
                                                                                                fullValue
                                                                                            )}
                                                                                        >
                                                                                            {
                                                                                                appt.name
                                                                                            }
                                                                                        </Option>
                                                                                    )
                                                                                );
                                                                            }
                                                                        )}
                                                                    </OptGroup>
                                                                );
                                                            }
                                                        )}
                                                    </Select>
                                                </Form.Item>
                                            )}

                                            <Form.Item
                                                label="Secondary Service Type"
                                                name="secondary_service_type"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please select a secondary service type',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    placeholder="Select secondary service type"
                                                    mode="multiple"
                                                    labelInValue
                                                    optionLabelProp="label"
                                                    onChange={(values) => {
                                                        const selectedValues =
                                                            values.map(
                                                                (v: any) =>
                                                                    v.value
                                                            );
                                                        setSelectedSecondaryValues(
                                                            selectedValues
                                                        );
                                                        form.setFieldValue(
                                                            'secondary_service_type',
                                                            values
                                                        );
                                                    }}
                                                >
                                                    {serviceCategoryData.map(
                                                        (category) => {
                                                            const uniqueAppointments =
                                                                Array.from(
                                                                    new Map(
                                                                        category.appointmentType.map(
                                                                            (
                                                                                appt: any
                                                                            ) => [
                                                                                appt._id,
                                                                                appt,
                                                                            ]
                                                                        )
                                                                    ).values()
                                                                );

                                                            const filteredAppointments =
                                                                uniqueAppointments.filter(
                                                                    (
                                                                        appt: any
                                                                    ) => {
                                                                        const fullValue = `${category._id}|${appt._id}`;
                                                                        return (
                                                                            fullValue !==
                                                                            primaryServiceType
                                                                        );
                                                                    }
                                                                );
                                                            if (
                                                                filteredAppointments.length ===
                                                                0
                                                            )
                                                                return null;

                                                            return (
                                                                <OptGroup
                                                                    key={
                                                                        category._id
                                                                    }
                                                                    label={
                                                                        category.name
                                                                    }
                                                                >
                                                                    {filteredAppointments.map(
                                                                        (
                                                                            appt: any
                                                                        ) => {
                                                                            const value = `${category._id}|${appt._id}`;
                                                                            return (
                                                                                <Option
                                                                                    key={`${category._id}-${appt._id}`}
                                                                                    value={
                                                                                        value
                                                                                    }
                                                                                    label={getLabelFromValue(
                                                                                        value
                                                                                    )}
                                                                                >
                                                                                    {
                                                                                        appt.name
                                                                                    }
                                                                                </Option>
                                                                            );
                                                                        }
                                                                    )}
                                                                </OptGroup>
                                                            );
                                                        }
                                                    )}
                                                </Select>
                                            </Form.Item>
                                            <div className="mb-5 flex items-start justify-between">
                                                <div className="checkbox-custom">
                                                    <Paragraph className="ant-form-item-label mb-0">
                                                        <label>
                                                            Number of Sessions :
                                                            <span className="ms-1 text-2xl text-red-400">
                                                                *
                                                            </span>
                                                        </label>
                                                    </Paragraph>
                                                </div>
                                                <div className="flex w-[80%] items-center ">
                                                    <Form.Item
                                                        label=""
                                                        className="w-1/3"
                                                        name="sessions"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    'Please select session type!',
                                                            },
                                                        ]}
                                                    >
                                                        <Radio.Group className="grid gap-6">
                                                            <Radio value="single">
                                                                Single Session
                                                            </Radio>
                                                            <Radio
                                                                value="multiple"
                                                                disabled={
                                                                    trialPricingClass
                                                                }
                                                            >
                                                                Multiple
                                                                Sessions
                                                            </Radio>
                                                            {form.getFieldValue(
                                                                'serviceType'
                                                            ) !== 'courses' && (
                                                                <Radio
                                                                    value="day_pass"
                                                                    disabled={
                                                                        trialPricingClass
                                                                    }
                                                                >
                                                                    Day Pass
                                                                </Radio>
                                                            )}
                                                            {form.getFieldValue(
                                                                'serviceType'
                                                            ) !== 'courses' && (
                                                                <Radio
                                                                    value="unlimited"
                                                                    disabled={
                                                                        trialPricingClass
                                                                    }
                                                                >
                                                                    Unlimited
                                                                    Sessions
                                                                </Radio>
                                                            )}
                                                        </Radio.Group>
                                                    </Form.Item>
                                                    <Form.Item
                                                        noStyle
                                                        shouldUpdate={(
                                                            prevValues,
                                                            currentValues
                                                        ) =>
                                                            prevValues?.sessions !==
                                                            currentValues?.sessions
                                                        }
                                                    >
                                                        {({
                                                            getFieldValue,
                                                        }) => {
                                                            const selectedSessionType =
                                                                getFieldValue(
                                                                    'sessions'
                                                                );
                                                            return (
                                                                selectedSessionType ===
                                                                    'multiple' && (
                                                                    <Form.Item
                                                                        label="Total Sessions"
                                                                        name={
                                                                            'sessionCount'
                                                                        }
                                                                        rules={[
                                                                            {
                                                                                required:
                                                                                    true,
                                                                                message:
                                                                                    'Please enter the total number of sessions!',
                                                                            },
                                                                        ]}
                                                                    >
                                                                        <Input placeholder="Enter total sessions" />
                                                                    </Form.Item>
                                                                )
                                                            );
                                                        }}
                                                    </Form.Item>
                                                    <Form.Item
                                                        noStyle
                                                        shouldUpdate={(
                                                            prev,
                                                            current
                                                        ) =>
                                                            prev?.sessions !==
                                                            current?.sessions
                                                        }
                                                    >
                                                        {({
                                                            getFieldValue,
                                                        }) => {
                                                            return (
                                                                getFieldValue(
                                                                    'sessions'
                                                                ) ===
                                                                    'day_pass' && (
                                                                    <Form.Item
                                                                        label="Number of Days"
                                                                        name="dayPassLimit"
                                                                        rules={[
                                                                            {
                                                                                required:
                                                                                    true,
                                                                                message:
                                                                                    'Please enter number of days!',
                                                                            },
                                                                        ]}
                                                                        initialValue={
                                                                            1
                                                                        }
                                                                    >
                                                                        <Input
                                                                            min={
                                                                                1
                                                                            }
                                                                            placeholder="Enter number of days"
                                                                        />
                                                                    </Form.Item>
                                                                )
                                                            );
                                                        }}
                                                    </Form.Item>
                                                    <Form.Item
                                                        noStyle
                                                        shouldUpdate={(
                                                            prevValues,
                                                            currentValues
                                                        ) =>
                                                            prevValues?.sessions !==
                                                            currentValues?.sessions
                                                        }
                                                    >
                                                        {({
                                                            getFieldValue,
                                                        }) => {
                                                            const selectedSessionType =
                                                                getFieldValue(
                                                                    'sessions'
                                                                );
                                                            return (
                                                                selectedSessionType ===
                                                                    'unlimited' && (
                                                                    <Form.Item
                                                                        label="Per Day Limit"
                                                                        name={
                                                                            'maxCapacity'
                                                                        }
                                                                        rules={[
                                                                            {
                                                                                required:
                                                                                    true,
                                                                                message:
                                                                                    'Please enter the total number of sessions!',
                                                                            },
                                                                        ]}
                                                                    >
                                                                        <Input
                                                                            placeholder="Enter total sessions"
                                                                            className=""
                                                                        />
                                                                    </Form.Item>
                                                                )
                                                            );
                                                        }}
                                                    </Form.Item>
                                                </div>
                                            </div>
                                            <Form.Item
                                                label="Revenue Category"
                                                name="revenueCategory"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            'Please select Revenue Category!',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    showSearch
                                                    placeholder="Select Revenue Category"
                                                    filterOption={(
                                                        input,
                                                        option
                                                    ) =>
                                                        (option?.label ?? '')
                                                            .toLowerCase()
                                                            .includes(
                                                                input.toLowerCase()
                                                            )
                                                    }
                                                    options={revenueCategoryOptions?.map(
                                                        (item: any) => ({
                                                            label: item.name,
                                                            value: item._id,
                                                        })
                                                    )}
                                                    // onChange={() => {}}
                                                />
                                            </Form.Item>
                                            <div>
                                                <div className="mb-5 flex items-center justify-between">
                                                    <div className="checkbox-custom md:w-[20%]"></div>
                                                    <div className="grid lg:w-[80%]">
                                                        <Paragraph className="text-md mb-0">
                                                            Does a client become
                                                            a member when they
                                                            purchase this
                                                            pricing option? If
                                                            so, select a
                                                            membership below.
                                                        </Paragraph>
                                                    </div>
                                                </div>
                                                <Form.Item
                                                    label="Membership"
                                                    name="membership"
                                                    rules={[
                                                        {
                                                            required: false,
                                                            message:
                                                                'Please select membership!',
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        showSearch
                                                        allowClear={true}
                                                        placeholder="Select membership (optional)"
                                                        filterOption={(
                                                            input,
                                                            option
                                                        ) =>
                                                            (
                                                                option?.label ??
                                                                ''
                                                            )
                                                                .toLowerCase()
                                                                .includes(
                                                                    input.toLowerCase()
                                                                )
                                                        }
                                                        options={
                                                            store.membershipList?.map(
                                                                (
                                                                    membership: any
                                                                ) => ({
                                                                    label: capitalizeFirstLetter(
                                                                        membership.name
                                                                    ),
                                                                    value: membership._id,
                                                                })
                                                            ) || []
                                                        }
                                                    />
                                                </Form.Item>
                                            </div>
                                        </div>
                                    ),
                                },
                                {
                                    key: 'activeTimeFrame',
                                    label: 'Active Time Frame',
                                    children: (
                                        <div className="rounded-3xl border lg:p-16 @sm:p-5">
                                            <div className="flex flex-row items-center justify-between pb-9 text-[13px] font-medium text-[#1A3353]">
                                                Select Days
                                                <div className="flex flex-wrap lg:w-[80%] lg:gap-3 @xl:gap-8">
                                                    {daysOfWeek?.map((day) => {
                                                        // Check if this day has an active time frame
                                                        const hasActiveTimeFrame =
                                                            activeTimeFrames.some(
                                                                (frame) =>
                                                                    frame.dayOfWeek ===
                                                                    dayMap[day]
                                                            );

                                                        return (
                                                            <div key={day}>
                                                                <Button
                                                                    shape="circle"
                                                                    disabled={
                                                                        false
                                                                    }
                                                                    className={`p-2 ${
                                                                        selectedDay?.includes(
                                                                            day
                                                                        )
                                                                            ? 'bg-[#455560] text-white'
                                                                            : hasActiveTimeFrame
                                                                            ? 'border-purple-200 bg-purple-200'
                                                                            : 'bg-white'
                                                                    }`}
                                                                    onClick={() =>
                                                                        handleDayClick(
                                                                            day
                                                                        )
                                                                    }
                                                                >
                                                                    {capitalizeFirstLetter(
                                                                        day.slice(
                                                                            0,
                                                                            3
                                                                        )
                                                                    )}
                                                                </Button>
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            </div>
                                            <div className="w-full">
                                                <Form.Item
                                                    label="Start Time"
                                                    name="startTime"
                                                >
                                                    <TimePicker
                                                        format="HH:mm"
                                                        minuteStep={5}
                                                        onChange={(time) =>
                                                            handleTimeChange(
                                                                'startTime',
                                                                time
                                                            )
                                                        }
                                                        onClear={() =>
                                                            handleTimeChange(
                                                                'startTime',
                                                                null
                                                            )
                                                        }
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    label="End Time"
                                                    name="endTime"
                                                    rules={[
                                                        // {
                                                        //     required: true,
                                                        //     message:
                                                        //         'Please select end time!',
                                                        // },
                                                        {
                                                            validator: (
                                                                _,
                                                                value
                                                            ) => {
                                                                const startTime =
                                                                    form.getFieldValue(
                                                                        'startTime'
                                                                    );
                                                                if (
                                                                    value &&
                                                                    startTime &&
                                                                    value.isBefore(
                                                                        startTime
                                                                    )
                                                                ) {
                                                                    return Promise.reject(
                                                                        'End time cannot be earlier than start time'
                                                                    );
                                                                }
                                                                return Promise.resolve();
                                                            },
                                                        },
                                                    ]}
                                                >
                                                    <TimePicker
                                                        format="HH:mm"
                                                        minuteStep={5}
                                                        onChange={(time) =>
                                                            handleTimeChange(
                                                                'endTime',
                                                                time
                                                            )
                                                        }
                                                        onClear={() =>
                                                            handleTimeChange(
                                                                'endTime',
                                                                null
                                                            )
                                                        }
                                                        disabledTime={() => {
                                                            const startTime =
                                                                form.getFieldValue(
                                                                    'startTime'
                                                                );
                                                            if (!startTime)
                                                                return {};

                                                            return {
                                                                disabledHours:
                                                                    () => {
                                                                        const hours =
                                                                            [];
                                                                        for (
                                                                            let i = 0;
                                                                            i <
                                                                            startTime.hour();
                                                                            i++
                                                                        ) {
                                                                            hours.push(
                                                                                i
                                                                            );
                                                                        }
                                                                        return hours;
                                                                    },
                                                                disabledMinutes:
                                                                    (
                                                                        selectedHour
                                                                    ) => {
                                                                        if (
                                                                            selectedHour ===
                                                                            startTime.hour()
                                                                        ) {
                                                                            const minutes =
                                                                                [];
                                                                            for (
                                                                                let i = 0;
                                                                                i <
                                                                                startTime.minute();
                                                                                i++
                                                                            ) {
                                                                                minutes.push(
                                                                                    i
                                                                                );
                                                                            }
                                                                            return minutes;
                                                                        }
                                                                        return [];
                                                                    },
                                                            };
                                                        }}
                                                    />
                                                </Form.Item>
                                            </div>
                                        </div>
                                    ),
                                },
                            ]}
                        />
                        {!(view === 'true' && !isEditable) && (
                            <div className="ms-auto mt-8 text-right">
                                <Button
                                    htmlType="submit"
                                    className="Paragraph-white   me-0  ms-auto bg-purpleLight text-white"
                                    loading={submitLoader}
                                    disabled={loader}
                                >
                                    Submit
                                </Button>
                            </div>
                        )}
                    </Form>
                </div>
            </div>
            {/* // )} */}
            {pinModalVisible && requirePinModal && (
                <ModulePinConfirmationModal
                    visible={pinModalVisible}
                    onConfirm={() => setPinModalVisible(false)}
                    module={SUBJECT_TYPE.PRICING_PRICING}
                    subModule={PERMISSIONS_ENUM.PRICING_WRITE}
                    onCancel={handleClose}
                />
            )}

            {addDiscountModal && (
                <AddDiscountModal
                    visible={addDiscountModal}
                    pricingId={id}
                    priceToCompare={form.getFieldValue('price')}
                    setDiscountData={setDiscountData}
                    selectedDiscount={selectedDiscount}
                    setSelectedDiscount={setSelectedDiscount}
                    setComingDiscounts={setComingDiscounts}
                    comingDiscounts={comingDiscounts}
                    onCancel={handleDiscountModalClose}
                />
            )}
        </ConfigProvider>
    );
};

export default CreatePricing;
