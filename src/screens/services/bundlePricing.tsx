import { Button, Checkbox, Form, Input, Radio, Select } from 'antd';
import Paragraph from 'antd/es/typography/Paragraph';
import Title from 'antd/es/typography/Title';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useLocation, useParams } from 'wouter';
import { capitalizeFirstLetter, goBack } from '~/components/common/function';
import FullLoader from '~/components/library/loader/full-loader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import {
    BundlePricingListByActiveStatus,
    CreateBundlePricingAPI,
    PricingDetails,
    BundleUpdatePricingAPI,
} from '~/redux/actions/pricing-actions';
import { PERMISSIONS_ENUM, RoleType, SUBJECT_TYPE } from '~/types/enums';
import { getQueryParams } from '~/utils/getQueryParams';
import ModulePinConfirmationModal from '~/components/modals/module-pin-confirmation-modal';
import { useSelector } from 'react-redux';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';

const BundlePricing = () => {
    const [form] = Form.useForm();
    const { id } = useParams<{ id: string }>();
    const dispatch = useAppDispatch();
    const [_, setLocation] = useLocation();
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const [pricingLoader, startPricingLoader, endPricingLoader] = useLoader();
    const [loader, startLoader, endLoader] = useLoader();
    const [loading, setLoading] = useState<boolean>(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const [page, setPage] = useState<number>(1);
    const pageSize = 20;
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [search, setSearch] = useState<string>('');
    const [isEditable, setIsEditable] = useState<boolean>();
    const params = getQueryParams();
    const { view } = params;
    const [selectedPackages, setSelectedPackages] = useState<any[]>([]);
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);
    const [requirePinModal, setRequirePinModal] = useState<boolean>(false);

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        pricingListByActiveStatus:
            state.pricing_store.bundlePricingListByActiveStatus,
        pricingListByActiveStatusCount:
            state.pricing_store.bundlePricingListByActiveStatusCount,
        pricingDetailData: state.pricing_store.pricingDetailData,
    }));
    console.log('Store--------------', store.pricingListByActiveStatusCount);
    const { role } = useSelector((state: any) => state.auth_store);

    useEffect(() => {
        if (id !== '0') {
            startLoader();
            dispatch(PricingDetails({ pricingId: id }))
                .unwrap()
                .finally(endLoader);
        }
    }, [id]);

    useEffect(() => {
        if (role === RoleType.ORGANIZATION) return;

        dispatch(
            GetSettingActiveStatus({
                settingKey: 'settings_pin',
            })
        ).then((response: any) => {
            const settingData = response?.payload?.data?.data;
            const isEnabled = settingData?.isEnabled;
            const isActive = settingData?.isActive;

            if (isEnabled && isActive) {
                setRequirePinModal(true);
            } else {
                setRequirePinModal(false);
                setPinModalVisible(false);
            }
        });
    }, [role, dispatch]);

    const onValuesChange = (changedValues: any, allValues: any) => {
        console.log('onValuesChange-----------', changedValues, allValues);
        const actualPrice = Number(allValues.BundlePrice) || 0;
        const discountValue = Number(allValues.BundleDiscountValue) || 0;
        let finalPrice = actualPrice;

        if (
            changedValues.BundleDiscountValue !== undefined ||
            changedValues.BundleDiscountType
        ) {
            if (changedValues.BundleDiscountType && !discountValue) {
                form.setFields([
                    {
                        name: 'BundleDiscountValue',
                        errors: ['Please enter discount value!'],
                    },
                ]);
                return;
            }

            if (allValues.BundleDiscountType === 'Percentage') {
                if (discountValue > 100) {
                    form.setFields([
                        {
                            name: 'BundleDiscountValue',
                            errors: ['Percentage cannot exceed 100!'],
                        },
                    ]);
                    return;
                }
                finalPrice = actualPrice - (actualPrice * discountValue) / 100;
            } else if (allValues.BundleDiscountType === 'Flat') {
                finalPrice = actualPrice - discountValue;
            }

            if (finalPrice < 0 || finalPrice > actualPrice) {
                form.setFields([
                    {
                        name: 'BundleDiscountValue',
                        errors: [
                            'Invalid discount! Final price cannot be negative or greater than the actual price.',
                        ],
                    },
                ]);
            } else {
                form.setFields([
                    {
                        name: 'BundleDiscountValue',
                        errors: [],
                    },
                ]);
            }
        }
    };

    useEffect(() => {
        const selected = form.getFieldValue('BundlePackageListing') || [];
        setSelectedPackages(selected);
        const accessFlag = sessionStorage.getItem('accessViaSecureFlow');
        if (role === RoleType.ORGANIZATION || (id || '0') !== '0') return;
        else if (accessFlag === 'true')
            sessionStorage.removeItem('accessViaSecureFlow');
        else setPinModalVisible(true);
    }, []);

    const handleClose = () => {
        setLocation('/pricing?pricingType=bundlePricing&page=1&pageSize=10');
    };

    const fetchPricingList = (
        pageNumber: number,
        isScroll: boolean = false
    ) => {
        if (isScroll && (loading || !hasMore)) return;

        setLoading(true);
        const obj: any = {
            page: pageNumber,
            pageSize,
            fetchWithBundled: false,
        };
        if (search) obj.search = search;
        dispatch(BundlePricingListByActiveStatus(obj))
            .unwrap()
            .then((res: any) => {
                const totalItemsLoaded = pageNumber * pageSize;
                const totalItemsAvailable =
                    res?.response?.data?.data?.count || 0;

                if (totalItemsLoaded >= totalItemsAvailable) {
                    setHasMore(false);
                }
            })
            .finally(() => setLoading(false));
    };

    useEffect(() => {
        if (search.length)
            debouncedRequest(() => {
                fetchPricingList(1);
            });
        else fetchPricingList(1);
    }, [search]);

    const handleScroll = useCallback(() => {
        const container = containerRef.current;
        if (container) {
            const { scrollTop, scrollHeight, clientHeight } = container;
            if (
                scrollTop + clientHeight >= scrollHeight - 10 &&
                hasMore &&
                !loading
            ) {
                const nextPage = page + 1;
                setPage(nextPage);
                fetchPricingList(nextPage, true);
            }
        }
    }, [hasMore, loading, page]);

    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
        }
        return () => {
            if (container) {
                container.removeEventListener('scroll', handleScroll);
            }
        };
    }, [handleScroll]);

    useEffect(() => {
        if (id && id !== '0' && store.pricingDetailData) {
            form.setFieldsValue({
                name: store.pricingDetailData?.name,
                // BundlePrice: store.pricingDetailData?.price,
                BundlePrice: store.pricingDetailData.isInclusiveofGst
                    ? store.pricingDetailData.finalPrice
                    : store.pricingDetailData.price,
                BundleIsSellOnline: store.pricingDetailData?.isSellOnline,
                BundleTax: store.pricingDetailData?.tax?.toString(),
                BundleDiscountType: store.pricingDetailData?.discount?.type,
                BundleDiscountValue: Number(
                    store.pricingDetailData?.discount?.value
                ),
                BundleExpireInput: store.pricingDetailData?.expiredInDays,
                BundleExpirationUnit: store.pricingDetailData?.durationUnit,
                hsnOrSacCode: store.pricingDetailData?.hsnOrSacCode,
                BundlePackageListing: store.pricingDetailData?.pricingIds,
                isInclusiveofGst: store.pricingDetailData.isInclusiveofGst,
            });
        }
    }, [id, store.pricingDetailData, form]);

    const onFinish = (values: any) => {
        startLoader();
        const payload: any = {
            name: values.name,
            price: Number(values.BundlePrice),
            isSellOnline: values.BundleIsSellOnline || false,
            tax: Number(values.BundleTax),
            discount: {
                type: values.BundleDiscountType,
                value: Number(values.BundleDiscountValue),
            },
            expiredInDays: Number(values.BundleExpireInput),
            durationUnit: values.BundleExpirationUnit,
            hsnOrSacCode: values.hsnOrSacCode,
            pricingIds: values.BundlePackageListing || [],
            isInclusiveofGst: values.isInclusiveofGst,
        };

        console.log('Generated Payload:-----------', payload);
        if (id === '0') {
            dispatch(CreateBundlePricingAPI(payload))
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        setLocation('/pricing?pricingType=bundlePricing');
                    }
                })
                .finally(endLoader);
        } else {
            payload['pricingId'] = id;
            dispatch(BundleUpdatePricingAPI(payload))
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        setLocation('/pricing?pricingType=bundlePricing');
                    }
                })
                .finally(endLoader);
        }
    };

    const toggleEditable = () => {
        setIsEditable((prev) => !prev);
    };

    const selectedIds = form.getFieldValue('BundlePackageListing') || [];

    const mergedList = Array.from(
        new Map(
            [...store.pricingListByActiveStatus, ...selectedPackages].map(
                (item) => [item._id, item]
            )
        ).values()
    );

    const combinedList = [
        ...mergedList.filter((item) => selectedIds.includes(item._id)),
        ...mergedList.filter((item) => !selectedIds.includes(item._id)),
    ];

    return (
        <>
            <div className="flex items-center justify-between pb-12 lg:pe-5">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {view === 'true' && !isEditable
                            ? 'View Bundle Pricing'
                            : id === '0'
                            ? 'Add a Bundle Pricing Option'
                            : 'Edit Bundle Pricing Option'}
                    </Title>
                </div>
                {view === 'true' &&
                    !isEditable &&
                    store.role !== RoleType.FRONT_DESK_ADMIN && (
                        <div onClick={() => toggleEditable()}>
                            <img
                                src="/icons/common/edit.svg"
                                alt="edit"
                                className="ms-auto h-[20px] cursor-pointer"
                            />
                        </div>
                    )}
            </div>

            {loader ? (
                <FullLoader state={true} />
            ) : (
                <div className="lg:mt-16 @sm:mt-5">
                    <div className="rounded-3xl border  lg:p-16 @sm:p-5">
                        <Form
                            name="BundlePricingCreate"
                            layout="horizontal"
                            size="large"
                            autoComplete="off"
                            form={form}
                            onFinish={onFinish}
                            initialValues={{
                                remember: true,
                                BundleIsSellOnline: false,
                                BundlePackageListing: [],
                            }}
                            disabled={view === 'true' && !isEditable}
                            onValuesChange={onValuesChange}
                        >
                            <Form.Item
                                label="Pricing option Name"
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message:
                                            'Please enter pricing option name!',
                                    },
                                ]}
                            >
                                <Input
                                    maxLength={50}
                                    placeholder="Enter Pricing option Name"
                                />
                            </Form.Item>
                            <Form.Item
                                label="Price"
                                name="BundlePrice"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter price!',
                                    },
                                ]}
                            >
                                <Input
                                    type="text"
                                    placeholder="Enter Price"
                                    maxLength={10}
                                    onKeyPress={(event) => {
                                        if (!/^\d$/.test(event.key)) {
                                            event.preventDefault();
                                        }
                                    }}
                                    onInput={(e: any) => {
                                        e.target.value = e.target.value.replace(
                                            /[^0-9]/g,
                                            ''
                                        );
                                    }}
                                />
                            </Form.Item>

                            <div className="mb-5 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                                <div className="checkbox-custom">
                                    <Paragraph className="ant-form-item-label mb-0">
                                        <label>Discount:</label>
                                    </Paragraph>
                                </div>
                                <div className="flex items-center lg:w-[80%] lg:flex-row @sm:flex-col">
                                    <Form.Item
                                        label=""
                                        name="BundleDiscountType"
                                        className="w-[40%]"
                                        rules={[
                                            {
                                                required: false,
                                                message:
                                                    'Please select a discount type!',
                                            },
                                        ]}
                                    >
                                        <Select
                                            placeholder="Select Discount Type"
                                            options={[
                                                {
                                                    label: 'Flat',
                                                    value: 'Flat',
                                                },
                                                {
                                                    label: 'Percentage',
                                                    value: 'Percentage',
                                                },
                                            ]}
                                        />
                                    </Form.Item>

                                    <Form.Item
                                        label=""
                                        name="BundleDiscountValue"
                                        className="w-[40%]"
                                        dependencies={['discountType']}
                                    >
                                        <Input
                                            type="number"
                                            placeholder="Enter discount value"
                                            min={0}
                                        />
                                    </Form.Item>
                                </div>
                            </div>

                            <Form.Item
                                label="Inclusive GST"
                                name="isInclusiveofGst"
                                valuePropName="checked"
                            >
                                <Checkbox
                                // onChange={(e) => {
                                //     const isChecked =
                                //         e.target.checked;
                                //     setTrialPricingClass(
                                //         isChecked
                                //     );
                                // }}
                                >
                                    Inclusive GST
                                </Checkbox>
                            </Form.Item>

                            <div className="mb-5 flex justify-between">
                                <div className="@sm:hidden"></div>
                                <div className="lg:w-[80%]">
                                    <Form.Item
                                        label=""
                                        name="BundleIsSellOnline"
                                        valuePropName="checked"
                                    >
                                        <Checkbox>Sell Online</Checkbox>
                                    </Form.Item>
                                </div>
                            </div>
                            <div className="mb-5 flex items-center lg:justify-between @sm:gap-5">
                                <div className="checkbox-custom">
                                    <Paragraph className="ant-form-item-label mb-0">
                                        <label>
                                            Tax:
                                            <span className="ms-1 text-2xl text-red-400">
                                                *
                                            </span>
                                        </label>
                                    </Paragraph>
                                </div>
                                <div className=" lg:grid lg:w-[80%]">
                                    <Form.Item
                                        label=""
                                        name="BundleTax"
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please select tax !',
                                            },
                                        ]}
                                    >
                                        <Radio.Group className="lg:grid">
                                            <Radio value="18"> GST 18 % </Radio>
                                            <Radio value="12"> GST 12 %</Radio>
                                            <Radio value="5"> GST 5 %</Radio>
                                            {/* <Radio value="0"> No GST </Radio> */}
                                        </Radio.Group>
                                    </Form.Item>
                                </div>
                            </div>

                            <Form.Item
                                label="HSN / SAC"
                                name="hsnOrSacCode"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter hsn code !',
                                    },
                                ]}
                            >
                                <Input
                                    maxLength={15}
                                    placeholder="Enter HSN / SAC Code"
                                />
                            </Form.Item>

                            <div className="mb-5 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                                <div className="checkbox-custom">
                                    <Paragraph className="ant-form-item-label mb-0">
                                        <label>
                                            Expire:
                                            <span className="ms-1 text-2xl text-red-400">
                                                *
                                            </span>
                                        </label>
                                    </Paragraph>
                                </div>
                                <div className="flex items-center lg:w-[80%] lg:flex-row @sm:flex-col">
                                    <Form.Item
                                        label=""
                                        name="BundleExpireInput"
                                        className="w-[40%]"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please enter a expiry!',
                                            },
                                        ]}
                                    >
                                        <Input placeholder="Will expire in days" />
                                    </Form.Item>

                                    <Form.Item
                                        label=""
                                        name="BundleExpirationUnit"
                                        className="w-[40%]"
                                        rules={[
                                            {
                                                required: true,
                                                message:
                                                    'Please select duration type!',
                                            },
                                        ]}
                                    >
                                        <Select
                                            showSearch
                                            placeholder="Select Duration"
                                            options={[
                                                {
                                                    label: 'Day(s)',
                                                    value: 'days',
                                                },
                                                {
                                                    label: 'Month(s)',
                                                    value: 'months',
                                                },
                                                {
                                                    label: 'Year(s)',
                                                    value: 'years',
                                                },
                                            ]}
                                        />
                                    </Form.Item>
                                </div>
                            </div>

                            <div className="mb-5 flex justify-between  lg:flex-row lg:items-start @sm:flex-col">
                                <div className="checkbox-custom">
                                    <Paragraph className="ant-form-item-label mb-0">
                                        <label>Select Pricing Packages:</label>
                                    </Paragraph>
                                </div>
                                <div className="flex items-start  lg:w-[80%] lg:flex-col @sm:flex-col">
                                    {/* <Form.Item
                                    label=""
                                    name="BundlePricingPackages"
                                    rules={[
                                        {
                                            required: false,
                                            message:
                                                'Please select a package type!',
                                        },
                                    ]}
                                    className="w-[40%]"
                                >
                                    <Select
                                        placeholder="Select Packages"
                                        mode="multiple"
                                        options={[
                                            {
                                                label: 'Flat',
                                                value: 'Flat',
                                            },
                                            {
                                                label: 'Percentage',
                                                value: 'Percentage',
                                            },
                                        ]}
                                    />
                                </Form.Item> */}
                                    <div
                                        ref={containerRef}
                                        className="mb-5 h-72 w-[32%] overflow-scroll rounded-2xl border p-6"
                                    >
                                        <Input
                                            placeholder="Search packages..."
                                            className="mb-3"
                                            onChange={(e) =>
                                                setSearch(e.target.value)
                                            }
                                        />
                                        <Form.Item
                                            className=""
                                            name="BundlePackageListing"
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        'Please select at least one service.',
                                                    type: 'array',
                                                },
                                            ]}
                                        >
                                            <Checkbox.Group
                                                className="flex flex-col gap-2"
                                                onChange={(checkedIds) => {
                                                    const fullCheckedItems =
                                                        combinedList.filter(
                                                            (item) =>
                                                                checkedIds.includes(
                                                                    item._id
                                                                )
                                                        );
                                                    setSelectedPackages(
                                                        fullCheckedItems
                                                    );
                                                    form.setFieldsValue({
                                                        BundlePackageListing:
                                                            checkedIds,
                                                    });
                                                }}
                                            >
                                                {combinedList?.map(
                                                    (item: any) => (
                                                        <Checkbox
                                                            key={item._id}
                                                            value={item._id}
                                                        >
                                                            {capitalizeFirstLetter(
                                                                item?.name
                                                            )}
                                                        </Checkbox>
                                                    )
                                                )}
                                            </Checkbox.Group>
                                            {/* {loading && <Paragraph className="text-center">Loading more...</Paragraph>}
                {!hasMore && <Paragraph className="text-center text-gray-400">No more items</Paragraph>} */}
                                        </Form.Item>
                                    </div>
                                </div>
                            </div>

                            {!(view === 'true' && !isEditable) && (
                                <div className="ms-auto mt-8 text-right">
                                    <Button
                                        loading={loader}
                                        htmlType="submit"
                                        className="Paragraph-white   me-0  ms-auto bg-purpleLight text-white"
                                    >
                                        Submit
                                    </Button>
                                </div>
                            )}
                        </Form>
                    </div>
                </div>
            )}
            {pinModalVisible && requirePinModal && (
                <ModulePinConfirmationModal
                    visible={pinModalVisible}
                    onConfirm={() => setPinModalVisible(false)}
                    module={SUBJECT_TYPE.PRICING_PRICING}
                    subModule={PERMISSIONS_ENUM.PRICING_WRITE}
                    onCancel={handleClose}
                />
            )}
        </>
    );
};

export default BundlePricing;
