// components/ClientListModal.tsx

import React, { useEffect, useState } from 'react';
import {
    Modal,
    Table,
    Input,
    Button,
    Typography,
    Checkbox,
    Pagination,
    Space,
} from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { useDebounce } from '~/hooks/useDebounce';
import {
    CustomerList,
    multipleSharePassTransfer,
} from '~/redux/actions/customer-action';
import { formatDate } from '~/components/common/function';
import { PricingListingForSharePass } from '~/redux/actions/appointment-action';
import Alertify from '~/services/alertify';
import ClientCheckInModal from './client-checkin-modal';

const { Title, Text } = Typography;

const ClientListModal = ({
    visible,
    onCancel,
    data,
    orderData,
    checkInModalVisible,
    setCheckInModalVisible,
    sharedClientsForCheckIn,
    setSharedClientsForCheckIn,
    setSelectedClientData,
}: any) => {
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [searchText, setSearchText] = useState('');
    const dispatch = useAppDispatch();

    const [currentPage, setCurrentPage] = useState(1);
    const [pageSizes, setPageSize] = useState(10);
    const [loader, startLoader, endLoader] = useLoader();
    const [submitLoader, starSubmittLoader, endSubmitLoader] = useLoader();
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const [pricingOption, setPricingOption] = useState([]);
    const [availableSessions, setAvailableSessions] = useState(0);
    const [selectedPricingItem, setSelectedPricingItem] = useState<any>(null);
    const [selectedClients, setSelectedClients] = useState<any[]>([]);

    const store = useAppSelector((state) => ({
        customerList: state.customer_store.customerList,
        organizationId: state.auth_store.organizationId,
        customerListCount: state.customer_store.customerListCount,
    }));

    console.log('Customer list----------', data);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
    }

    useEffect(() => {
        startLoader();
        const requestParams = {
            page: currentPage,
            pageSize: pageSizes,
        } as any;

        if (searchText) {
            debouncedRequest(() => {
                dispatch(
                    CustomerList({
                        ...requestParams,
                        search: searchText,
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .finally(endLoader);
            });
        } else {
            debouncedRequest(() => {
                dispatch(
                    CustomerList({
                        ...requestParams,
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .finally(endLoader);
            });
        }
    }, [currentPage, pageSizes, searchText]);

    useEffect(() => {
        if (orderData) {
            dispatch(
                PricingListingForSharePass({
                    userId: orderData?.userId,
                    // bundledPricingId: data?.bundledPricingId,
                    invoiceId: orderData?._id,
                })
            )
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        const pricingList = res.data?.data?.map(
                            (item: any) => ({
                                value: item._id,
                                label: item.packageName,
                                id: item._id,
                                packageId: item.packageId,
                                bundledPricingId: item.bundledPricingId,
                                ...item,
                            })
                        );
                        let matchedItems: any[] = [];

                        if (data?.isBundledPricing) {
                            matchedItems = pricingList.filter(
                                (item: any) =>
                                    item.bundledPricingId ===
                                    data?.bundledPricingId
                            );
                        } else {
                            matchedItems = pricingList.filter(
                                (item: any) =>
                                    item.packageId === data?.packageId
                            );
                        }

                        const totalSessions = matchedItems.reduce(
                            (sum: any, item: any) =>
                                sum + (item.remainingSessions || 0),
                            0
                        );

                        setAvailableSessions(totalSessions);
                        setSelectedPricingItem(matchedItems);

                        setPricingOption(pricingList);
                    }
                });
        }
    }, [orderData]);

    const handleTransfer = () => {
        if (!data?.userId || !store.organizationId) return;

        const clientsToTransfer = [...selectedClients];

        if (selectedRowKeys.length > availableSessions) {
            Alertify.error('Selected clients exceed available sessions.');
            return;
        }

        const pricingQueue = [...selectedPricingItem];

        const shares = [];

        let clientIndex = 0;
        starSubmittLoader();

        while (clientIndex < clientsToTransfer.length && pricingQueue.length) {
            const client: any = clientsToTransfer[clientIndex];

            while (pricingQueue.length) {
                const currentPricing = pricingQueue[0];

                if (currentPricing.remainingSessions > 0) {
                    shares.push({
                        shareTo: client.userId,
                        purchaseId: currentPricing._id,
                        noOfSessions: 1,
                    });

                    currentPricing.remainingSessions -= 1;
                    clientIndex++;
                    break;
                } else {
                    pricingQueue.shift();
                }
            }
        }

        const payload = {
            shareFrom: data.userId,
            organizationId: store.organizationId,
            shares,
        };

        console.log('Payload:', payload);

        dispatch(multipleSharePassTransfer(payload))
            .unwrap()
            .then((res: any) => {
                console.log('Response:', res);
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    setSharedClientsForCheckIn(selectedClients);
                    setSelectedClientData(res?.data?.data);
                    setCheckInModalVisible(true);
                    onCancel();
                }
            })
            .finally(endSubmitLoader);
    };

    const columns = [
        {
            title: '',
            dataIndex: 'checkbox',
            render: (_: any, record: any) => (
                <Checkbox
                    checked={selectedRowKeys.includes(record.userId)}
                    onChange={(e) => {
                        const checked = e.target.checked;
                        if (checked) {
                            if (selectedRowKeys.length < availableSessions) {
                                setSelectedRowKeys((prev) => [
                                    ...prev,
                                    record.userId,
                                ]);
                                setSelectedClients((prev) => [...prev, record]);
                            } else {
                                Alertify.error(
                                    `You can only transfer to ${availableSessions} client(s)`
                                );
                            }
                        } else {
                            setSelectedClients((prev) =>
                                prev.filter(
                                    (client) => client.userId !== record.userId
                                )
                            );
                            setSelectedRowKeys((prev) =>
                                prev.filter((key) => key !== record.userId)
                            );
                        }
                    }}
                />
            ),
            width: 50,
        },
        {
            title: 'NAME',
            dataIndex: 'name',
        },
        {
            title: 'PHONE NO',
            dataIndex: 'mobile',
        },
        {
            title: 'EMAIL',
            dataIndex: 'email',
        },
        {
            title: 'CREATED AT',
            dataIndex: 'createdAt',
            render: (text: string) => formatDate(text),
        },
    ];

    return (
        <>
            <Modal
                open={visible}
                onCancel={onCancel}
                footer={null}
                className="w-[60%]"
            >
                <Title level={4}>Client List</Title>
                <div className="mb-12 flex gap-10 ">
                    <Text strong>Package Name: </Text>
                    <Text>{data?.product}</Text>
                    <br />
                    <Text strong>No. of Session(s) Remaining: </Text>
                    <Text>{availableSessions}</Text>
                </div>

                <Input
                    placeholder="Search"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className="mb-6 w-[40%]"
                    allowClear
                />

                <Table
                    columns={columns}
                    dataSource={store.customerList}
                    pagination={false}
                    loading={loader}
                />

                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={store.customerListCount}
                        pageSize={pageSizes}
                        onChange={paginate}
                        pageSizeOptions={['10', '20', '50']}
                        hideOnSinglePage
                    />
                </div>

                <div className="flex justify-end gap-5">
                    <Button
                        htmlType="button"
                        loading={submitLoader}
                        className="h-16  w-[110px]  bg-purpleLight text-white "
                        onClick={handleTransfer}
                    >
                        <p>Transfer </p>
                    </Button>
                </div>
            </Modal>
        </>
    );
};

export default ClientListModal;
