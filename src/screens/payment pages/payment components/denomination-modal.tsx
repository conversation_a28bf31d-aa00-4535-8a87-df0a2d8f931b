import React, { useState, useEffect } from 'react';
import { Mo<PERSON>, But<PERSON>, Table, ConfigProvider, Input } from 'antd';

const DenominationModal: React.FC<{
    visible: boolean;
    onClose: () => void;
    onDenominationChange?: (data: Record<number, number>) => void;
    overallTotal?: number;
    setOverallTotal?: any;
}> = ({
    visible,
    onClose,
    onDenominationChange,
    overallTotal,
    setOverallTotal,
}) => {
        const [data, setData] = useState([
            {
                key: '1',
                Denomination: 500,
                quantity: 0,
            },
            {
                key: '2',
                Denomination: 200,
                quantity: 0,
            },
            {
                key: '3',
                Denomination: 100,
                quantity: 0,
            },
            {
                key: '4',
                Denomination: 50,
                quantity: 0,
            },
            {
                key: '5',
                Denomination: 20,
                quantity: 0,
            },
            {
                key: '6',
                Denomination: 10,
                quantity: 0,
            },
            {
                key: '7',
                Denomination: 5,
                quantity: 0,
            },
            {
                key: '8',
                Denomination: 2,
                quantity: 0,
            },
            {
                key: '9',
                Denomination: 1,
                quantity: 0,
            },
        ]);
        useEffect(() => {
            if (visible) {
                setData((prevData) =>
                    prevData.map((item) => ({ ...item, quantity: 0 }))
                );
                setOverallTotal?.(0);
            }
        }, [visible]);

        useEffect(() => {
            const total = data.reduce(
                (sum, item) => sum + item.Denomination * item.quantity,
                0
            );
            setOverallTotal(total);
        }, [data]);

        const handleQuantityChange = (
            id: string,
            action: 'increment' | 'decrement' | 'input',
            value?: number
        ) => {
            setData((prevData) =>
                prevData.map((item) =>
                    item.key === id
                        ? {
                            ...item,
                            quantity:
                                action === 'increment'
                                    ? item.quantity + 1
                                    : action === 'decrement'
                                        ? Math.max(item.quantity - 1, 0)
                                        : Math.max(value ?? 0, 0),
                        }
                        : item
                )
            );
        };

        const handleOk = () => {
            const filteredData = data?.reduce((acc, item) => {
                if (item.quantity > 0) {
                    acc[item.Denomination] = item.quantity;
                }
                return acc;
            }, {} as Record<number, number>);

            if (onDenominationChange) {
                onDenominationChange(filteredData);
            }
            onClose();
        };

        const handleClose = () => {
            setData((prevData) =>
                prevData.map((item) => ({ ...item, quantity: 0 }))
            );

            setOverallTotal(0);

            if (onDenominationChange) {
                onDenominationChange({});
            }

            onClose();
        };

        const columns = [
            {
                title: 'Denomination',
                dataIndex: 'Denomination',
                key: 'Denomination',
                align: 'center',
                render: (value: number) => `₹ ${value}`,
                width: 100,
            },
            {
                title: 'Count',
                dataIndex: 'quantity',
                key: 'Count',
                align: 'center',
                width: 50,
                render: (_: any, record: any) => (
                    <div className=" flex items-center justify-between ">
                        <Button
                            size="small"
                            shape="circle"
                            onClick={() =>
                                handleQuantityChange(record.key, 'decrement')
                            }
                            disabled={record.quantity <= 0}
                        >
                            -
                        </Button>
                        <Input
                            type="text"
                            onInput={(e: any) => {
                                e.target.value = e.target.value.replace(
                                    /[^0-9]/g,
                                    ''
                                );
                            }}
                            min={0}
                            value={record.quantity}
                            className="w-28 text-center"
                            onChange={(e) =>
                                handleQuantityChange(
                                    record.key,
                                    'input',
                                    parseInt(e.target.value) || 0
                                )
                            }
                            onKeyDown={(e) => {
                                if (e.key === '-' || e.key === 'e') {
                                    e.preventDefault();
                                }
                            }}
                        />
                        <Button
                            shape="circle"
                            size="small"
                            onClick={() =>
                                handleQuantityChange(record.key, 'increment')
                            }
                        >
                            +
                        </Button>
                    </div>
                ),
            },
            {
                title: 'Total',
                dataIndex: 'Total',
                key: 'Total',
                align: 'center',
                width: 100,
                render: (_: any, record: any) =>
                    `₹ ${record.Denomination * record.quantity}`,
            },
        ];

        return (
            <Modal
                title="Count Cash Drawer"
                open={visible}
                onCancel={handleClose}
                footer={null}
                className="w-[30%]"
                centered
            >
                <div className="mt-8 border">
                    <ConfigProvider
                        theme={{
                            components: {
                                Table: {
                                    borderColor: '#0000001A',
                                    cellFontSize: 13,
                                    headerBg: '#fff',
                                    headerColor: '#1A3353',
                                    colorText: '#455560',
                                    cellPaddingBlock: 6,
                                },
                            },
                        }}
                    >
                        <Table
                            pagination={false}
                            dataSource={data}
                            columns={columns}
                        />
                    </ConfigProvider>
                </div>

                <div className="flex flex-row justify-start gap-4 pt-5">
                    <p className="text-2xl font-medium text-[#1a3353]">Total : </p>
                    <p className="text-2xl font-normal text-[#1a3353]">
                        ₹ {overallTotal}
                    </p>
                </div>

                <div className="mt-14 flex items-center justify-between gap-4">
                    <div>
                        <p className="text-2xl font-medium text-[#1a3353]">
                            Open Drawer
                        </p>
                    </div>
                    <div className="flex flex-row gap-4">
                        <Button
                            onClick={handleClose}
                            className=" h-12 w-32 border-1 border-[#1A3353] text-[#1a3353] "
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={handleOk}
                            className="h-12 w-32 bg-purpleLight text-white"
                        >
                            OK
                        </Button>
                    </div>
                </div>
            </Modal>
        );
    };

export default DenominationModal;
