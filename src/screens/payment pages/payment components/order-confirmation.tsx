import { Button, Modal, Table } from 'antd';
import Title from 'antd/es/typography/Title';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useParams } from 'wouter';
import { formatDate } from '~/components/common/function';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    DownloadInvoice,
    OrderInvoiceDetails,
} from '~/redux/actions/purchased-action';
import SharePassModal from '~/screens/customers/share-pass-modal';
import { ClassType, RoleType, SUBJECT_TYPE } from '~/types/enums';
import ClientListModal from './group-checkin-modal';
import ClientCheckInModal from './client-checkin-modal';
import {
    multipleCheckInOrder,
    singleCheckInOrder,
} from '~/redux/actions/customer-action';
import BundleShareSessionModal from './bundle-share-modal';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';

const OrderConfirmation = () => {
    const [_, setLocation] = useLocation();
    const [sharePassData, setSharePassData] = useState<any>(false);
    const [multisharePassData, setMultiSharePassData] = useState<any>(false);
    const [packageList, setPackageList] = useState<any>();
    const [orderData, setOrderData] = useState<any>();
    const [loader, startLoader, endLoader] = useLoader(true);
    const [submitLoader, starSubmittLoader, endSubmitLoader] = useLoader();
    const { orderId } = useParams();
    const dispatch = useAppDispatch();
    const [multipleSharePassOpen, setMultipleSharePassOpen] =
        useState<boolean>(false);
    const [clientListModalVisible, setClientListModalVisible] = useState(false);
    const [checkInModalVisible, setCheckInModalVisible] = useState(false);
    const [sharedClientsForCheckIn, setSharedClientsForCheckIn] = useState<any>(
        []
    );
    const [selectedClientData, setSelectedClientData] = useState<any>(null);
    const [bundleModalVisible, setBundleModalVisible] = useState(false);
    const [bundleData, setBundleData] = useState<any>(null);
    const [confirmationVisible, setConfirmationVisible] = useState(false);
    const [selectedCheckInData, setSelectedCheckInData] = useState<any>(null);
    const [checkInSuccessModalVisible, setCheckInSuccessModalVisible] =
        useState(false);
    const [checkInSuccessData, setCheckInSuccessData] = useState<any[]>([]);

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasPricingPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) => subject.type === SUBJECT_TYPE.ORDER
            )
        );
    }, [all_permissions_for_role]);

    useEffect(() => {
        startLoader();
        if (orderId) {
            dispatch(OrderInvoiceDetails({ orderId: orderId }))
                .unwrap()
                .then((res: any) => {
                    const data = res.data.data;
                    const userId = data?.userId;
                    const purchaseItems = data?.purchaseItems?.length
                        ? data?.purchaseItems.map((item: any, i: number) => ({
                              key: i + 1,
                              srNo: i + 1,
                              product: item.name,
                              quantity: item.quantity,
                              price: item.price,
                              sessionType: item.sessionType,
                              classType: item.classType,
                              bundledPricingId: item.isBundledPricing
                                  ? item.packageId
                                  : null,
                              packageId: item.isBundledPricing
                                  ? null
                                  : item.packageId,
                              userId,
                              ...item,
                          }))
                        : [];
                    const productItems = data?.productItem?.length
                        ? data?.productItem.map((item: any, i: number) => ({
                              key: purchaseItems.length + i + 1,
                              srNo: purchaseItems.length + i + 1,
                              product: item.name,
                              quantity: item.quantity,
                              price: item.price,
                              isProduct: true,
                              userId,
                              ...item,
                          }))
                        : [];
                    const customPackageItems = data?.customPackageItems?.length
                        ? data?.customPackageItems.map(
                              (item: any, i: number) => ({
                                  key:
                                      purchaseItems.length +
                                      productItems.length +
                                      i +
                                      1,
                                  srNo:
                                      purchaseItems.length +
                                      productItems.length +
                                      i +
                                      1,
                                  product: item.name,
                                  quantity: item.quantity,
                                  price: item.price,
                                  userId,
                                  ...item,
                              })
                          )
                        : [];
                    setPackageList([
                        ...purchaseItems,
                        ...productItems,
                        ...customPackageItems,
                    ]);
                    setOrderData(data);
                })
                .finally(endLoader);
        }
    }, [orderId]);

    const handleDownloadInvoice = async () => {
        if (!orderId) return;

        try {
            const response = await dispatch(
                DownloadInvoice({ orderId })
            ).unwrap();

            if (!response) {
                throw new Error('No URL received from API.');
            }

            window.open(response, '_blank', 'noopener,noreferrer');
        } catch (error) {
            console.error('Error downloading invoice:', error);
        }
    };

    const handleConfirmCheckIn = async () => {
        if (!selectedCheckInData) return;

        starSubmittLoader();

        const payload = {
            packageId: selectedCheckInData?.packageId,
            userId: selectedCheckInData?.userId,
            invoiceId: selectedCheckInData?.invoiceId,
            organizationId: orderData?.organizationId,
            facilityId: orderData?.facilityId?._id,
        };

        try {
            await dispatch(singleCheckInOrder(payload))
                .unwrap()
                .then((res: any) => {
                    console.log('Res--------', res);
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        setConfirmationVisible(false);
                        setSelectedCheckInData(null);
                        const checkInDetails = res?.data?.data || [];
                        setCheckInSuccessData(checkInDetails);
                        setCheckInSuccessModalVisible(true);
                    }
                });
        } catch (error) {
            console.log('Check-In failed');
        } finally {
            endSubmitLoader();
        }
    };

    const columns = [
        {
            title: 'Sr. No.',
            dataIndex: 'srNo',
            key: 'srNo',
        },
        {
            title: 'Item',
            dataIndex: 'product',
            key: 'product',
        },
        {
            title: ' Quantity',
            dataIndex: 'quantity',
            key: 'quantity',
        },
        {
            title: 'Unit Price',
            dataIndex: 'price',
            key: 'price',
            render: (value: any) => value?.toFixed(2),
        },
        {
            title: 'Actions',
            key: 'actions',
            render: (record: any) => {
                console.log('Recrod ----------', record);
                if (record?.classType === ClassType.BOOKING) {
                    return (
                        <div className="flex gap-4 ">
                            <Button
                                htmlType="button"
                                className="h-10   bg-purpleLight  text-white "
                                onClick={() => {
                                    setSelectedCheckInData({
                                        packageId: record?.packageId,
                                        userId: record?.userId,
                                        invoiceId: orderData?._id,
                                    });
                                    setConfirmationVisible(true);
                                }}
                            >
                                <p className="text-lg">Check-In</p>
                            </Button>
                            <Button
                                htmlType="button"
                                className="h-10   bg-purpleLight  text-white "
                                onClick={() => {
                                    if (record.isBundledPricing) {
                                        setBundleData(record);
                                        setBundleModalVisible(true);
                                    } else {
                                        setMultipleSharePassOpen(true);
                                        setMultiSharePassData(record);
                                    }
                                }}
                            >
                                <p className="text-lg">
                                    {' '}
                                    {record.isBundledPricing
                                        ? 'All Packages'
                                        : 'Group Check-In'}{' '}
                                </p>
                            </Button>
                        </div>
                    );
                }
            },
            // !record.isProduct &&
            // record.classType !== 'courses' &&
            // ['day_pass', 'multiple'].includes(record.sessionType) && (
            //     <Button
            //         htmlType="button"
            //         className="h-10   bg-purpleLight  text-white "
            //         onClick={() =>
            //             setSharePassData({
            //                 visible: true,
            //                 bundledPricingId: record?.bundledPricingId,
            //                 packageId: record?.packageId,
            //             })
            //         }
            //     >
            //         <p className="text-lg"> Share Pass </p>
            //     </Button>
            // ),
        },
    ];

    const handleCheckIn = async (clientsIds: any) => {
        const purchaseId = clientsIds;
        if (!purchaseId) return;

        starSubmittLoader();
        const payload = {
            purchaseIds: purchaseId,
            organizationId: orderData?.organizationId,
            facilityId: orderData?.facilityId?._id,
        };
        try {
            await dispatch(multipleCheckInOrder(payload))
                .unwrap()
                .then((res: any) => {
                    console.log('Res--------', res);
                    const status = res?.payload?.status ?? res?.status;
                    if (status === 200 || status === 201) {
                        setCheckInModalVisible(false);
                        const checkInDetails = res?.data?.data || [];
                        setCheckInSuccessData(checkInDetails);
                        setCheckInSuccessModalVisible(true);
                    }
                });
        } catch (error) {
            console.error('Check-In Failed:', error);
        } finally {
            endSubmitLoader();
        }
    };

    return (
        <div className="">
            <div className="flex flex-row justify-between">
                <div className="flex flex-row items-center gap-6 pb-12">
                    <Title className=" text-[#1a3353]" level={4}>
                        Order Detail
                    </Title>
                    <p className=" text-[#455560] lg:text-[16px]">
                        Order ID: {orderData?.orderId}
                    </p>
                </div>
                <div className="flex  flex-row items-center gap-4">
                    {['completed', 'refund'].includes(
                        orderData?.paymentStatus
                    ) && (
                        <Button
                            onClick={handleDownloadInvoice}
                            htmlType="button"
                            className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                        >
                            <p> Print Receipt </p>
                        </Button>
                    )}
                    {(hasPricingPermission ||
                        store.role === RoleType.ORGANIZATION) && (
                        <Button
                            htmlType="button"
                            className="h-16  w-[110px]  bg-purpleLight text-white "
                            onClick={() => setLocation('/order-listing')}
                        >
                            <p>Order History </p>
                        </Button>
                    )}
                </div>
            </div>
            <div className="flex flex-row items-start gap-4">
                <div className="w-[70%] rounded-md border border-gray-200 px-5 py-5">
                    <p className=" font-semibold text-[#1a3353] lg:text-[16px]">
                        Order contents
                    </p>

                    <div>
                        <Table
                            dataSource={packageList}
                            columns={columns}
                            pagination={false}
                        />
                    </div>
                </div>
                <div className="w-[30%] rounded-md border border-gray-200 p-5">
                    <p className=" font-semibold  text-[#1a3353] lg:text-[16px]">
                        Order summary
                    </p>
                    <div className="flex flex-col gap-2">
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Client Name :</p>
                            <p className="text-[#455560]">
                                {orderData?.clientDetails.name}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Purchase Date :</p>
                            <p className="text-[#455560]">
                                {formatDate(orderData?.invoiceDate)}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Payment Status :</p>
                            <p className="text-[#455560]">
                                {orderData?.paymentStatus}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Item Total :</p>
                            <p className="text-[#455560]">
                                ₹{orderData?.subTotal}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Discount :</p>
                            <p className="text-[#455560]">
                                {orderData?.discount}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">
                                Cart Discount(
                                {orderData?.cartDiscountType === 'Flat'
                                    ? `₹ ${orderData?.cartDiscount}`
                                    : `${orderData?.cartDiscount} %`}
                                ) :
                            </p>
                            <p className="text-[#455560]">
                                {orderData?.cartDiscountAmount}
                            </p>
                        </div>
                        {orderData?.clientBillingDetails?.utCode ===
                        orderData?.billingDetails?.utCode ? (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">CGST :</p>
                                    <p className="text-[#455560]">
                                        {orderData?.cgst}
                                    </p>
                                </div>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">SGST :</p>
                                    <p className="text-[#455560]">
                                        {orderData?.sgst}
                                    </p>
                                </div>
                            </>
                        ) : (
                            <>
                                <div className="flex flex-row items-center gap-2 pt-3">
                                    <p className="font-medium ">IGST :</p>
                                    <p className="text-[#455560]">
                                        {orderData?.igst}
                                    </p>
                                </div>
                            </>
                        )}
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Total Amount </p>
                            <p className="text-[#455560]">
                                ₹{orderData?.totalAmountAfterGst}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Round Off </p>
                            <p className="text-[#455560]">
                                ₹ -{orderData?.roundOff}
                            </p>
                        </div>
                        <div className="flex flex-row items-center gap-2 pt-3">
                            <p className="font-medium ">Grand Total :</p>
                            <p className="text-[#455560]">
                                ₹{Math.floor(orderData?.grandTotal)}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            {/* <div className=" mx-auto  flex w-[33%] flex-row items-center gap-4 pt-20">
                <Button
                    htmlType="button"
                    className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                >
                    <p> Print Receipt </p>
                </Button>
                <Button
                    htmlType="button"
                    className="h-16  w-[110px] border-[#1A3353] bg-[#fff]  text-[#1A3353] "
                    onClick={() => setLocation('/order-listing')}
                >
                    <p>Order History </p>
                </Button>
            </div> */}
            {sharePassData?.visible && (
                <SharePassModal
                    visible={sharePassData?.visible}
                    onClose={() => setSharePassData(null)}
                    clientId={orderData?.userId}
                    clientName={orderData?.clientDetails.name}
                    invoiceId={orderData?._id}
                    bundledPricingId={sharePassData?.bundledPricingId}
                    packageId={sharePassData?.packageId}
                />
            )}
            {multipleSharePassOpen && (
                <ClientListModal
                    visible={multipleSharePassOpen}
                    data={multisharePassData}
                    onCancel={() => setMultipleSharePassOpen(false)}
                    orderData={orderData}
                    checkInModalVisible={checkInModalVisible}
                    setCheckInModalVisible={setCheckInModalVisible}
                    sharedClientsForCheckIn={sharedClientsForCheckIn}
                    setSharedClientsForCheckIn={setSharedClientsForCheckIn}
                    setSelectedClientData={setSelectedClientData}
                />
            )}

            {checkInModalVisible && (
                <ClientCheckInModal
                    visible={checkInModalVisible}
                    data={multisharePassData}
                    loading={submitLoader}
                    onClose={() => setCheckInModalVisible(false)}
                    sharedClients={sharedClientsForCheckIn}
                    selectedClientData={selectedClientData}
                    setSelectedClientData={setSelectedClientData}
                    onCheckIn={(checkedInClients: any) => {
                        console.log('Check-in Clients:', checkedInClients);
                        handleCheckIn(checkedInClients);
                    }}
                />
            )}

            {bundleModalVisible && (
                <BundleShareSessionModal
                    visible={bundleModalVisible}
                    onClose={() => setBundleModalVisible(false)}
                    orderData={orderData}
                    bundleData={bundleData}
                    openClientListModal={(selectedPackage: any) => {
                        setMultiSharePassData(selectedPackage);
                        setMultipleSharePassOpen(true);
                    }}
                />
            )}

            {confirmationVisible && (
                <CommonConfirmationModal
                    visible={confirmationVisible}
                    onCancel={() => {
                        setConfirmationVisible(false);
                        setSelectedCheckInData(null);
                    }}
                    onConfirm={handleConfirmCheckIn}
                    message="Are you sure you want to check-in this client?"
                />
            )}
            {checkInSuccessModalVisible && (
                <Modal
                    title="Check-In Summary"
                    open={checkInSuccessModalVisible}
                    onCancel={() => setCheckInSuccessModalVisible(false)}
                    footer={[
                        <Button
                            key="close"
                            className="h-16  w-[110px]  bg-purpleLight text-white "
                            onClick={() => setCheckInSuccessModalVisible(false)}
                        >
                            Close
                        </Button>,
                    ]}
                    width="60%"
                >
                    <Table
                        dataSource={checkInSuccessData}
                        // rowKey={(record) =>
                        //     record?.clientName + record?.packageName
                        // }
                        columns={[
                            {
                                title: 'Client Name',
                                dataIndex: 'clientName',
                                key: 'clientName',
                            },
                            {
                                title: 'Package Name',
                                dataIndex: 'packageName',
                                key: 'packageName',
                            },
                            {
                                title: 'Service Category',
                                dataIndex: 'serviceCategoryName',
                                key: 'serviceCategoryName',
                            },
                            {
                                title: 'Room Name',
                                dataIndex: 'roomName',
                                key: 'roomName',
                            },
                            {
                                title: 'Start Time',
                                dataIndex: 'from',
                                key: 'from',
                                render: (text) => text,
                            },
                            {
                                title: 'End Time',
                                dataIndex: 'to',
                                key: 'to',
                                render: (text) => text,
                            },
                        ]}
                        pagination={false}
                    />
                </Modal>
            )}
        </div>
    );
};

export default OrderConfirmation;
