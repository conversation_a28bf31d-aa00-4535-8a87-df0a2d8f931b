import React, { useEffect, useState } from 'react';
import { Button, Input } from 'antd';
import { useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import DenominationModal from './denomination-modal';
import { PurchasePricingPackages } from '~/redux/actions/pricing-actions';
import Alertify from '~/services/alertify';
import {
    ClearPosPurchasedData,
    SetPosPurchasedData,
} from '~/redux/slices/purchaged-slice';
import { getQueryParams } from '~/utils/getQueryParams';
import { markOrderAsPaid } from '~/redux/actions/purchased-action';
import { goBack } from '~/components/common/function';
import Paragraph from 'antd/es/typography/Paragraph';
import { useSelector } from 'react-redux';

// Table columns

interface CashPaymentProps {
    isMarkedAsPaid?: boolean;
    paymentMethodList?: any[];
}

const SplitPayment: React.FC<CashPaymentProps> = ({
    isMarkedAsPaid,
    paymentMethodList,
}) => {
    const [_, setLocation] = useLocation();
    const [overallTotal, setOverallTotal] = useState(0);
    const [inputValues, setInputValues] = useState<any>({});
    const [paymentStatus, setPaymentStatus] = useState<string | null>(null);

    // console.log('denominationData-------', denominationData);

    const [loader, startLoader, endLoader] = useLoader();
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const orderId = params.orderId;
    const store = useAppSelector((state) => ({
        posPurchasedData: state.purchased_store.posPuchasedData,
        userId: state.auth_store.userId,
    }));

    const handleConfirmStatusChange = () => {
        startLoader();
        if (overallTotal < Math.floor(store.posPurchasedData?.total)) {
            Alertify.error('Input amount must match the total bill.');
            endLoader();
            return;
        }
        const payload: Record<string, any> = {
            invoiceId: orderId,
            status: 'completed',
            // billingAddressId: store.posPurchasedData?.billingAddressId,
            amountPaid: overallTotal,
            isSplittedPayment: true,
            paymentDetails: Object.entries(inputValues).map(([key, value]) => {
                const paymentMethod = paymentMethodList?.find(
                    (item) => item.key === key
                );
                return {
                    paymentMethodId: paymentMethod?.paymentMethodId, // Adding paymentMethodId
                    paymentMethod: key,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: value,
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                };
            }),
        };

        dispatch(markOrderAsPaid(payload))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(`/order-confirmation/${orderId}`, {
                        replace: true,
                    });
                    Alertify.success('Payment completed successfully');
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(endLoader);
    };

    const handleInputChange = (key: string, value: any, item: any) => {
        if (/^\d*\.?\d*$/.test(value)) {
            const val = Number(value);
            // Allow only numbers and decimals
            setInputValues((prev: any) => ({
                ...prev,
                [key]: val,
            }));
        }
    };

    useEffect(() => {
        const sum = Object.values(inputValues).reduce(
            (acc, curr) => acc + curr,
            0
        );

        setOverallTotal(sum);
    }, [inputValues]);

    const handleContinueToPay = () => {
        startLoader();
        if (overallTotal < Math.floor(store.posPurchasedData?.total)) {
            Alertify.error('Input amount must match the total bill.');
            endLoader();
            return;
        }
        const payload = {
            cart: {
                facilityId: store.posPurchasedData?.facilityId,
                userId: store.posPurchasedData?.userId,
                items: [
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isPackage)
                        ?.map((item: any) => ({
                            itemType: 'service',
                            itemId: item._id,
                            quantity: item.quantity || 1,
                            promotionId:
                                item.promotion?._id === 'custom'
                                    ? null
                                    : item.promotion?._id,
                            discount:
                                item.promotion?._id &&
                                item.promotion?._id === 'custom'
                                    ? {
                                          type: item.promotion?.type,
                                          value: item.promotion?.value,
                                          discountedBy: store.userId,
                                      }
                                    : null,
                        })) || []),
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isProduct)
                        ?.map((item: any) => ({
                            itemType: 'product',
                            itemId: item.productId,
                            variantId: item.productVariantId,
                            quantity: item.quantity || 1,
                            promotionId: null,
                            discount: null,
                        })) || []),
                    ...(store.posPurchasedData?.customPackageItems?.map(
                        (item: any) => ({
                            itemType: 'custom_package',
                            itemId: item.customPackageId,
                            quantity: item.quantity || 1,
                            promotionId: null,
                            discount: {
                                type: item.discount?.type,
                                value: item.discount?.value,
                                discountedBy: store.userId,
                            },
                        })
                    ) || []),
                ],
                promotionCode: store.posPurchasedData?.promoCode,
                discount: {
                    type: store.posPurchasedData?.cartDiscountType,
                    value: store.posPurchasedData?.cartDiscount,
                    discountedBy: store.userId,
                },
            },
            paymentDetails: Object.entries(inputValues).map(([key, value]) => {
                const paymentMethod = paymentMethodList?.find(
                    (item) => item.key === key
                );
                return {
                    paymentMethodId: paymentMethod?.paymentMethodId,
                    paymentMethod: key,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: value,
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                };
            }),
            isSplittedPayment: true,
            amountPaid: store.posPurchasedData?.total,
            platform: 'Web',
            billingAddressId: store.posPurchasedData?.billingAddressId,
            paymentBy: store.userId,
        };
        dispatch(PurchasePricingPackages(payload))
            .unwrap()
            .then((res: any) => {
                // console.log('Res----------------', res);
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(
                        `/order-confirmation/${res.data?.data?.invoiceId}`,
                        { replace: true }
                    );
                    Alertify.success('Payment completed successfully');
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(endLoader);
    };

    const { role } = useSelector((state: any) => state.auth_store);

    const handleConfirm = (status = paymentStatus) => {
        if (isMarkedAsPaid) handleConfirmStatusChange();
        else if (status) handleContinueToPay();
    };

    const handleClick = (paymentStatus: string) => {
        setPaymentStatus(paymentStatus);
        handleConfirm(paymentStatus);
    };

    function handleTranscationId(value: any) {
        if (value)
            store.posPurchasedData = {
                ...store.posPurchasedData,
                paymentDetails: store.posPurchasedData.paymentDetails.map(
                    (payment: any) => ({
                        ...payment,
                        transactionId: value,
                    })
                ),
            };
        dispatch(SetPosPurchasedData(store.posPurchasedData));
    }
    return (
        <div className="bg-white py-5">
            <div className="mb-6">
                <h3 className="pt-10 text-[#1A3353] lg:text-4xl">
                    How do you want to pay?
                </h3>
            </div>
            <div className="flex flex-col gap-3 pb-20">
                {paymentMethodList
                    ?.filter((item: any) => item.key !== 'splitPayment')
                    .map((item: any) => (
                        <div key={item.key} className="flex items-center gap-2">
                            <p className="text-[#383838] lg:w-[25%]">
                                {item.title}
                            </p>

                            <div className="flex items-center gap-2 lg:w-[40%]">
                                <Input
                                    placeholder={`Enter Amount to be paid by ${item.title}`}
                                    value={inputValues[item.key] || ''}
                                    onChange={(e) =>
                                        handleInputChange(
                                            item.key,
                                            e.target.value,
                                            item
                                        )
                                    }
                                />
                            </div>
                        </div>
                    ))}
            </div>
            <div className="mb-9 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                <div className="checkbox-custom">
                    <Paragraph className="ant-form-item-label mb-0">
                        <label>Notes/Txn ID</label>
                    </Paragraph>
                </div>
                <div className="flex items-center lg:w-[80%] lg:flex-row @sm:flex-col">
                    <Input
                        placeholder={`Enter Transcation Id`}
                        onChange={(e) => handleTranscationId(e.target.value)}
                    />
                </div>
            </div>
            {/* Payment Details */}
            <div className="mb-6">
                <h3 className="mb-4 text-24 font-normal text-[#1A3353]">
                    Payment Details
                </h3>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Item Total Amount</span>
                    <span className="font-semibold">
                        ₹{store.posPurchasedData?.subTotal?.toFixed(2)}/-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Cart Discount</span>
                    <span className="font-semibold">
                        ₹
                        {store.posPurchasedData?.cartDiscountAmount?.toFixed(2)}
                        /-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">GST</span>
                    <span className="font-semibold">
                        ₹{store.posPurchasedData?.gst?.toFixed(2)}
                        /-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Total Amount</span>
                    <span className="font-semibold">
                        ₹{store.posPurchasedData?.total?.toFixed(2)}/-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Round Off</span>
                    <span className="font-semibold">
                        ₹
                        {(
                            store.posPurchasedData?.total -
                            Math.floor(store.posPurchasedData?.total)
                        ).toFixed(2)}
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Amount Paid</span>
                    <span className="font-semibold">₹{overallTotal}/-</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold text-[#383838]">
                        Amount Pending
                    </span>
                    <span
                        className={`font-bold ${
                            overallTotal < (store.posPurchasedData?.total ?? 0)
                                ? 'text-red-600'
                                : 'text-green-600'
                        }`}
                    >
                        ₹{' '}
                        {overallTotal >=
                        Math.floor(store.posPurchasedData?.total ?? 0)
                            ? '0.00'
                            : (
                                  Math.floor(
                                      store.posPurchasedData?.total ?? 0
                                  ) - (overallTotal ?? 0)
                              ).toFixed(2)}
                        /-
                    </span>
                </div>
                {overallTotal >
                    Math.floor(store.posPurchasedData?.total ?? 0) && (
                    <div className="flex justify-between border-t pt-2">
                        <span className="font-semibold">Change Due</span>
                        <span className="font-bold text-red-600">
                            ₹ -
                            {(
                                (overallTotal ?? 0) -
                                Math.floor(store.posPurchasedData?.total ?? 0)
                            ).toFixed(2)}
                            /-
                        </span>
                    </div>
                )}
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end gap-10">
                <Button
                    onClick={goBack}
                    className="rounded-lg border border-[#1A3353]        px-10 py-8"
                >
                    Back
                </Button>

                <Button
                    loading={loader}
                    onClick={() => handleClick('completed')}
                    className="rounded-lg bg-purpleLight px-10 py-8 text-white"
                >
                    Confirm Payment
                </Button>
            </div>
        </div>
    );
};

export default SplitPayment;
