import React, { useState } from 'react';
import { Table, Button, ConfigProvider, Input } from 'antd';
import { useLocation } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import DenominationModal from './denomination-modal';
import { PurchasePricingPackages } from '~/redux/actions/pricing-actions';
import Alertify from '~/services/alertify';
import {
    ClearPosPurchasedData,
    SetPosPurchasedData,
} from '~/redux/slices/purchaged-slice';
import { getQueryParams } from '~/utils/getQueryParams';
import { markOrderAsPaid } from '~/redux/actions/purchased-action';
import { goBack } from '~/components/common/function';
import Paragraph from 'antd/es/typography/Paragraph';
import { useSelector } from 'react-redux';

// Table columns
const columns = [
    {
        title: 'Item',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'Qty',
        dataIndex: 'quantity',
        align: 'center',
        key: 'quantity',
        width: 90,
    },
    {
        title: 'Price',
        key: 'subTotal',
        dataIndex: 'price',
        align: 'center',
        render: (value: any) => value?.toFixed(2),
    },
    {
        title: 'Discount',
        key: 'discountedValue',
        align: 'center',
        dataIndex: 'discountedValue',
    },
    {
        title: 'GST',
        dataIndex: 'tax',
        key: 'tax',
        align: 'center',
    },
];

interface CashPaymentProps {
    isMarkedAsPaid?: boolean;
}

const CashPayment: React.FC<CashPaymentProps> = ({ isMarkedAsPaid }) => {
    const [_, setLocation] = useLocation();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [denominationData, setDenominationData] = useState<
        Record<number, number>
    >({});
    const [overallTotal, setOverallTotal] = useState(0);
    const [paymentStatus, setPaymentStatus] = useState<string | null>(null);

    // console.log('denominationData-------', denominationData);

    const [loader, startLoader, endLoader] = useLoader();
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const orderId = params.orderId;
    const store = useAppSelector((state) => ({
        posPurchasedData: state.purchased_store.posPuchasedData,
        userId: state.auth_store.userId,
    }));

    // console.log('Data-----------store--------', store.posPurchasedData);

    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleDenominationChange = (data: Record<number, number>) => {
        setDenominationData(data);
    };

    const handleConfirmStatusChange = () => {
        startLoader();
        const totalDenomination = Object.entries(denominationData)?.reduce(
            (sum, [key, value]: any) => sum + key * value,
            0
        );

        if (Object.keys(denominationData)?.length === 0) {
            Alertify.error('Please provide cash denominations.');
            endLoader();
            return;
        }

        if (
            totalDenomination < (Math.floor(store.posPurchasedData?.total) ?? 0)
        ) {
            Alertify.error(
                'Cash denominations must match or exceed the total bill.'
            );
            endLoader();
            return;
        }

        const payload: Record<string, any> = {
            invoiceId: orderId,
            status: 'completed',
            amountPaid: store.posPurchasedData?.total,
            // billingAddressId: store.posPurchasedData?.billingAddressId,
            paymentDetails: [
                {
                    paymentMethodId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethodId,
                    paymentMethod:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethod,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: Math.floor(store.posPurchasedData?.total),
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                    denominations: denominationData,
                },
            ],
        };

        dispatch(markOrderAsPaid(payload))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(`/order-confirmation/${orderId}`, {
                        replace: true,
                    });
                    Alertify.success('Payment completed successfully');
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(endLoader);
    };

    const handleContinueToPay = () => {
        startLoader();
        const totalDenomination = Object.entries(denominationData)?.reduce(
            (sum, [key, value]: any) => sum + key * value,
            0
        );

        if (Object.keys(denominationData)?.length === 0) {
            Alertify.error('Please provide cash denominations.');
            endLoader();
            return;
        }

        if (
            totalDenomination < (Math.floor(store.posPurchasedData?.total) ?? 0)
        ) {
            Alertify.error(
                'Cash denominations must match or exceed the total bill.'
            );
            endLoader();
            return;
        }
        const payload = {
            cart: {
                facilityId: store.posPurchasedData?.facilityId,
                userId: store.posPurchasedData?.userId,
                items: [
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isPackage)
                        ?.map((item: any) => ({
                            itemType: 'service',
                            itemId: item._id,
                            quantity: item.quantity || 1,
                            promotionId:
                                item.promotion?._id === 'custom'
                                    ? null
                                    : item.promotion?._id,
                            discount:
                                item.promotion?._id &&
                                item.promotion?._id === 'custom'
                                    ? {
                                          type: item.promotion?.type,
                                          value: item.promotion?.value,
                                          discountedBy: store.userId,
                                      }
                                    : null,
                        })) || []),
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isProduct)
                        ?.map((item: any) => ({
                            itemType: 'product',
                            itemId: item.productId,
                            variantId: item.productVariantId,
                            quantity: item.quantity || 1,
                            promotionId: null,
                            discount: null,
                        })) || []),
                    ...(store.posPurchasedData?.customPackageItems?.map(
                        (item: any) => ({
                            itemType: 'custom_package',
                            itemId: item.customPackageId,
                            quantity: item.quantity || 1,
                            promotionId: null,
                            discount: {
                                type: item.discount?.type,
                                value: item.discount?.value,
                                discountedBy: store.userId,
                            },
                        })
                    ) || []),
                ],
                promotionCode: store.posPurchasedData?.promoCode,
                discount: {
                    type: store.posPurchasedData?.cartDiscountType,
                    value: store.posPurchasedData?.cartDiscount,
                    discountedBy: store.userId,
                },
            },
            paymentDetails: [
                {
                    paymentMethodId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethodId,
                    paymentMethod:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethod,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: Math.floor(store.posPurchasedData?.total),
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                    denominations: denominationData,
                },
            ],
            isSplittedPayment: false,
            amountPaid: store.posPurchasedData?.total,
            platform: 'Web',
            billingAddressId: store.posPurchasedData?.billingAddressId,
            paymentBy: store.userId,
        };

        dispatch(PurchasePricingPackages(payload))
            .unwrap()
            .then((res: any) => {
                // console.log('Res----------------', res);
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(
                        `/order-confirmation/${res.data?.data?.invoiceId}`,
                        { replace: true }
                    );
                    Alertify.success('Payment completed successfully');
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(endLoader);
    };

    const { role } = useSelector((state: any) => state.auth_store);

    const handleConfirm = (status = paymentStatus) => {
        if (isMarkedAsPaid) handleConfirmStatusChange();
        else if (status) handleContinueToPay();
    };

    const handleClick = (paymentStatus: any) => {
        setPaymentStatus(paymentStatus);
        handleConfirm(paymentStatus);
    };

    const handleClose = () => {
        setPaymentStatus(null);
        setIsModalVisible(false);
    };

    function handleTranscationId(value: any) {
        if (value)
            store.posPurchasedData = {
                ...store.posPurchasedData,
                paymentDetails: store.posPurchasedData.paymentDetails.map(
                    (payment: any) => ({
                        ...payment,
                        transactionId: value,
                    })
                ),
            };
        dispatch(SetPosPurchasedData(store.posPurchasedData));
    }
    return (
        <div className=" bg-white py-10">
            {/* Ant Design Table */}
            <div className="lg:pb-16">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                cellPaddingBlock: 6,
                            },
                        },
                        token: {
                            boxShadowSecondary:
                                '0 25px 50px -12px rgb(0 0 0 / 0.25)',
                        },
                    }}
                >
                    <Table
                        dataSource={[
                            ...(store.posPurchasedData?.purchaseItems || []),
                            ...(store.posPurchasedData?.customPackageItems ||
                                []),
                        ]}
                        columns={columns}
                        pagination={false}
                        bordered
                        // summary={() => (
                        //     <Table.Summary.Row>
                        //         <Table.Summary.Cell index={0} colSpan={2}>
                        //             <span className="font-semibold">Total</span>
                        //         </Table.Summary.Cell>
                        //         <Table.Summary.Cell
                        //             index={2}
                        //             className="text-center"
                        //         >
                        //             <span className="font-bold">6000</span>
                        //         </Table.Summary.Cell>
                        //     </Table.Summary.Row>
                        // )}
                    />
                </ConfigProvider>
            </div>

            <div className="pb-16">
                <Button
                    onClick={showModal}
                    className="h-16 border border-[#1a3353] text-[#1a3353]"
                >
                    Select Denomination
                </Button>
            </div>
            {/* <span> Notes/TXN ID</span>
            <Input
                placeholder={`Enter Transcation Id`}
                onChange={(e) =>
                    handleTranscationId(e.target.value)

                } /> */}
            <div className="mb-9 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                <div className="checkbox-custom">
                    <Paragraph className="ant-form-item-label mb-0">
                        <label>Notes/Txn ID</label>
                    </Paragraph>
                </div>
                <div className="flex items-center lg:w-[80%] lg:flex-row @sm:flex-col">
                    <Input
                        placeholder={`Enter Transcation Id`}
                        onChange={(e) => handleTranscationId(e.target.value)}
                    />
                </div>
            </div>
            {/* Payment Details */}
            <div className="mb-6">
                <h3 className="mb-4 text-24 font-normal text-[#1A3353]">
                    Payment Details
                </h3>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Item Total Amount</span>
                    <span className="font-semibold">
                        ₹{store.posPurchasedData?.subTotal?.toFixed(2)}/-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Cart Discount</span>
                    <span className="font-semibold">
                        ₹
                        {store.posPurchasedData?.cartDiscountAmount?.toFixed(2)}
                        /-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">GST</span>
                    <span className="font-semibold">
                        ₹{store.posPurchasedData?.gst?.toFixed(2)}
                        /-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Total Amount</span>
                    <span className="font-semibold">
                        ₹{store.posPurchasedData?.total?.toFixed(2)}/-
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Round Off</span>
                    <span className="font-semibold">
                        ₹
                        {(
                            store.posPurchasedData?.total -
                            Math.floor(store.posPurchasedData?.total)
                        ).toFixed(2)}
                    </span>
                </div>
                <div className="mb-2 flex justify-between">
                    <span className="text-[#383838]">Amount Paid by Cash</span>
                    <span className="font-semibold">₹{overallTotal}/-</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold text-[#383838]">
                        Amount Pending
                    </span>
                    <span
                        className={`font-bold ${
                            overallTotal < (store.posPurchasedData?.total ?? 0)
                                ? 'text-red-600'
                                : 'text-green-600'
                        }`}
                    >
                        ₹{' '}
                        {overallTotal >=
                        Math.floor(store.posPurchasedData?.total ?? 0)
                            ? '0.00'
                            : (
                                  Math.floor(
                                      store.posPurchasedData?.total ?? 0
                                  ) - (overallTotal ?? 0)
                              ).toFixed(2)}
                        /-
                    </span>
                </div>
                {overallTotal >
                    Math.floor(store.posPurchasedData?.total ?? 0) && (
                    <div className="flex justify-between border-t pt-2">
                        <span className="font-semibold">Change Due</span>
                        <span className="font-bold text-red-600">
                            ₹ -
                            {(
                                (overallTotal ?? 0) -
                                Math.floor(store.posPurchasedData?.total ?? 0)
                            ).toFixed(2)}
                            /-
                        </span>
                    </div>
                )}
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end gap-10">
                <Button
                    onClick={goBack}
                    className="rounded-lg border border-[#1A3353]        px-10 py-8"
                >
                    Back
                </Button>

                {/* {!isMarkedAsPaid && (
                                    <Button
                                        className="rounded-lg border border-[#1A3353] px-10 py-8"
                                        loading={payLaterLoader}
                                        onClick={() => handleClick('pending')}
                                    >
                                        Pay Later
                                    </Button>
                                )} */}

                <Button
                    loading={loader}
                    onClick={() => handleClick('completed')}
                    className="rounded-lg bg-purpleLight px-10 py-8 text-white"
                >
                    Confirm Payment
                </Button>
            </div>
            {isModalVisible && (
                <DenominationModal
                    onDenominationChange={handleDenominationChange}
                    visible={isModalVisible}
                    onClose={handleClose}
                    setOverallTotal={setOverallTotal}
                    overallTotal={overallTotal}
                />
            )}
        </div>
    );
};

export default CashPayment;
