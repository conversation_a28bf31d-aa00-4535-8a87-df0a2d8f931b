import React, { useEffect, useState } from 'react';
import { Table, Button, ConfigProvider, Input } from 'antd';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLocation } from 'wouter';
import { PurchasePricingPackages } from '~/redux/actions/pricing-actions';
import {
    ClearPosPurchasedData,
    SetPosPurchasedData,
} from '~/redux/slices/purchaged-slice';
import { goBack } from '~/components/common/function';
import Alertify from '~/services/alertify';
import { useLoader } from '~/hooks/useLoader';
import { markOrderAsPaid } from '~/redux/actions/purchased-action';
import { getQueryParams } from '~/utils/getQueryParams';
import Paragraph from 'antd/es/typography/Paragraph';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import { useSelector } from 'react-redux';

const ReceiptTotal: React.FC<{ store: any }> = ({ store }) => {
    const total = store.posPurchasedData?.total || 0;
    const amountPaid = Math.floor(total);
    return (
        <>
            <div className="mb-2 flex justify-between">
                <span className="text-[#383838]">Item Total Amount</span>
                <span className="font-semibold">
                    ₹{store.posPurchasedData?.subTotal?.toFixed(2)}/-
                </span>
            </div>
            <div className="mb-2 flex justify-between">
                <span className="text-[#383838]">Cart Discount</span>
                <span className="font-semibold">
                    ₹{store.posPurchasedData?.cartDiscountAmount?.toFixed(2)}
                    /-
                </span>
            </div>
            <div className="mb-2 flex justify-between">
                <span className="text-[#383838]">GST</span>
                <span className="font-semibold">
                    ₹{store.posPurchasedData?.gst?.toFixed(2)}
                    /-
                </span>
            </div>
            <div className="mb-2 flex justify-between">
                <span className="text-[#383838]">Total Amount</span>
                <span className="font-semibold">₹{total?.toFixed(2)}/-</span>
            </div>
            <div className="mb-2 flex justify-between">
                <span className="text-[#383838]">Round Off</span>
                <span className="font-semibold">
                    ₹
                    {(
                        store.posPurchasedData?.total -
                        Math.floor(store.posPurchasedData?.total)
                    ).toFixed(2)}
                </span>
            </div>
            <div className="mb-2 flex justify-between">
                <span className="text-[#383838]">Amount Paid</span>
                <span className="font-semibold">
                    ₹{Math.floor(amountPaid)}/-
                </span>
            </div>
        </>
    );
};

const columns = [
    {
        title: 'Item',
        dataIndex: 'name',
        key: 'name',
        // ellipsis: true,
        // width: 250,
    },
    {
        title: 'Qty',
        dataIndex: 'quantity',
        align: 'center',
        key: 'quantity',
        width: 90,
    },
    {
        title: 'Price',
        key: 'subTotal',
        dataIndex: 'price',
        align: 'center',
        render: (value: any) => value?.toFixed(2),
    },
    {
        title: 'Discount',
        key: 'discountedValue',
        align: 'center',
        dataIndex: 'discountedValue',
    },
    {
        title: 'GST',
        dataIndex: 'tax',
        key: 'tax',
        align: 'center',
    },
];

interface CreditCardPaymentProps {
    isMarkedAsPaid?: boolean;
}

const CreditCardPayment: React.FC<CreditCardPaymentProps> = ({
    isMarkedAsPaid,
}) => {
    const [_, setLocation] = useLocation();
    const [loader, startLoader, endLoader] = useLoader();
    const params = getQueryParams();
    const orderId = params.orderId;
    const [payLaterLoader, startPayLaterLoader, endPayLaterLoader] =
        useLoader();
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        posPurchasedData: state.purchased_store.posPuchasedData,
        userId: state.auth_store.userId,
    }));
    const [pinModalVisible, setPinModalVisible] = useState<boolean>(false);
    const [paymentStatus, setPaymentStatus] = useState<string | null>(null);

    const handleConfirmStatusChange = () => {
        startLoader();
        const payload: Record<string, any> = {
            invoiceId: orderId,
            status: 'completed',
            amountPaid: store.posPurchasedData?.total,
            // billingAddressId: store.posPurchasedData?.billingAddressId,
            paymentDetails: [
                {
                    paymentMethodId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethodId,
                    paymentMethod:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethod,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: Math.floor(store.posPurchasedData?.total),
                    paymentDate: new Date(),
                    paymentStatus: 'completed',
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                },
            ],
        };

        dispatch(markOrderAsPaid(payload))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(`/order-confirmation/${orderId}`, {
                        replace: true,
                    });
                    Alertify.success('Payment completed successfully');
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(endLoader);
    };

    const handleContinueToPay = (status = paymentStatus) => {
        if (status === 'pending') {
            startPayLaterLoader();
        } else if (status === 'completed') {
            startLoader();
        }

        console.log(
            'store.posPurchasedData=============',
            store.posPurchasedData.customPackageItems
        );

        const payload = {
            cart: {
                facilityId: store.posPurchasedData?.facilityId,
                userId: store.posPurchasedData?.userId,
                items: [
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isPackage)
                        ?.map((item: any) => ({
                            itemType: 'service',
                            itemId: item._id,
                            quantity: item.quantity || 1,
                            promotionId:
                                item.promotion?._id === 'custom'
                                    ? null
                                    : item.promotion?._id,
                            discount:
                                item.promotion?._id &&
                                item.promotion?._id === 'custom'
                                    ? {
                                          type: item.promotion?.type,
                                          value: item.promotion?.value,
                                          discountedBy: store.userId,
                                      }
                                    : null,
                        })) || []),
                    ...(store.posPurchasedData?.purchaseItems
                        ?.filter((item: any) => item.isProduct)
                        ?.map((item: any) => ({
                            itemType: 'product',
                            itemId: item.productId,
                            variantId: item.productVariantId,
                            quantity: item.quantity || 1,
                            promotionId: null,
                            discount: null,
                        })) || []),
                    ...(store.posPurchasedData?.customPackageItems?.map(
                        (item: any) => ({
                            itemType: 'custom_package',
                            itemId: item.customPackageId,
                            quantity: item.quantity || 1,
                            promotionId: null,
                            discount: {
                                type: item.discount?.type,
                                value: item.discount?.value,
                                discountedBy: store.userId,
                            },
                        })
                    ) || []),
                ],
                promotionCode: store.posPurchasedData?.promoCode,
                discount: {
                    type: store.posPurchasedData?.cartDiscountType,
                    value: store.posPurchasedData?.cartDiscount,
                    discountedBy: store.userId,
                },
            },
            paymentDetails: [
                {
                    paymentMethodId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethodId,
                    paymentMethod:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.paymentMethod,
                    transactionId:
                        store?.posPurchasedData?.paymentDetails?.[0]
                            ?.transactionId,
                    amount: Math.floor(store.posPurchasedData?.total),
                    paymentDate: new Date(),
                    paymentStatus: status,
                    paymentGateway: 'Stripe',
                    description: 'Payment for January subscription',
                },
            ],
            isSplittedPayment: false,
            amountPaid: store.posPurchasedData?.total,
            platform: 'Web',
            billingAddressId: store.posPurchasedData?.billingAddressId,
            paymentBy: store.userId,
        };

        console.log(
            'payload ===========gtht===',
            payload.paymentDetails[0].paymentStatus
        );

        dispatch(PurchasePricingPackages(payload))
            .unwrap()
            .then((res: any) => {
                if (res?.status === 200 || res?.status === 201) {
                    setLocation(
                        `/order-confirmation/${res.data?.data?.invoiceId}`,
                        { replace: true }
                    );
                    if (
                        payload?.paymentDetails[0]?.paymentStatus === 'pending'
                    ) {
                        Alertify.success('Your payment is marked pending');
                    } else {
                        Alertify.success('Payment completed successfully');
                    }
                    dispatch(ClearPosPurchasedData());
                }
            })
            .finally(() => {
                if (status === 'pending') {
                    endPayLaterLoader();
                } else if (status === 'completed') {
                    endLoader();
                }
                setPaymentStatus(null);
            });
    };

    const { role } = useSelector((state: any) => state.auth_store);

    const handleConfirm = (status = paymentStatus) => {
        if (isMarkedAsPaid) handleConfirmStatusChange();
        else if (status) handleContinueToPay(status);
    };

    const handleClick = (paymentStatus: string) => {
        setPaymentStatus(paymentStatus);
        handleConfirm(paymentStatus);
    };

    function handleTranscationId(value: any) {
        if (value)
            // store.posPurchasedData = {
            //     ...store.posPurchasedData,
            //     paymentDetails: store.posPurchasedData.paymentDetails.map(
            //         (payment: any) => ({
            //             ...payment,
            //             transactionId: value,
            //         })
            //     ),
            // };

            dispatch(
                SetPosPurchasedData({
                    ...store.posPurchasedData,
                    paymentDetails: store.posPurchasedData.paymentDetails.map(
                        (payment: any) => ({
                            ...payment,
                            transactionId: value,
                        })
                    ),
                })
            );
    }

    const [payLaterOption, setPayLaterOption] = React.useState(false);
    useEffect(() => {
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_point_of_sale_pay_later',
            })
        )
            .unwrap()
            .then((response: any) => {
                console.log(
                    'The status response is::::::',
                    response?.data?.data?.isEnabled
                );
                setPayLaterOption(response?.data?.data?.isEnabled);
            })
            .catch((error: any) =>
                Alertify.error('Error in fetching setting status', error)
            );
    }, []);
    return (
        <div className="bg-white py-10">
            {/* Ant Design Table */}
            <div className="pb-16">
                <ConfigProvider
                    theme={{
                        components: {
                            Table: {
                                cellPaddingBlock: 6,
                            },
                        },
                        token: {
                            boxShadowSecondary:
                                '0 25px 50px -12px rgb(0 0 0 / 0.25)',
                        },
                    }}
                >
                    <Table
                        dataSource={[
                            ...(store.posPurchasedData?.purchaseItems || []),
                            ...(store.posPurchasedData?.customPackageItems ||
                                []),
                        ]}
                        columns={columns}
                        pagination={false}
                        bordered
                    />
                </ConfigProvider>
            </div>
            <div className="mb-9 flex justify-between lg:flex-row lg:items-center @sm:flex-col">
                <div className="checkbox-custom">
                    <Paragraph className="ant-form-item-label mb-0">
                        <label>Notes/Txn ID</label>
                    </Paragraph>
                </div>
                <div className="flex items-center lg:w-[80%] lg:flex-row @sm:flex-col">
                    <Input
                        placeholder={`Enter Transcation Id`}
                        onChange={(e) => handleTranscationId(e.target.value)}
                    />
                </div>
            </div>
            {/* Payment Details */}
            <div className="mb-6">
                <h3 className="mb-4 text-24 font-normal text-[#1A3353]">
                    Payment Details
                </h3>
                <ReceiptTotal store={store} />
                {/* <div className="flex justify-between border-t pt-2">
                    <span className="font-semibold">Amount Pending</span>
                    <span className="font-bold text-red-600">₹ (-1)</span>
                </div> */}
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end gap-10">
                <Button
                    onClick={goBack}
                    className="rounded-lg border border-[#1A3353] px-10 py-8"
                >
                    Back
                </Button>
                {!isMarkedAsPaid && payLaterOption && (
                    <Button
                        className="rounded-lg border border-[#1A3353] px-10 py-8"
                        loading={payLaterLoader}
                        onClick={() => handleClick('pending')}
                    >
                        Pay Later
                    </Button>
                )}
                <Button
                    loading={loader}
                    onClick={() => handleClick('completed')}
                    className="rounded-lg bg-purpleLight px-10 py-8 text-white"
                >
                    Confirm Payment
                </Button>
            </div>
        </div>
    );
};

export default CreditCardPayment;
