import { DeleteOutlined } from '@ant-design/icons';
import { Button, Input, Select, Table } from 'antd';
import Title from 'antd/es/typography/Title';
import AddCustomerClassModal from './add-customer-class-modal';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { getQueryParams } from '~/utils/getQueryParams';
import { useParams } from 'wouter';
import { useLoader } from '~/hooks/useLoader';
import { coursesSchedulingCustomerList } from '~/redux/actions/courses-action';
const { Search } = Input;
function goBack() {
    window.history.back();
}
const dataSource = [
    {
        key: '1',
        NAME: 'John <PERSON>',
        'PHONE NO': '****** 567 890',
        EMAIL: '<EMAIL>',
        BALANCE: '$150',
        'PAYMENT TYPE': 'Credit Card',
        STATUS: 'Active',
        // ACTION: ,
        ACTION: (
            <div className="flex flex-row justify-center">
                <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
            </div>
        ),
    },
    {
        key: '2',
        NAME: 'Jane Smith',
        'PHONE NO': '+44 789 456 123',
        EMAIL: '<EMAIL>',
        BALANCE: '$0',
        'PAYMENT TYPE': 'PayPal',
        STATUS: 'Inactive',
        ACTION: (
            <div className="flex flex-row justify-center">
                <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
            </div>
        ),
    },
    {
        key: '3',
        NAME: 'Michael Brown',
        'PHONE NO': '+91 ************',
        EMAIL: '<EMAIL>',
        BALANCE: '$50',
        'PAYMENT TYPE': 'Debit Card',
        STATUS: 'Pending',
        ACTION: (
            <div className="flex flex-row justify-center">
                <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
            </div>
        ),
    },
    {
        key: '4',
        NAME: 'Emily Johnson',
        'PHONE NO': '+61 400 123 456',
        EMAIL: '<EMAIL>',
        BALANCE: '$200',
        'PAYMENT TYPE': 'Net Banking',
        STATUS: 'Active',
        ACTION: (
            <div className="flex flex-row justify-center">
                <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
            </div>
        ),
    },
    {
        key: '5',
        NAME: 'David Williams',
        'PHONE NO': '+81 ************',
        EMAIL: '<EMAIL>',
        BALANCE: '$75',
        'PAYMENT TYPE': 'UPI',
        STATUS: 'Inactive',
        ACTION: (
            <div className="flex flex-row justify-center">
                <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
            </div>
        ),
    },
];

const columns = [
    {
        title: 'NAME',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'PHONE NO ',
        dataIndex: 'mobile',
        key: 'mobile',
    },
    {
        title: 'EMAIL',
        dataIndex: 'EMAIL',
        key: 'EMAIL',
    },
    {
        title: 'DATE',
        dataIndex: 'date',
        key: 'date',
    },
    {
        title: 'TIME',
        dataIndex: '',
        key: 'TIME',
    },
    {
        title: 'STATUS',
        dataIndex: 'STATUS',
        key: 'STATUS',
    },
    {
        title: <p className="text-center">ACTION</p>,
        dataIndex: 'ACTION',
        key: 'ACTION',
    },
];
const AttendeesList = () => {
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const { id } = useParams<{ id: string }>();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();

    const store = useAppSelector((state) => ({
        courseScheduleCustomerList:
            state.course_store.courseSchedulingCustomerList,
        courseScheduleCustomerListCount:
            state.course_store.courseSchedulingCustomerListCount,
    }));

    console.log(
        'Store----------------------',
        store.courseScheduleCustomerList
    );

    const [isModalVisible, setIsModalVisible] = useState(false);
    const handleOpenModal = () => {
        setIsModalVisible(true);
    };

    useEffect(() => {
        if (id) {
            const payload = {
                page: currentPage,
                pageSize: pageSizes,
                // search: searchParam,
                schedulingId: id,
            };
            dispatch(coursesSchedulingCustomerList({ payload }));
        }
    }, [id]);

    const handleCloseModal = () => {
        setIsModalVisible(false);
    };

    return (
        <div className="flex flex-col gap-12">
            <div className="flex flex-row justify-between">
                <div className="flex items-center gap-4">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353] " level={4}>
                        Sign up
                    </Title>
                </div>

                {/* <Button
                    onClick={handleOpenModal}
                    className="rounded-xl bg-purpleLight px-10 text-white"
                >
                    Add +
                </Button> */}
            </div>
            {/* <div className="flex flex-row justify-between lg:w-[100%]">
                <Search className="w-[20%]" allowClear placeholder="Search" />

                <Select
                    defaultValue="Payment Type"
                    className="w-[20%]"
                    //   onChange={handleChange}
                    options={[
                        {
                            value: 'All',
                            label: 'All',
                        },
                        {
                            value: 'Paid',
                            label: 'Paid',
                        },
                        {
                            value: 'Unpaid',
                            label: 'Unpaid',
                        },
                        {
                            value: 'Free',
                            label: 'Free',
                        },
                    ]}
                />
            </div> */}
            <div className="lg:w-[100%]">
                <Table
                    dataSource={dataSource}
                    columns={columns}
                    pagination={false}
                />
            </div>
            <AddCustomerClassModal
                visible={isModalVisible}
                onClose={handleCloseModal}
            />
        </div>
    );
};

export default AttendeesList;
