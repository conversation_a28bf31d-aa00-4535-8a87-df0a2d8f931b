import {
    DeleteFilled,
    DownOutlined,
    EditFilled,
    EditOutlined,
    InfoCircleFilled,
    LoadingOutlined,
    SettingOutlined,
} from '@ant-design/icons';
import {
    Button,
    Collapse,
    ConfigProvider,
    Dropdown,
    Input,
    Menu,
    Select,
    Space,
    Switch,
    Tooltip,
} from 'antd';
import Title from 'antd/es/typography/Title';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import clsx from 'clsx';
import { PriceListByServiceCategory } from '~/redux/actions/pricing-actions';
import { useLoader } from '~/hooks/useLoader';
import { capitalizeFirstLetter, goBack } from '~/components/common/function';
import { useSelector } from 'react-redux';
import { ServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import { ClassType } from '~/types/enums';
import BookingModal from '~/screens/appointment/booking-modal';
import { useDebounce } from '~/hooks/useDebounce';
import { getQueryParams } from '~/utils/getQueryParams';
// const handleChange = (value: string) => {
//     console.log(`selected ${value}`);
// };
const onChange = (checked: boolean) => {
    console.log(`switch to ${checked}`);
};

const { Search } = Input;

export const classesType = [
    {
        id: 1,
        name: 'Yoga',
        schedules: '6 Scheduled',
    },
    {
        id: 2,
        name: 'Pilates',
        schedules: '4 Scheduled',
    },
    {
        id: 3,
        name: 'Dance',
        schedules: '2 Scheduled',
    },
    {
        id: 4,
        name: 'Boxing',
        schedules: '3 Scheduled',
    },
    {
        id: 5,
        name: 'Cycling',
        schedules: '5 Scheduled',
    },
    {
        id: 6,
        name: 'Swimming',
        schedules: '1 Scheduled',
    },
    {
        id: 7,
        name: 'Animal Flow',
        schedules: '3 Scheduled',
    },
    {
        id: 8,
        name: 'Kickboxing',
        schedules: '4 Scheduled',
    },
    {
        id: 9,
        name: 'CrossFit',
        schedules: '7 Scheduled',
    },
    {
        id: 10,
        name: 'Zumba',
        schedules: '3 Scheduled',
    },
    {
        id: 11,
        name: 'Tai Chi',
        schedules: '2 Scheduled',
    },
    {
        id: 12,
        name: 'HIIT',
        schedules: '5 Scheduled',
    },
    {
        id: 13,
        name: 'Barre',
        schedules: '3 Scheduled',
    },
    {
        id: 14,
        name: 'Strength Training',
        schedules: '6 Scheduled',
    },
    {
        id: 15,
        name: 'Meditation',
        schedules: '2 Scheduled',
    },
    {
        id: 16,
        name: 'TRX',
        schedules: '4 Scheduled',
    },
    {
        id: 17,
        name: 'Aqua Aerobics',
        schedules: '1 Scheduled',
    },
    {
        id: 18,
        name: 'Spin Class',
        schedules: '5 Scheduled',
    },
    {
        id: 19,
        name: 'Parkour',
        schedules: '2 Scheduled',
    },
    {
        id: 20,
        name: 'Piloxing',
        schedules: '3 Scheduled',
    },
];
const classesPriceList = () => {
    const [_, setLocation] = useLocation();
    const dispatch = useAppDispatch();
    const params = getQueryParams();

    const pageParam = Number(params.page);
    const searchParam = params.search;
    const pageSizeParam = Number(params.pageSize);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const store = useAppSelector((state) => ({
        classList: state.class_store.classList,
        classListCount: state.class_store.classListCount,
        pricingListByServiceCategory:
            state.pricing_store.pricingListByServiceCategory,
        ServiceCategoryListData:
            state.service_category_store.ServiceCategoryListData,
        ServiceCategoryListCount:
            state.service_category_store.ServiceCategoryListCount,
    }));
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const [searchValue, setSearchValue] = useState(searchParam || '');

    const [loader, startLoader, endLoader] = useLoader();
    const [loaderClass, startLoaderClass, endLoaderClass] = useLoader();
    const [openedPanel, setOpenedPanel] = useState<{
        serviceCategoryId?: string;
    }>({});

    const [selectedServiceId, setSelectedServiceId] = useState<string | null>(
        null
    );

    useEffect(() => {
        startLoaderClass();
        if (searchParam) {
            debouncedRequest(() => {
                dispatch(
                    ServiceCategoryList({
                        page: currentPage,
                        pageSize: pageSizes,
                        classType: ClassType.CLASSES,
                        search: searchParam,
                    })
                )
                    .unwrap()
                    .then(() => {})
                    .catch(() => {})
                    .finally(endLoaderClass);
            });
        } else {
            dispatch(
                ServiceCategoryList({
                    page: currentPage,
                    pageSize: pageSizes,
                    classType: ClassType.CLASSES,
                })
            )
                .unwrap()
                .then(() => {})
                .catch(() => {})
                .finally(endLoaderClass);
        }
    }, [currentPage, pageSizes, searchParam]);

    const dispatchPriceList = (serviceId: string) => {
        startLoader();
        const payload = {
            serviceId: serviceId,
        };
        dispatch(PriceListByServiceCategory(payload))
            .unwrap()
            .then(() => {
                console.log('Fetched price list successfully.');
            })
            .catch((error: any) => {
                console.error('Error fetching price list:', error);
            })
            .finally(endLoader);
    };

    useEffect(() => {
        if (
            store.ServiceCategoryListData &&
            store.ServiceCategoryListData?.length > 0
        ) {
            const firstClass = store.ServiceCategoryListData[0];
            if (firstClass) {
                setSelectedServiceId(firstClass._id);
                dispatchPriceList(firstClass._id);
            }
        }
    }, [store.ServiceCategoryListData]);

    const handleClassClick = (serviceId: string) => {
        setSelectedServiceId(serviceId);
        dispatchPriceList(serviceId);
    };

    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);

    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleClose = () => {
        setIsModalVisible(false);
    };

    const moreMenu = [
        {
            key: 1,
            label: 'Create or Schedule a Class',
        },
        {
            key: 2,
            label: 'Schedule a Closed Business Day',
        },
        {
            key: 3,
            label: 'Set Up Auto Emails',
        },
        {
            key: 4,
            label: 'Class and course Options',
        },
        {
            key: 5,
            label: 'View Hidden Class Cancellations',
        },
    ];

    return (
        <>
            <div className="flex flex-col gap-8 rounded-lg lg:border  lg:px-16 lg:py-10">
                <div className="flex items-center justify-between">
                    <Title className=" text-[#1a3353]" level={4}>
                        Classes
                    </Title>
                    {/* <Dropdown
                        trigger={['click']}
                        menu={{ items: moreMenu }}
                        placement="bottomRight"
                    >
                        <Button>
                            More
                            <span>
                                <DownOutlined />
                            </span>
                        </Button>
                    </Dropdown> */}
                </div>
                <div>
                    <p className=" text-2xl font-normal text-[#455560]">
                        A class is when a staff member offers services to
                        multiple clients at once. Classes repeat regularly
                        <br /> and allow clients to drop into them without
                        committing to more than one.
                    </p>
                </div>
                <div className="flex items-center justify-between lg:flex-row ">
                    <div className="flex items-end gap-6 lg:flex-row ">
                        <p className="text-3xl font-medium text-[#1a3353]">
                            The Lab
                        </p>
                        <p className="cursor-pointer text-xl text-primary">
                            Rename
                        </p>
                    </div>
                    <div className="flex items-center gap-10">
                        <p className="cursor-pointer text-xl text-[#1a3353]">
                            Live Stream
                        </p>
                        <ConfigProvider>
                            <Switch
                                defaultChecked
                                className={clsx(
                                    'rounded-full transition-colors',
                                    status ? 'bg-purpleLight' : 'bg-secondary'
                                )}
                                onChange={onChange}
                            />
                        </ConfigProvider>
                        <SettingOutlined className="cursor-pointer" />
                    </div>
                </div>
                <div className="flex gap-8   lg:flex-row lg:items-start @sm:flex-col">
                    <div className="flex flex-col lg:w-[50%] lg:gap-5 @sm:gap-5 @sm:rounded-md  @sm:border @sm:p-5">
                        <div className="@sm:px04 flex flex-row  items-center justify-between">
                            <p className="text-3xl font-medium text-[#1a3353]">
                                Classes
                            </p>
                            <Button
                                onClick={() => setLocation('add-class/0')}
                                className=" bg-purpleLight py-6 text-xl"
                                type="primary"
                                htmlType="submit"
                            >
                                Add a class{' '}
                                <span className="text-2xl"> + </span>
                            </Button>
                        </div>
                        <div className="flex h-[290px] flex-col gap-3 overflow-y-scroll  px-4">
                            {loaderClass ? (
                                <div className="flex h-full items-center justify-center">
                                    <LoadingOutlined className="text-[#8143d1]" />{' '}
                                </div>
                            ) : (
                                store.ServiceCategoryListData?.map(
                                    (item: any) => {
                                        const isSelected =
                                            selectedServiceId === item._id;
                                        return (
                                            <div
                                                key={item._id}
                                                className={`group relative flex items-center justify-between border-b-1 px-2 py-3 hover:bg-gray-100 sm:pe-16 @sm:pe-12 @sm:ps-5 ${
                                                    isSelected
                                                        ? 'bg-gray-100 '
                                                        : ''
                                                }`}
                                            >
                                                <div
                                                    onClick={() =>
                                                        handleClassClick(
                                                            item._id
                                                        )
                                                    }
                                                    className={`flex cursor-pointer items-center gap-3 `}
                                                >
                                                    <p
                                                        className={`text-1.5xl ${
                                                            isSelected
                                                                ? 'text-primary'
                                                                : 'text-[#455560]'
                                                        } `}
                                                    >
                                                        {capitalizeFirstLetter(
                                                            item.name
                                                        )}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <p className="text-1.5xl text-[#3E79F7]">
                                                        {item.schedules}
                                                    </p>
                                                    <p className="text-[#E6EBF1]">
                                                        {' '}
                                                        |
                                                    </p>
                                                    <p
                                                        onClick={showModal}
                                                        className="text-1.5xl cursor-pointer text-primary"
                                                    >
                                                        + Schedules
                                                    </p>
                                                </div>
                                                <EditFilled
                                                    onClick={() =>
                                                        setLocation(
                                                            `/add-class/${item._id}`
                                                        )
                                                    }
                                                    className="absolute right-7 border-0 bg-transparent p-0 px-2 py-1 text-[#8143d1] opacity-0 transition-opacity duration-200 group-hover:opacity-100"
                                                />
                                                <Button className="absolute right-0 border-0 bg-transparent p-0 px-2 py-1 text-[#8143d1] opacity-0 transition-opacity duration-200 group-hover:opacity-100">
                                                    <DeleteFilled />
                                                </Button>
                                            </div>
                                        );
                                    }
                                )
                            )}
                        </div>
                    </div>
                    <div className="flex flex-col  lg:w-[50%] lg:gap-5 @sm:gap-5 @sm:rounded-md @sm:border @sm:p-5">
                        <div className="flex flex-col gap-4">
                            <div className="flex flex-row items-center justify-between ">
                                <p className="text-3xl font-medium text-[#1a3353]">
                                    Pricing Options
                                </p>
                                <Button
                                    className=" bg-purpleLight py-6 text-xl"
                                    type="primary"
                                    onClick={() =>
                                        setLocation(
                                            `/duplicate-create-pricing/0?type=classes`
                                        )
                                    }
                                >
                                    Add a Pricing
                                    <span className="text-2xl"> + </span>
                                </Button>
                            </div>
                            <div className="flex flex-row items-center justify-between border-b border-t     py-1 lg:pe-16 lg:ps-8 @sm:px-9">
                                <p className="text-xl font-medium text-[#1a3353]">
                                    Name
                                </p>
                                <p className="text-xl font-medium text-[#1a3353]">
                                    Price
                                </p>
                            </div>
                        </div>
                        <div className="flex h-[255px] flex-col gap-3 overflow-y-scroll  px-4">
                            {loader ? (
                                <div className="flex h-full items-center justify-center">
                                    <LoadingOutlined className="text-[#8143d1]" />{' '}
                                </div>
                            ) : (
                                store.pricingListByServiceCategory?.map(
                                    (item: any) => {
                                        return (
                                            <div
                                                key={item.id}
                                                className="flex items-center justify-between px-4 pb-1 "
                                            >
                                                <p className="text-1.5xl  text-[#1a3353]">
                                                    {capitalizeFirstLetter(
                                                        item.name
                                                    )}
                                                </p>
                                                <div className="flex items-center gap-3">
                                                    <p className="text-1.5xl text-[#1a3353]">
                                                        INR {item.price}
                                                    </p>
                                                </div>
                                            </div>
                                        );
                                    }
                                )
                            )}
                        </div>
                    </div>
                </div>
            </div>
            <div
                className="mx-auto mt-8 flex w-[100%] cursor-pointer gap-4 rounded-xl border p-6"
                onClick={() =>
                    setLocation('add-appointment-service/0?serviceType=classes')
                }
            >
                <p className="text-[#455560]">+ Add new service category</p>
                <Tooltip title="prompt text">
                    <InfoCircleFilled />
                </Tooltip>
            </div>

            {/* <ScheduleClassesModal
                showModal={isModalVisible}
                onClose={handleClose}
                title="Where and When"
                content=""
            /> */}
            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleClose}
                    tabValue={'bookings'}
                />
            )}
        </>
    );
};

export default classesPriceList;
