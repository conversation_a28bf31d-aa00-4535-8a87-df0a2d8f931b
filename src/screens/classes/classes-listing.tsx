import {
    Button,
    Checkbox,
    ConfigProvider,
    Dropdown,
    Menu,
    Pagination,
    Select,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

import {
    DeleteOutlined,
    EditOutlined,
    MoreOutlined,
    StopOutlined,
} from '@ant-design/icons';
import { Link, useLocation, useParams } from 'wouter';
import Title from 'antd/es/typography/Title';
import BookingModal from '../appointment/booking-modal';
import {
    capitalizeFirstLetter,
    formatDate,
    goBack,
} from '~/components/common/function';
import AddCustomerClassModal from '../classes/add-customer-class-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    roomListing,
    roomListingByFacilityId,
} from '~/redux/actions/room-action';
import { FacilitiesList } from '~/redux/actions/facility-action';
import { getQueryParams } from '~/utils/getQueryParams';
import { useLoader } from '~/hooks/useLoader';
import { navigate } from 'wouter/use-location';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import dayjs from 'dayjs';
import Alertify from '~/services/alertify';
import { ClassType } from '~/types/enums';
import SubstituteTrainerModal from '~/screens/courses/substitute-modal';
import {
    classCancelScheduling,
    classDeleteScheduling,
    classSchedulingList,
    getClassSchedulingDetails,
    updateClassScheduling,
} from '~/redux/actions/class-action';

const { Option } = Select;

interface IFacility {
    _id: string;
    facilityName: string;
    [key: string]: any;
}

interface ClassesListing {
    type: string;
}
const ClassesListing: React.FC<ClassesListing> = ({ type }) => {
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const dispatch = useAppDispatch();
    const params = getQueryParams();
    const { id } = useParams<{ id: string }>();
    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);
    const searchParam = params.search;
    const [isSubstituteModalVisible, setIsSubstituteModalVisible] =
        useState(false);
    const [selectedSubstituteSchedule, setSelectedSubstituteSchedule] =
        useState<any>(null);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [loader, startLoader, endLoader] = useLoader();
    const [substituteLoader, startSubtituteLoader, endSubtituteLoader] =
        useLoader();
    const [selectedLocation, setSelectedLocation] = useState<string>();
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [confirmModalMessage, setConfirmModalMessage] = useState('');
    const [selectedAction, setSelectedAction] = useState<
        'delete' | 'cancel' | null
    >(null);
    const [selectedScheduleId, setSelectedScheduleId] = useState<string | null>(
        null
    );
    const [isEdit, setIsEdit] = useState(false);
    const [selectedRooms, setSelectedRooms] = useState<string>();
    const [schedulingId, setSchedulingId] = useState<any>('');

    const store = useAppSelector((state) => ({
        facilityList: state.facility_store.facilityList,
        roomList: state.room_store.roomListByFacilityId,
        classSchedulingList: state.class_store.classSchedulingList,
        classSchedulingListCount: state.class_store.classSchedulingListCount,
        classSchedulingDetails: state.class_store.classSchedulingDetails,
    }));

    console.log('Store----------------', store.classSchedulingDetails);

    const RoomOptions = store.roomList?.map((item: any) => ({
        value: item._id,
        label: item.roomName,
        id: item._id,
    }));

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    useEffect(() => {
        startLoader();
        const payload = {
            page: currentPage,
            pageSize: pageSizes,
            ...(selectedLocation && { facilityId: selectedLocation }),
            ...(selectedRooms && { roomId: selectedRooms }),
        };

        dispatch(classSchedulingList(payload))
            .unwrap()
            .then(() => {})
            .finally(endLoader);
    }, [
        currentPage,
        pageSizes,
        selectedLocation,
        isModalVisible,
        selectedRooms,
    ]);

    useEffect(() => {
        if (selectedLocation)
            dispatch(
                roomListingByFacilityId({
                    facilityId: selectedLocation,
                    // status: true,
                })
            );
    }, [selectedLocation]);

    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 50 })).then((res: any) => {
            setSelectedLocation(res?.payload?.data?.data?.list?.[0]?._id);
        });
    }, []);

    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleClose = () => {
        setIsModalVisible(false);
        setIsEdit(false);
        setSchedulingId('');
    };

    const handleSubstituteTrainer = async (toTrainerId: any) => {
        const scheduleId = selectedSubstituteSchedule?._id;
        if (!scheduleId) return;
        startSubtituteLoader();
        try {
            const result = await dispatch(
                getClassSchedulingDetails({ schedulingId: scheduleId })
            ).unwrap();
            const resData = result?.data?.data;
            const payload: any = {
                organizationId: resData?.organizationId,
                facilityId: resData?.facilityId,
                trainerId: toTrainerId,
                classType: ClassType.CLASSES,
                subType: resData?.subTypeId,
                serviceCategory: resData?.serviceCategoryId,
                duration: resData?.duration,
                roomId: resData?.roomId,
                dateRange: resData?.dateRange,
                date: resData?.date,
                from: resData?.from,
                to: resData?.to,
                notes: resData?.Notes,
                classCapacity: resData?.classCapacity,
                schedulingId: resData?._id,
            };
            await dispatch(updateClassScheduling({ payload: payload }))
                .then((res: any) => {
                    console.log('Res---------------', res);
                    if (
                        res?.payload?.status === 200 ||
                        res?.payload?.status === 201
                    ) {
                        dispatch(
                            classSchedulingList({
                                page: currentPage,
                                pageSize: pageSizes,
                                ...(selectedLocation && {
                                    facilityId: selectedLocation,
                                }),
                                ...(selectedRooms && {
                                    roomId: selectedRooms,
                                }),
                            })
                        );
                        setIsSubstituteModalVisible(false);
                        setSelectedSubstituteSchedule(null);
                        setIsModalVisible(false);
                    }
                })
                .finally(endSubtituteLoader);
        } catch (error) {
            console.error('Failed to substitute trainer:', error);
            Alertify.error('Failed to substitute trainer. Please try again.');
        }
    };

    const handleMenuAction = async (
        action: 'edit' | 'delete' | 'cancel' | 'substitute',
        record: any
    ) => {
        if (action === 'edit') {
            setIsEdit(true);
            // setIsModalVisible(true);
            setSchedulingId(record._id);
            try {
                await dispatch(
                    getClassSchedulingDetails({ schedulingId: record._id })
                ).unwrap();
                setIsModalVisible(true);
            } catch (error) {
                console.error(
                    'Failed to fetch class scheduling details:',
                    error
                );
                Alertify.error(
                    'Failed to fetch schedule details. Please try again.'
                );
            }
            return;
        }

        if (action === 'substitute') {
            setSelectedSubstituteSchedule(record);
            setIsSubstituteModalVisible(true);
            return;
        }

        const formattedDate = formatDate(record.date);
        const message = `Are you sure you want to ${
            action === 'delete' ? 'delete' : 'cancel'
        } this class on ${formattedDate} from ${record.from} to ${record.to}?`;

        setSelectedAction(action);
        setSelectedScheduleId(record._id);
        setConfirmModalMessage(message);
        setConfirmModalVisible(true);
    };

    const getMenu = (record: any) => (
        <Menu
            onClick={({ key }) =>
                handleMenuAction(key as 'edit' | 'delete' | 'cancel', record)
            }
        >
            {/* {record.scheduleStatus !== 'canceled' && (
                <Menu.Item key="edit">
                    <EditOutlined /> &nbsp; Edit Schedule
                </Menu.Item>
            )}
            {record.scheduleStatus !== 'canceled' && (
                <Menu.Item key="substitute">
                    <EditOutlined /> &nbsp; Substitue Staff
                </Menu.Item>
            )}
            <Menu.Item key="delete">
                <DeleteOutlined /> &nbsp; Delete Schedule
            </Menu.Item>
            {record.scheduleStatus !== 'canceled' && (
                <Menu.Item key="cancel">
                    <StopOutlined /> &nbsp; Cancel Schedule
                </Menu.Item>
            )} */}
        </Menu>
    );

    const handleConfirm = () => {
        if (!selectedScheduleId || !selectedAction) return;

        const actionThunk =
            selectedAction === 'delete'
                ? classDeleteScheduling
                : classCancelScheduling;

        dispatch(actionThunk({ schedulingId: selectedScheduleId }))
            .unwrap()
            .then(() => {
                dispatch(
                    classSchedulingList({
                        page: currentPage,
                        pageSize: pageSizes,
                    })
                );
            })
            .catch(() => {})
            .finally(() => {
                setConfirmModalVisible(false);
                setSelectedAction(null);
                setSelectedScheduleId(null);
            });
    };

    const groupedByDate = useMemo(() => {
        const grouped: Record<string, any[]> = {};
        store.classSchedulingList.forEach((item: any) => {
            const dateKey = dayjs(item.date).format('ddd MMMM D YYYY'); // e.g., Tue August 6 2024
            if (!grouped[dateKey]) {
                grouped[dateKey] = [];
            }
            grouped[dateKey].push(item);
        });
        return grouped;
    }, [store.classSchedulingList]);

    const allSchedulingIds = useMemo(() => {
        return store.classSchedulingList.map((s: any) => s._id);
    }, [store.classSchedulingList]);

    return (
        <div className="">
            <div className="flex flex-col gap-10">
                <div className="flex w-full flex-row justify-between py-6">
                    <div className="flex  gap-4">
                        <img
                            src="/icons/back.svg"
                            alt="edit"
                            className="h-[14px] translate-y-3 cursor-pointer"
                            onClick={goBack}
                        />
                        <div className="flex flex-col">
                            <Title className=" text-[#455560]" level={4}>
                                Class Schedule
                            </Title>
                            <p className="text-2xl text-[#455560]">
                                {store.classSchedulingDetails?.name}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center gap-10">
                        <Button
                            onClick={() => setIsModalVisible(true)}
                            className="border border-[#455560]"
                        >
                            Schedule
                        </Button>
                    </div>
                </div>
                <div className="flex w-full flex-row justify-end gap-8 pb-8 pt-8 ">
                    <div className="w-[18%] ">
                        <p className="text-lg font-medium text-[#455560] lg:-mt-7">
                            Location
                        </p>
                        <Select
                            // mode="multiple"
                            maxTagCount="responsive"
                            placeholder="Select locations"
                            value={selectedLocation}
                            onChange={(value) => setSelectedLocation(value)}
                            // style={{ width: '85%' }}
                            className="w-[100%]"
                        >
                            {store.facilityList.map((facility: IFacility) => (
                                <Option
                                    key={facility?._id}
                                    value={facility?._id}
                                >
                                    {facility?.facilityName}
                                </Option>
                            ))}
                        </Select>
                    </div>
                    <div className="  w-[18%]  ">
                        <p className="text-lg font-medium text-[#455560] lg:-mt-7">
                            Room
                        </p>
                        <Select
                            showSearch
                            value={selectedRooms}
                            allowClear
                            maxTagCount="responsive"
                            onChange={(value) => setSelectedRooms(value)}
                            // style={{ width: '85%' }}
                            className="w-[100%]"
                            placeholder="Rooms"
                            filterOption={(input, option) =>
                                String(option?.label ?? '')
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                            }
                            options={RoomOptions}
                        />
                    </div>
                </div>
            </div>

            <div className="w-full overflow-hidden rounded-lg">
                <div className="grid grid-cols-[1fr_1fr_1fr_1fr_1fr_1fr_1fr] items-center bg-white px-4 py-3 text-16 font-medium text-gray-500">
                    <div className=" text-14 font-semibold text-black"></div>
                    <div className="text-14 font-semibold text-black">
                        Sign-In
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Class
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Teacher
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Location
                    </div>
                    <div className=" text-14 font-semibold text-black">
                        Room
                    </div>
                    <div className=" text-center text-14 font-semibold text-black">
                        Action
                    </div>
                </div>
                {Object.entries(groupedByDate).map(
                    ([dayLabel, sessions], i) => (
                        <div
                            key={i}
                            className="my-4 border-b border-gray-100 pb-2"
                        >
                            <div className="col-span-2 flex items-center gap-2  ps-4">
                                {/* <Checkbox
                                    type="checkbox"
                                    className="accent-[#455560]"
                                /> */}
                                <span className="-mt-3 text-14 font-semibold text-[#455560]">
                                    {dayLabel}
                                </span>
                            </div>

                            {sessions?.map((record, index) => (
                                <div
                                    key={record._id}
                                    className={`mb-1 grid grid-cols-[1fr_1fr_1fr_1fr_1fr_1fr_1fr] items-center rounded ${
                                        record.scheduleStatus === 'canceled'
                                            ? 'bg-[#e0e1e2]'
                                            : index % 2 === 0
                                            ? 'bg-[#f8f9fa]'
                                            : 'bg-white'
                                    } px-4 py-3`}
                                >
                                    <div>{`${record.from} - ${record.to}`}</div>
                                    <div>
                                        <Link
                                            to={`/class-attendees/${record._id}`}
                                            className="text-14 text-primary"
                                        >
                                            Sign-In ({record.enrolled || 0}/
                                            {record.checkIns || 0})
                                        </Link>
                                    </div>
                                    <div>
                                        {`${capitalizeFirstLetter(
                                            record.serviceCategory?.name
                                        )} - ${capitalizeFirstLetter(
                                            record.subType?.name
                                        )}`}
                                    </div>
                                    <div>
                                        {capitalizeFirstLetter(
                                            record.trainer?.name
                                        )}
                                    </div>
                                    <div>{record.facility?.facilityName}</div>
                                    <div>
                                        {capitalizeFirstLetter(
                                            record.room?.name
                                        )}
                                    </div>
                                    <div className="text-center">
                                        <Dropdown
                                            overlay={getMenu(record)}
                                            trigger={['click']}
                                        >
                                            <MoreOutlined className="cursor-pointer text-18" />
                                        </Dropdown>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )
                )}
            </div>

            <ConfigProvider
                theme={{
                    components: {
                        Table: {
                            cellPaddingBlock: 8,
                        },
                    },
                }}
            >
                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={store.classSchedulingListCount}
                        pageSizeOptions={['10', '20', '50']}
                        pageSize={pageSizes}
                        onChange={paginate}
                        hideOnSinglePage
                    />
                </div>
            </ConfigProvider>

            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleClose}
                    tabValue={'classes'}
                    classId={id}
                    isEdit={isEdit}
                    scheduleId={schedulingId}
                    facilityId={selectedLocation}
                />
            )}

            {isSubstituteModalVisible && (
                <SubstituteTrainerModal
                    visible={isSubstituteModalVisible}
                    onClose={() => {
                        setIsSubstituteModalVisible(false);
                        setSelectedSubstituteSchedule(null);
                    }}
                    onSubmit={handleSubstituteTrainer}
                    classType={ClassType.CLASSES}
                    selectedSchedule={selectedSubstituteSchedule}
                    loading={substituteLoader}
                />
            )}

            {confirmModalVisible && (
                <CommonConfirmationModal
                    visible={confirmModalVisible}
                    message={confirmModalMessage}
                    onConfirm={handleConfirm}
                    onCancel={() => {
                        setConfirmModalVisible(false);
                        setSelectedAction(null);
                        setSelectedScheduleId(null);
                    }}
                />
            )}
        </div>
    );
};

export default ClassesListing;
