import React, { useState } from 'react';
import { Tabs } from 'antd';
import clsx from 'clsx';
import CustomerListing from './customerListing';
import WavierLeadListingModal from './wavierLead';
import LeadsListing from './leads-listing';

const { TabPane } = Tabs;

const ClientTabNavigation: React.FC = () => {
    const [activeKey, setActiveKey] = useState('clients');

    const handleTabChange = (key: string) => {
        setActiveKey(key);
    };

    const tabs = [
        { key: 'clients', label: 'Clients' },
        { key: 'wavier', label: 'Waiver' },
        { key: 'leads', label: 'Leads' },
    ];

    const renderTabBar = () => (
        <div className="flex gap-12 rounded-md  px-4 py-3 shadow-sm">
            {tabs.map((tab) => {
                const isActive = activeKey === tab.key;
                return (
                    <button
                        key={tab.key}
                        onClick={() => handleTabChange(tab.key)}
                        className={clsx(
                            ' flex w-[50%] items-center justify-center rounded-md px-8  py-3  text-2xl ',
                            isActive
                                ? 'bg-purpleLight text-white shadow'
                                : '  text-[#112D55]'
                        )}
                    >
                        <p className="text-center font-semibold">{tab.label}</p>
                    </button>
                );
            })}
        </div>
    );

    const renderContent = () => {
        switch (activeKey) {
            case 'clients':
                return <CustomerListing />;
            case 'wavier':
                return <WavierLeadListingModal />;
            case 'leads':
                return <LeadsListing />;
        }
    };

    return (
        <div className="w-full">
            {renderTabBar()}
            <div className="mt-6">{renderContent()}</div>
        </div>
    );
};

export default ClientTabNavigation;
