import { UserOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Form, Input, Select } from 'antd';
import { goBack } from '~/components/common/function';

const LeadSms = () => {
    const [form] = Form.useForm();
    const templateMessages: Record<string, string> = {
        diwali: 'Wishing you a joyous Diwali! Enjoy our special festive offers!',
        pilates:
            'Join our exclusive Pilates Workshop this weekend. Limited seats!',
    };

    // Handle template change
    const handleTemplateChange = (value: string) => {
        form.setFieldsValue({
            message: templateMessages[value] || '',
        });
    };
    return (
        <>
            <div className="flex w-[30%] flex-row items-center gap-5">
                <img
                    src="/icons/back.svg"
                    alt="edit"
                    className="h-[10px] cursor-pointer"
                    onClick={goBack}
                />
            </div>

            <p className="pb-10 pt-8 text-center text-2xl font-medium text-[#455560]">
                DLT Registration Is Important To Use SMS Service As Per The New
                TRAI Regulation. Please Check The TRAI Official Website For More
                Details.
            </p>

            <div className="flex flex-row items-center gap-5 pt-5">
                <div className="mx-auto flex flex-col gap-1 rounded-xl border px-7 pt-7 shadow-lg xl:w-1/3 @lg:w-[40%] @2xl:w-[45%]">
                    <div className="flex flex-row items-center gap-2">
                        <UserOutlined className="text-[2rem] text-[#455560]" />
                        <p className="text-3xl font-medium text-[#455560]">
                            Send Bulk SMS
                        </p>
                    </div>
                    <ConfigProvider
                        theme={{
                            components: {
                                Form: {
                                    itemMarginBottom: 22,
                                    verticalLabelMargin: -5,
                                },
                            },
                            token: {
                                borderRadius: 5,
                            },
                        }}
                    >
                        <Form
                            className=" flex flex-col gap-2 pt-8"
                            name="add-trainer"
                            layout="vertical"
                            size="large"
                            autoComplete="off"
                            form={form}
                        >
                            <Form.Item
                                label="Lead"
                                name="lead"
                                className="w-full"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select lead',
                                    },
                                ]}
                            >
                                <Select placeholder="Select Lead"></Select>
                            </Form.Item>
                            <Form.Item
                                label="Number"
                                name="number"
                                className="w-full"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please add number',
                                    },
                                ]}
                            >
                                <Input placeholder="Add Number"></Input>
                            </Form.Item>

                            <Form.Item
                                label="Select Template"
                                name="template"
                                className="w-full"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select template',
                                    },
                                ]}
                            >
                                <Select
                                    onChange={handleTemplateChange}
                                    placeholder="Select Template"
                                >
                                    <Select.Option value="diwali">
                                        Diwali offer
                                    </Select.Option>
                                    <Select.Option value="pilates">
                                        Pilate's workshop
                                    </Select.Option>
                                </Select>
                            </Form.Item>
                            <Form.Item
                                label="Message"
                                name="message"
                                className="w-full"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter message',
                                    },
                                ]}
                            >
                                <Input.TextArea
                                    className="mt-1.5"
                                    placeholder="Enter Message"
                                    autoSize={{ minRows: 3, maxRows: 3 }}
                                />
                            </Form.Item>
                            <div className="flex flex-row justify-end">
                                <Form.Item>
                                    <Button
                                        className="rounded-lg bg-purpleLight px-10   py-7 text-xl text-white  "
                                        htmlType="submit"
                                    >
                                        Send
                                    </Button>
                                </Form.Item>
                            </div>
                        </Form>
                    </ConfigProvider>
                </div>
            </div>
        </>
    );
};

export default LeadSms;
