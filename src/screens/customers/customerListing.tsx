import { ConfigProvider, Dropdown, Pagination, Switch } from 'antd';
import React, { useEffect, useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import CommonTable from '~/components/common/commonTable';
import {
    capitalizeFirstLetter,
    formatDateString,
} from '~/components/common/function';
import { useDebounce } from '~/hooks/useDebounce';
import {
    ClientStatusUpdate,
    CustomerList,
    DeleteClient,
} from '~/redux/actions/customer-action';
import { AppDispatch } from '~/redux/store';
import { getQueryParams } from '~/utils/getQueryParams';
import AddClientModal from './add-client-modal';
import { useLoader } from '~/hooks/useLoader';
import { MoreOutlined } from '@ant-design/icons';
import BookingModal from '../appointment/booking-modal';
import { setCustomerPosDetails } from '~/redux/slices/customer-slice';
import clsx from 'clsx';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useAppSelector } from '~/hooks/redux-hooks';
import { SUBJECT_TYPE, PERMISSIONS_ENUM, RoleType } from '~/types/enums';
import Alertify from '~/services/alertify';
import SharePassModal from './share-pass-modal';
import { GetSettings } from '~/redux/actions/settings-actions';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import AddSubprofileModal from './wavier/add-child-modal';

const columns = [
    // { title: 'Client Id', dataIndex: 'clientId' },
    {
        title: 'Name',
        dataIndex: '',
        // width: "25%",
        render: (record: any) => {
            // console.log('Record--------', record);

            return (
                <Link
                    to={`${'/user-profile'}/${record?._id}?userId=${
                        record?.userId
                    }`}
                >
                    {capitalizeFirstLetter(record.firstName) +
                        ' ' +
                        record.lastName}
                </Link>
            );
        },
    },
    {
        title: 'Phone No',
        dataIndex: '',
        // width: "25%",
        render: (record: any) => {
            // console.log(record);
            return (
                <div>
                    {' '}
                    {record?.countryCode
                        ? record?.countryCode + record?.mobile
                        : record?.mobile}
                </div>
            );
        },
    },
    {
        title: 'Email',
        dataIndex: 'email',
        render: (text: string | undefined | null) => {
            return text?.trim() ? text : <p className="">—</p>;
        },
        // align: "center",
        // width: "10%",
    },
    {
        title: 'Branch',
        dataIndex: 'cityName',
        align: 'center',
        // width: "10%",
    },
    // {
    //     title: 'Status',
    //     dataIndex: 'Status',
    //     align: 'center',
    //     render: (text: any, record: any) => {
    //         return <Switch checked={record.Status === 'active'} />;
    //     },
    // },
    {
        title: 'Date Joined',
        dataIndex: '',
        align: 'center',
        // width: "10%",
        render: (record: any) => {
            return <div>{formatDateString(record.createdAt)}</div>;
        },
    },
];

// const tabsConfig = [
//     {
//         key: 'all',
//         tab: 'All',
//     },
//     {
//         key: 'leads',
//         tab: 'Leads',
//     },
//     {
//         key: 'clients',
//         tab: 'Clients',
//     },
//     {
//         key: 'inactive',
//         tab: 'Inactive Clients',
//     },
// ];

const CustomerListing: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>();

    const { customerListForForms, customerList, customerListCount } =
        useSelector((state: any) => state.customer_store);

    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        clientOnboarding: state.settings_store.clientOnboarding,
    }));

    // console.log('customerList------------', customerList);

    const params = getQueryParams();

    const pageParam = Number(params.page);
    const searchParam = params.search;
    const pageSizeParam = Number(params.pageSize);
    const [activeTab, setActiveTab] = useState('all');
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [clientId, setClientId] = useState<string>('');
    const [loader, startLoader, endLoader] = useLoader();
    const [selectedCity, setSelectedCity] = useState<any>([]);
    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );
    const [location, setLocation] = useLocation();
    const [isDeleting, setIsDeleting] = useState(false);
    const [clientData, setClientData] = useState<any>();
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [openLeadListModal, setOpenLeadListModal] = useState(false);
    const [addSubProfileLeadModel, setaddSubProfileLeadModel] = useState(false);

    const debouncedRequest = useDebounce((callback) => callback(), 500);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        if (searchParam) {
            navigate(
                `?page=${page}&pageSize=${pageSize}&search=${searchParam}`,
                { replace: true }
            );
        } else {
            navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
        }
    }

    // console.log('Console for check');
    // for share pass option(should be visible or not)
    const [sharePassOption, setSharePassOption] = useState(false);
    const [sharePassEnabled, setSharePassEnabled] = useState(false);

    useEffect(() => {
        startLoader();
        const requestParams = {
            page: currentPage,
            pageSize: pageSizes,
        } as any;

        if (selectedCity && selectedCity?.length > 0) {
            requestParams.locationId = selectedCity;
        }

        if (searchParam) {
            debouncedRequest(() => {
                dispatch(
                    CustomerList({
                        ...requestParams,
                        search: searchParam,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        // if(store.role !== RoleType.ORGANIZATION){
                        //                     const firstFacilityId = [res?.data?.data?.list?.[0]?._id]
                        //                     setSelectedCity(firstFacilityId)
                        //                 }
                    })
                    .finally(endLoader);
            });
        } else {
            debouncedRequest(() => {
                dispatch(
                    CustomerList({
                        ...requestParams,
                    })
                )
                    .unwrap()
                    .then((res: any) => {
                        // if(store.role !== RoleType.ORGANIZATION){
                        //                     const firstFacilityId = [res?.data?.data?.list?.[0]?._id]
                        //                     setSelectedCity(firstFacilityId)
                        //                 }
                    })
                    .finally(endLoader);
            });
        }

        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_scheduling_sharepass',
            })
        )
            .then((response: any) => {
                // console.log('The status response is::::::', response);
                setSharePassOption(response?.payload?.data?.data?.isActive);
                setSharePassEnabled(response?.payload?.data?.data?.isEnabled);
            })
            .catch((error: any) =>
                Alertify.error('Error in fetching setting status', error)
            );
    }, [
        currentPage,
        pageSizes,
        searchParam,
        selectedCity,
        setSharePassEnabled,
        addSubProfileLeadModel,
    ]);

    useEffect(() => {
        dispatch(GetSettings({})).unwrap();
    }, []);

    const handleTabChange = (key: string) => {
        setActiveTab(key);
    };

    const [modalVisible, setModalVisible] = useState<boolean>();

    const showModal = () => {
        setModalVisible(true);
        setIsEdit(false);
        setClientId('');
    };
    const handleClose = () => {
        setModalVisible(false);
    };

    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [isSharePassModalVisible, setIsSharePassModalVisible] =
        useState<boolean>(false);

    const handleSharePassClose = () => {
        setIsSharePassModalVisible(false);
    };
    const handleBookingClose = () => {
        setIsModalVisible(false);
        setClientData(null);
    };

    const openEditModal = (record: any) => {
        setIsEdit(true);
        setModalVisible(true);
        setClientId(record?._id);
    };

    // const openAddModal = () => {
    //     setIsEdit(false);
    //     setModalVisible(true);
    //     setClientId('');
    // };

    // console.log("customerListCount------------", customerListCount)

    const handleCancelStatusChange = () => {
        setClientData(null);
        setConfirmationModalVisible(false);
        setIsDeleting(false);
    };

    const handleConfirmStatusChange = () => {
        dispatch(
            ClientStatusUpdate({
                clientId: clientData.userId,
                isActive: !clientData.isActive,
                page: currentPage,
                pageSize: pageSizes,
            })
        ).then(() => {
            Alertify.success('Client status updated successfully');
            setConfirmationModalVisible(false);
            setClientData(null);
        });
    };

    const handleDelete = () => {
        dispatch(DeleteClient(clientData?.userId)).then(async (res: any) => {
            // console.log(res.payload);
            if (res?.payload?.status === 200 || res?.payload?.status === 201) {
                dispatch(
                    CustomerList({
                        page: currentPage,
                        pageSize: pageSizes,
                        search: searchParam,
                    })
                ).unwrap();
            }
        });
        setConfirmationModalVisible(false);
        setIsDeleting(false);
        setClientData(null);
    };

    const openConfirmationModal = (record: any, isDelete: boolean = false) => {
        setClientData(record);
        setConfirmationModalVisible(true);
        setIsDeleting(isDelete);
    };
    // for add client permissions
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );

    console.log('all_permissions_for_role--------', all_permissions_for_role);

    const hasClientWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasClientDeletePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_DELETE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasClientUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type ===
                        SUBJECT_TYPE.AUTHENTICATION_CLIENT_ONBOARDING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.CLIENTS_UPDATE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);

    const hasPurchaseWritePermissions = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) => subject.type === SUBJECT_TYPE.POS
            )
        );
    }, [all_permissions_for_role]);
    const hasAvailabilityPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_AVAILABILITY &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_AVAILABILITY_WRITE
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    console.log('hasClientUpdatePermission', hasClientUpdatePermission);
    const selectColumn = [
        {
            title: 'Status',
            dataIndex: '',
            key: 'status',
            render: (record: any) => {
                return (
                    <ConfigProvider
                        theme={{
                            components: {
                                Switch: {
                                    handleBg: '#fff',
                                },
                            },
                        }}
                    >
                        <Switch
                            className={clsx(
                                'rounded-full transition-colors',
                                record.isActive
                                    ? 'bg-switch-on'
                                    : 'bg-switch-off'
                            )}
                            id="swtich-off"
                            checkedChildren="ON"
                            // className="bg-[#D0D4D7]"
                            unCheckedChildren="OFF"
                            disabled={
                                store.role === RoleType.ORGANIZATION
                                    ? false
                                    : !hasClientUpdatePermission
                            }
                            onChange={() => openConfirmationModal(record)}
                            checked={record.isActive || false}
                        />
                    </ConfigProvider>
                );
            },
        },
        ...(hasClientUpdatePermission ||
        hasPurchaseWritePermissions ||
        hasAvailabilityPermission ||
        hasClientDeletePermission ||
        store.role === RoleType.ORGANIZATION ||
        (sharePassEnabled && store.clientOnboarding?.sharePass)
            ? [
                  {
                      title: 'Action',
                      dataIndex: '',
                      width: '120px',
                      align: 'center',
                      key: 'action',
                      render: (record: any) => {
                          const menuItems = [
                              ...(hasClientUpdatePermission ||
                              store.role === RoleType.ORGANIZATION
                                  ? [
                                        {
                                            key: 'edit',
                                            label: (
                                                <div
                                                    className="text-xl text-[#1A3353]"
                                                    onClick={() =>
                                                        setLocation(
                                                            `/user-profile/${record._id}`
                                                        )
                                                    }
                                                >
                                                    Edit Client
                                                </div>
                                            ),
                                        },
                                    ]
                                  : []),
                              ...(hasPurchaseWritePermissions ||
                              store.role === RoleType.ORGANIZATION
                                  ? [
                                        {
                                            key: 'assign-package',
                                            label: (
                                                <div
                                                    onClick={() => {
                                                        if (!record.isActive) {
                                                            Alertify.error(
                                                                "Client is deactive can't Assign Package"
                                                            );
                                                        } else {
                                                            setLocation(
                                                                `/point-of-sales/${record?.userId}/${record?._id}`
                                                            );
                                                            dispatch(
                                                                setCustomerPosDetails(
                                                                    record
                                                                )
                                                            );
                                                        }
                                                    }}
                                                    className="text-xl text-[#1A3353]"
                                                >
                                                    Assign Package
                                                </div>
                                            ),
                                        },
                                    ]
                                  : []),
                              ...(hasAvailabilityPermission ||
                              store.role === RoleType.ORGANIZATION
                                  ? [
                                        {
                                            key: 'book-&-check-in',
                                            label: (
                                                <div
                                                    onClick={() => {
                                                        setClientData({
                                                            ...record,
                                                        });
                                                        if (!record.isActive) {
                                                            Alertify.error(
                                                                "Client is inactive can't add appointment"
                                                            );
                                                        } else {
                                                            setIsModalVisible(
                                                                true
                                                            );
                                                        }
                                                    }}
                                                    className="text-xl text-[#1A3353]"
                                                >
                                                    Add Session
                                                </div>
                                            ),
                                        },
                                    ]
                                  : []),

                              ...(sharePassEnabled &&
                              store.clientOnboarding?.sharePass
                                  ? [
                                        {
                                            key: 'share-pass',
                                            label: (
                                                <div
                                                    className="text-xl text-[#1A3353]"
                                                    onClick={() => {
                                                        setClientData({
                                                            ...record,
                                                        });
                                                        if (!record.isActive) {
                                                            Alertify.error(
                                                                "Client is deactive, can't share pass."
                                                            );
                                                        } else {
                                                            setIsSharePassModalVisible(
                                                                true
                                                            );
                                                        }
                                                    }}
                                                >
                                                    Share Pass
                                                </div>
                                            ),
                                        },
                                    ]
                                  : []),
                              ...(hasClientDeletePermission ||
                              store.role === RoleType.ORGANIZATION
                                  ? [
                                        {
                                            key: 'delete',
                                            label: (
                                                <div
                                                    onClick={() => {
                                                        openConfirmationModal(
                                                            record,
                                                            true
                                                        );
                                                    }}
                                                    className="text-xl text-[#1A3353]"
                                                >
                                                    Delete
                                                </div>
                                            ),
                                        },
                                    ]
                                  : []),
                          ];
                          return (
                              <>
                                  <span className="flex justify-center gap-5 ">
                                      <div>
                                          <Dropdown
                                              menu={{ items: menuItems }}
                                              trigger={['click']}
                                          >
                                              <MoreOutlined
                                                  style={{
                                                      fontSize: '20px',
                                                      cursor: 'pointer',
                                                  }}
                                              />
                                          </Dropdown>
                                      </div>
                                  </span>
                              </>
                          );
                      },
                  },
              ]
            : []),
    ];

    const combinedColumns = [...columns, ...selectColumn];
    const [search, setSearch] = useState(params.search);

    const handleSearch = (value: string) => {
        // dispatch(setSearchValue(value));
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    };
    const openLeadModal = () => {
        setOpenLeadListModal(true);
    };
    const openChildModal = () => {
        setaddSubProfileLeadModel(true);
    };
    return (
        <div className="">
            <div className="mt-6">
                <CommonTable
                    heading={<p>&nbsp;</p>}
                    className="min-w-min"
                    columns={combinedColumns}
                    dataSource={customerList}
                    onSearch={handleSearch}
                    showSearch={true}
                    search={search}
                    loading={loader}
                    addNewModal={
                        hasClientWritePermission ||
                        store.role === RoleType.ORGANIZATION
                    }
                    showStaffLocation={true}
                    openModal={showModal}
                    addNewTitle="Add Client"
                    addchildButton={
                        hasClientWritePermission ||
                        store.role === RoleType.ORGANIZATION
                    }
                    onAddChildButtonClick={openChildModal}
                    // addNewLink="/"
                    // heading="Clients"
                    {...{
                        selectedCity,
                        setSelectedCity,
                    }}
                    DivWidth="w-[100%]"
                />
                <div className="flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={customerListCount}
                        pageSize={pageSizes}
                        onChange={paginate}
                        pageSizeOptions={['10', '20', '50']}
                        hideOnSinglePage
                    />
                </div>
                {/* </>
                )} */}
            </div>

            {modalVisible && (
                <AddClientModal
                    open={modalVisible}
                    isEdit={isEdit}
                    onClose={handleClose}
                    clientId={clientId}
                />
            )}

            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={
                        isDeleting ? handleDelete : handleConfirmStatusChange
                    }
                    onCancel={handleCancelStatusChange}
                    message={`Are you sure you want to ${
                        isDeleting ? 'delete this client' : 'change the status'
                    }?`}
                />
            )}

            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleBookingClose}
                    clientId={clientData?.userId}
                    facilityId={clientData?.facilityId}
                />
            )}
            {isSharePassModalVisible && (
                <SharePassModal
                    visible={isSharePassModalVisible}
                    onClose={handleSharePassClose}
                    clientId={clientData?.userId}
                    clientName={clientData?.name}
                />
            )}
            {addSubProfileLeadModel && (
                <>
                    <AddSubprofileModal
                        open={addSubProfileLeadModel}
                        onCancel={() => setaddSubProfileLeadModel(false)}
                    />
                </>
            )}
        </div>
    );
};

export default CustomerListing;
