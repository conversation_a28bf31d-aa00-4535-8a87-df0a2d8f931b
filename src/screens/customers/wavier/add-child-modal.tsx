import React, { useEffect, useState } from 'react';
import {
    Modal,
    Input,
    Select,
    DatePicker,
    Form,
    Button,
    ConfigProvider,
    Checkbox,
} from 'antd';
import { CustomerList } from '~/redux/actions/customer-action';
import { useAppDispatch } from '~/hooks/redux-hooks';
import InfiniteScrollSelect from '~/components/common/InfiniteScroll';
import ImportChildFromWavierModal from './import-child.modal';
import dayjs from 'dayjs';
import { addClientMinor } from '~/redux/actions/customerLead-action';
import UploadPhotoModal from './upload-photo-modal';
import { PlusOutlined } from '@ant-design/icons';
import { formatStringWithSpaces } from '~/components/common/function';

const { TextArea } = Input;

interface AddSubprofileModalProps {
    open: boolean;
    onCancel: () => void;
    onSaveSuccess?: () => void;
    selectedParentClient?: {
        value: string;
        label: string;
        userId: string;
        data: any;
    };
}

const policies: Record<string, boolean> = {
    facilityWaiver: false,
    checkForId: false,
    safetyBriefingDone: false,
};

const AddSubprofileModal: React.FC<AddSubprofileModalProps> = ({
    open,
    onCancel,
    onSaveSuccess,
    selectedParentClient,
}) => {
    const dispatch = useAppDispatch();
    const [form] = Form.useForm();
    console.log('check values selectedParentClient', selectedParentClient);

    const [imageUrl, setImageUrl] = useState<any>('/assets/Profile_icon.png');
    const [selectedClient, setSelectClient] = useState<any>('');
    const [selectedClientUserId, setSelectClientUserId] = useState<any>('');
    const [selectedClientData, setSelectedClientData] = useState<any>(null);
    const [importFromWavierButton, setImportFromWavierButton] =
        useState<boolean>(false);
    const [importWavierModal, setImportWavierModal] = useState<boolean>(false);
    const [clientSourceId, setClientSourceId] = useState<string>('');
    const [selectedMinor, setSelectedMinor] = useState<any>([]);
    const [uploadedPhotos, setUploadedPhotos] = useState<boolean>(false);
    const defaultCheckedPolicies = Object.keys(policies).filter(
        (key) => policies[key]
    );
    const [selectedPolicies, setSelectedPolicies] = useState<string[]>(
        defaultCheckedPolicies
    );

    const [selectedMinorIndexForPhoto, setSelectedMinorIndexForPhoto] =
        useState<number | null>(null);

    const [policyDates, setPolicyDates] = useState<Record<string, any>>({});

    useEffect(() => {
        if (selectedParentClient) {
            setSelectClient(selectedParentClient?.value);
            setSelectedClientData(selectedParentClient?.data);
        }
    }, [selectedParentClient]);

    useEffect(() => {
        if (selectedClientData?.photo) {
            setImageUrl(selectedClientData.photo);
        } else {
            setImageUrl('/assets/Profile_icon.png');
        }
        if (selectedClientData?.sourceId) {
            setImportFromWavierButton(true);
            setClientSourceId(selectedClientData?.sourceId);
        } else {
            setImportFromWavierButton(false);
            setClientSourceId('');
        }
    }, [selectedClientData]);
    useEffect(() => {
        if (open) {
            form.setFieldsValue({
                subProfiles: [{}],
                policies: defaultCheckedPolicies,
            });
            setSelectedPolicies(defaultCheckedPolicies);
        }
    }, [open]);

    const fetchCustomerOptions = async (searchText = '', page = 1) => {
        const response = await dispatch(
            CustomerList({
                page,
                pageSize: 10,
                search: searchText,
                isActive: true,
                isParent: true,
            })
        ).unwrap();

        return response?.data?.data?.list?.map((customer: any) => ({
            value: customer._id,
            label: `${customer.firstName} ${customer.lastName}`,
            userId: customer.userId,
            data: customer,
        }));
    };

    const handleSave = async () => {
        try {
            const minorsToSubmit: any[] = [];

            // Add all selected minors (manually added or from waiver)
            if (selectedMinor.length > 0) {
                selectedMinor.forEach((minor: any) => {
                    const cleanedMinor = {
                        firstName: minor.firstName,
                        lastName: minor.lastName || '',
                        dob: dayjs(minor.dob, [
                            'DD-MMM-YYYY',
                            'YYYY-MM-DD',
                            'DD/MM/YYYY',
                        ]).toISOString(),
                        gender: minor.gender?.toLowerCase(),
                        relation: minor.relation || 'child',
                        minorId: minor._id ? minor._id : undefined,
                        photo: minor.photo || '',
                    };
                    minorsToSubmit.push(cleanedMinor);
                });
            }

            // Check if user filled any form fields
            const formValues = form.getFieldsValue();
            const hasFormData = Object.values(formValues).some(
                (val) => val !== undefined && val !== null && val !== ''
            );

            // If form has data, validate and push it
            if (hasFormData) {
                const validatedValues = await form.validateFields();

                minorsToSubmit.push({
                    firstName: validatedValues.firstName,
                    lastName: validatedValues.lastName || '',
                    dob: validatedValues.dob?.toISOString(),
                    gender: validatedValues.gender?.toLowerCase(),
                    relation: validatedValues.relation,
                });
            }

            if (minorsToSubmit.length === 0) {
                console.warn('No minors to submit.');
                return;
            }

            await dispatch(
                addClientMinor({
                    clientId: selectedClientData?._id,
                    minor: minorsToSubmit,
                    wavierSourceId: clientSourceId || undefined,
                })
            );

            form.resetFields();
            onCancel();

            if (onSaveSuccess) {
                onSaveSuccess(); // ✅ Notify parent to redirect
            }
        } catch (error: any) {
            if (error?.errorFields && error.errorFields.length > 0) {
                form.scrollToField(error.errorFields[0].name[0]);
            }
            console.warn('Form validation failed or nothing to submit', error);
        }
    };

    const handlePolicyChange = (checkedValues: string[]) => {
        form.setFieldsValue({ policies: checkedValues });

        // Add or remove dates based on selection
        const updatedDates: Record<string, any> = {};
        checkedValues.forEach((policy) => {
            updatedDates[policy] = policyDates[policy] || null;
        });

        setPolicyDates(updatedDates);
    };

    const handlePolicyDateChange = (policy: string, date: any) => {
        setPolicyDates((prev) => ({
            ...prev,
            [policy]: date,
        }));
    };

    useEffect(() => {
        if (open) {
            form.setFieldsValue({
                subProfiles: [{}],
                policies: defaultCheckedPolicies, // ✅ set on form
            });
            setSelectedPolicies(defaultCheckedPolicies); // ✅ set in state
        }
    }, [open]);

    return (
        <>
            <ConfigProvider
                theme={{
                    components: {
                        Form: {
                            verticalLabelMargin: -5,
                        },
                    },
                }}
            >
                <Modal
                    className=" w-[70%] "
                    title={null}
                    open={open}
                    onCancel={onCancel}
                    footer={null}
                    centered
                    width={1000}
                    bodyStyle={{ padding: 0, borderRadius: 10 }}
                >
                    <div className="w-full    text-center  text-3xl font-semibold text-[#1A3353]">
                        Add Subprofile
                    </div>

                    <div className="flex flex-col gap-4 bg-white px-6 pb-10 pt-6 md:flex-row">
                        <div className="w-[40%] border-r border-gray-200  pr-7">
                            <div className="mb-4 flex justify-center">
                                <img
                                    src={imageUrl}
                                    className="w-h-32 h-32 rounded-full border object-cover"
                                    alt="profile"
                                />
                            </div>

                            <Form
                                variant="borderless"
                                className="space-y-8"
                                layout="horizontal"
                            >
                                <Form.Item
                                    label={
                                        <p className="text-left">
                                            Parent <br /> Client
                                        </p>
                                    }
                                >
                                    <InfiniteScrollSelect
                                        fetchOptions={fetchCustomerOptions}
                                        value={selectedClient}
                                        onChange={(value, option) => {
                                            setSelectClient(value);
                                            setSelectClientUserId(
                                                option.userId
                                            );
                                            setSelectedClientData(option.data);
                                            setSelectedMinor([]);
                                        }}
                                        placeholder="Select Parent Client"
                                        disabled={false}
                                        className="w-full border-b-1 border-[#d1d5db]"
                                    />
                                </Form.Item>
                                {/* <Form.Item label="Client ID">
                                    <Input
                                        // className="border-b-1"
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        disabled
                                        value={
                                            selectedClientData?.clientId || ''
                                        }
                                    />
                                </Form.Item> */}
                                <Form.Item label="Phone">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        disabled
                                        value={
                                            selectedClientData?.countryCode &&
                                            selectedClientData?.mobile
                                                ? `(${selectedClientData?.countryCode}) ${selectedClientData?.mobile}`
                                                : ''
                                        }
                                    />
                                </Form.Item>
                                <Form.Item label="Email">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        disabled
                                        value={selectedClientData?.email || ''}
                                    />
                                </Form.Item>
                                <Form.Item label="Age">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        disabled
                                        value={selectedClientData?.age || ''}
                                    />
                                </Form.Item>
                                <Form.Item label="Location">
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                            backgroundColor: '#f5f5f5',
                                            color: '#a0a0a0',
                                            cursor: 'not-allowed',
                                        }}
                                        disabled
                                        value={
                                            selectedClientData?.facilityName ||
                                            ''
                                        }
                                    />
                                </Form.Item>
                            </Form>

                            <div className="border-[rgba(0, 0, 0, 0.1)] ms-3 mt-7 rounded-md border-1 px-8  py-6 ">
                                <p className="text-xl font-semibold text-[#1A3353]">
                                    Policies
                                </p>
                                <div className="mt-5 gap-y-5 lg:grid lg:grid-cols-1 @sm:flex @sm:flex-col @sm:gap-2">
                                    {policies &&
                                        Object.keys(policies).map(
                                            (policyKey) => (
                                                <div
                                                    key={policyKey}
                                                    className="flex items-center justify-between gap-4"
                                                >
                                                    <Checkbox
                                                        checked={selectedPolicies.includes(
                                                            policyKey
                                                        )}
                                                        onChange={(e) => {
                                                            const isChecked =
                                                                e.target
                                                                    .checked;
                                                            const updatedPolicies =
                                                                isChecked
                                                                    ? [
                                                                          ...selectedPolicies,
                                                                          policyKey,
                                                                      ]
                                                                    : selectedPolicies.filter(
                                                                          (
                                                                              item
                                                                          ) =>
                                                                              item !==
                                                                              policyKey
                                                                      );

                                                            setSelectedPolicies(
                                                                updatedPolicies
                                                            );
                                                            form.setFieldsValue(
                                                                {
                                                                    policies:
                                                                        updatedPolicies,
                                                                }
                                                            );

                                                            // Maintain or remove the date
                                                            setPolicyDates(
                                                                (prev) => {
                                                                    const newDates =
                                                                        {
                                                                            ...prev,
                                                                        };
                                                                    if (
                                                                        !isChecked
                                                                    )
                                                                        delete newDates[
                                                                            policyKey
                                                                        ];
                                                                    else if (
                                                                        !newDates[
                                                                            policyKey
                                                                        ]
                                                                    )
                                                                        newDates[
                                                                            policyKey
                                                                        ] =
                                                                            null;
                                                                    return newDates;
                                                                }
                                                            );
                                                        }}
                                                    >
                                                        <span className="text-[#455560]">
                                                            {formatStringWithSpaces(
                                                                policyKey
                                                            )}
                                                        </span>
                                                    </Checkbox>

                                                    {/* Show DatePicker only if checkbox is checked */}

                                                    <DatePicker
                                                        className="w-[50%]"
                                                        placeholder="Select Date"
                                                        value={
                                                            policyDates[
                                                                policyKey
                                                            ] || dayjs()
                                                        }
                                                        onChange={(date) =>
                                                            handlePolicyDateChange(
                                                                policyKey,
                                                                date
                                                            )
                                                        }
                                                    />
                                                </div>
                                            )
                                        )}
                                </div>
                            </div>
                        </div>
                        <div className="w-[60%] pl-6">
                            {selectedMinor.length > 0 && (
                                <div className="mt-4">
                                    <p className="mb-2 font-semibold  text-[#1A3353]">
                                        Added Client
                                    </p>
                                    <table className="w-full rounded-lg border border-gray-200 text-left ">
                                        <thead className="bg-gray-100 font-semibold text-[#1A3353]">
                                            <tr>
                                                <th className="p-2">NAME</th>
                                                <th className="p-2">GENDER</th>
                                                <th className="p-2">D.O.B.</th>
                                                <th className="p-2">
                                                    RELATION
                                                </th>
                                                <th className="p-2">Photo</th>
                                                <th className="p-2">ACTION</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {selectedMinor.map(
                                                (minor: any, index: number) => (
                                                    <tr
                                                        key={minor._id}
                                                        className="border-t"
                                                    >
                                                        <td className="p-2 capitalize">
                                                            {minor.firstName}{' '}
                                                            {minor.lastName}
                                                        </td>
                                                        <td className="p-2 capitalize">
                                                            {minor.gender}
                                                        </td>
                                                        <td className="p-2">
                                                            {new Date(
                                                                minor.dob
                                                            ).toLocaleDateString(
                                                                'en-GB',
                                                                {
                                                                    day: '2-digit',
                                                                    month: 'short',
                                                                    year: 'numeric',
                                                                }
                                                            )}
                                                        </td>
                                                        <td className="p-2 capitalize">
                                                            {minor.relation ||
                                                                'Child'}
                                                        </td>
                                                        <td className="p-2">
                                                            <div
                                                                className="relative mx-auto h-12 w-12 cursor-pointer"
                                                                onClick={() => {
                                                                    setSelectedMinorIndexForPhoto(
                                                                        index
                                                                    );
                                                                    setUploadedPhotos(
                                                                        true
                                                                    );
                                                                }}
                                                            >
                                                                <img
                                                                    src={
                                                                        minor.photo
                                                                            ? minor.photo
                                                                            : '/assets/Profile_icon.png'
                                                                    }
                                                                    className="h-12 w-12 rounded-full border object-cover"
                                                                    alt="profile"
                                                                />
                                                                {!minor.photo && (
                                                                    <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black bg-opacity-30">
                                                                        <PlusOutlined className="text-xs text-white" />
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </td>

                                                        <td className="p-2">
                                                            {/* <button
                                                        type="button"
                                                        onClick={() => {
                                                            form.setFieldsValue({
                                                                ...minor,
                                                                dob: minor.dob ? dayjs(minor.dob) : null,
                                                            });
                                                            setSelectedMinor((prev: any) => prev.filter((_: any, i: any) => i !== index));
                                                        }}
                                                    >
                                                        <img
                                                            src="/icons/common/edit.svg"
                                                            alt="Edit"
                                                            className="w-6 h-6 hover:opacity-80"
                                                        />
                                                    </button> */}

                                                            <button
                                                                type="button"
                                                                onClick={() =>
                                                                    setSelectedMinor(
                                                                        (
                                                                            prev: any[]
                                                                        ) =>
                                                                            prev.filter(
                                                                                (
                                                                                    _,
                                                                                    i
                                                                                ) =>
                                                                                    i !==
                                                                                    index
                                                                            )
                                                                    )
                                                                }
                                                            >
                                                                <img
                                                                    src="/icons/common/delete.svg"
                                                                    alt="Delete"
                                                                    className="h-5 w-5 text-red-500 hover:text-red-700"
                                                                />
                                                            </button>
                                                        </td>
                                                    </tr>
                                                )
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            )}

                            {importFromWavierButton && (
                                <div className="mb-7 mt-4">
                                    {selectedMinor.length > 0 && (
                                        <p className=" mb-1 font-medium text-gray-700">
                                            Add Another
                                        </p>
                                    )}
                                    <button
                                        className="rounded-md border border-purpleLight  px-4 py-1 font-normal  text-purpleLight"
                                        onClick={() =>
                                            setImportWavierModal(true)
                                        }
                                    >
                                        Import from Waiver
                                    </button>
                                </div>
                            )}
                            {/* Form Section */}
                            <Form
                                layout="horizontal"
                                name="subProfiles"
                                form={form}
                                className="space-y-8"
                                variant="borderless"
                            >
                                <Form.Item
                                    label="First Name"
                                    name="firstName"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'First name is required',
                                        },
                                    ]}
                                >
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Enter First Name"
                                        className="rounded-md"
                                        disabled={!selectedClient}
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Last Name"
                                    name="lastName"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Last name is required',
                                        },
                                    ]}
                                >
                                    <Input
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Enter Last Name"
                                        className="rounded-md"
                                        disabled={!selectedClient}
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Relation to Parent"
                                    name="relation"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Relation is required',
                                        },
                                    ]}
                                >
                                    <Select
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Child / Spouse / Partner"
                                        options={[
                                            { label: 'Child', value: 'child' },
                                            {
                                                label: 'Spouse',
                                                value: 'spouse',
                                            },
                                            {
                                                label: 'Partner',
                                                value: 'partner',
                                            },
                                        ]}
                                        disabled={!selectedClient}
                                        className="rounded-md"
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Gender"
                                    name="gender"
                                    rules={[
                                        {
                                            required: true,
                                            message: 'Gender is required',
                                        },
                                    ]}
                                >
                                    <Select
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Select Gender"
                                        options={[
                                            { label: 'Male', value: 'male' },
                                            {
                                                label: 'Female',
                                                value: 'female',
                                            },
                                            { label: 'Other', value: 'other' },
                                        ]}
                                        disabled={!selectedClient}
                                        className="rounded-md"
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Date of Birth"
                                    name="dob"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Date of Birth is required',
                                        },
                                    ]}
                                >
                                    <DatePicker
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        placeholder="Select DOB"
                                        disabled={!selectedClient}
                                        className="w-full rounded-md"
                                    />
                                </Form.Item>

                                <Form.Item
                                    label="Notes"
                                    name="notes"
                                    className="md:col-span-2"
                                >
                                    <TextArea
                                        style={{
                                            borderBottom: '2px solid #f2f3f5',
                                            borderRadius: 0,
                                        }}
                                        rows={2}
                                        placeholder="Notes..."
                                        disabled={!selectedClient}
                                        className="rounded-md"
                                    />
                                </Form.Item>

                                <Form.Item className="flex justify-end">
                                    <div className="mt-6 flex w-[100%] justify-end gap-4 ">
                                        <Button
                                            type="default"
                                            onClick={() => {
                                                form.validateFields().then(
                                                    (values) => {
                                                        setSelectedMinor(
                                                            (prev: any) => [
                                                                ...prev,
                                                                values,
                                                            ]
                                                        );
                                                        form.resetFields();
                                                    }
                                                );
                                            }}
                                            className="  rounded-md border border-[#1a3353] py-1.5 text-[#1a3353] "
                                        >
                                            Add Another
                                        </Button>
                                        <Button
                                            onClick={handleSave}
                                            type="primary"
                                            className="  rounded-md bg-purpleLight py-1.5 text-white"
                                        >
                                            Save
                                        </Button>
                                    </div>
                                </Form.Item>
                            </Form>
                        </div>
                    </div>
                </Modal>
            </ConfigProvider>
            {importWavierModal && (
                <ImportChildFromWavierModal
                    open={importWavierModal}
                    onCancel={() => setImportWavierModal(false)}
                    clientSourceId={clientSourceId}
                    selectedMinors={selectedMinor}
                    onProceed={(selectedMinorsFromModal: any) => {
                        const importedMinors = selectedMinorsFromModal.map(
                            (m: any) => ({ ...m, source: 'waiver' })
                        );

                        setSelectedMinor((prev: any) => {
                            const manuallyAdded = prev.filter(
                                (m: any) => m.source !== 'waiver'
                            );
                            return [...manuallyAdded, ...importedMinors];
                        });

                        setImportWavierModal(false);
                    }}
                />
            )}
            {uploadedPhotos && (
                <>
                    <UploadPhotoModal
                        open={uploadedPhotos}
                        onCancel={() => setUploadedPhotos(false)}
                        onSave={(photoUrl: string) => {
                            setSelectedMinor((prevMinors: any[]) => {
                                if (
                                    selectedMinorIndexForPhoto !== null &&
                                    selectedMinorIndexForPhoto >= 0 &&
                                    selectedMinorIndexForPhoto <
                                        prevMinors.length
                                ) {
                                    const updatedMinors = [...prevMinors];
                                    updatedMinors[selectedMinorIndexForPhoto] =
                                        {
                                            ...updatedMinors[
                                                selectedMinorIndexForPhoto
                                            ],
                                            photo: photoUrl,
                                        };
                                    return updatedMinors;
                                }
                                return prevMinors;
                            });
                            setUploadedPhotos(false);
                        }}

                        // selectedClientUserId={selectedClientUserId}
                        // selectedMinor={selectedMinor}
                        // setSelectedMinor={setSelectedMinor}
                    />
                </>
            )}
        </>
    );
};

export default AddSubprofileModal;
