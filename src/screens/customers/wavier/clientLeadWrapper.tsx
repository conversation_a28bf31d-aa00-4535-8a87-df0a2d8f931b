import React, { useState } from 'react';
import AddClientLeadModal from './add-individual-client-modal';
import AddSubprofileModal from './add-child-modal';

const ClientLeadWrapper = () => {
    const [openClientModal, setOpenClientModal] = useState(false);
    const [openChildModal, setOpenChildModal] = useState(false);

    return (
        <div>
            <button
                onClick={() => {
                    setOpenClientModal(true);
                }}
            >
                Open Add Client Modal
            </button>

            {openClientModal && (
                <AddClientLeadModal
                    open={openClientModal}
                    onClose={() => setOpenClientModal(false)}
                    onChildModalTrigger={() => setOpenChildModal(true)}
                />
            )}

            {openChildModal && (
                <AddSubprofileModal
                    open={openChildModal}
                    onCancel={() => setOpenChildModal(false)}
                />
            )}
        </div>
    );
};

export default ClientLeadWrapper;
