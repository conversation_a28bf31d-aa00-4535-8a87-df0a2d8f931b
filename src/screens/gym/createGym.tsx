import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
    Button,
    Col,
    ConfigProvider,
    Form,
    Input,
    Row,
    FormProps,
    Typography,
    Select,
    Upload,
    GetProp,
    UploadProps,
    message,
    SelectProps,
    Checkbox,
    Collapse,
} from 'antd';
import React, { useEffect, useState } from 'react';
import ReactQuill from 'react-quill';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'wouter';
import { useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { AmenityGroupList } from '~/redux/actions/amenities-action';
import {
    BillingCityList,
    CityList,
    CountryList,
    UploadImage,
} from '~/redux/actions/common-action';
import {
    CreateFacelityOnBoarding,
    FacilityDetails,
    UpdateFacilityOnboard,
} from '~/redux/actions/facility-action';

import { OrganizationList } from '~/redux/actions/organization-action';
import { AppDispatch } from '~/redux/store';
import { RoleType } from '~/types/enums';
import { getQueryParams } from '~/utils/getQueryParams';
import WorkingHours from './workingHours';
import {
    manipulateWorkingHours,
    manipulateWorkingHoursIntoMoment,
} from '~/utils/transformerFunc';
import dayjs from 'dayjs';
const { Panel } = Collapse;

const { Title } = Typography;
function goBack() {
    window.history.back();
}

// for banner upload
type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

const aminityOptions: SelectProps['options'] = [];

for (let i = 10; i < 36; i++) {
    aminityOptions.push({
        value: i.toString(36) + i,
        label: i.toString(36) + i,
    });
}

const CreateGym: React.FC = () => {
    // For banner upload

    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState<string>();
    const [imageUrls, setImageUrls] = useState<string[]>([]);
    const [citySearchText, setCitySearchText] = useState<string>('');
    const [billingCitySearchText, setBillingCitySearchText] =
        useState<string>('');
    const [organizationSearchText, setOrganizationSearchText] =
        useState<string>('');
    const dispatch = useDispatch<AppDispatch>();
    const { countryList, cityList, billingCityList } = useSelector(
        (state: any) => state.common_store
    );
    const amenitiesGroupList = useSelector(
        (state: any) => state.amenity_store.amenitiesGroupList
    );
    const store = useAppSelector((state) => ({
        organizationList: state.organization_store.organizationList,
        facilityDetails: state.facility_store.facilityDetails,
        workingHours: state.facility_store.facilityDetails?.workingHours,
    }));
    const [location, setLocation] = useLocation();

    const params = getQueryParams();

    const { updateDetails, organizationId, view } = params;

    console.log('updateDetail---------', organizationId);

    // Extract the last segment of the URL
    const facilityId = location.split('/').pop();
    const [loader, startLoader, endLoader] = useLoader();
    const [selectedState, setSelectedState] = useState<string | null>(null);
    const [isSelectedState, setIsSelectedState] = useState<boolean>(false);
    const [BillingSelectedState, setBillingSelectedState] = useState<
        string | null
    >(null);
    const [isBillingSelectedState, setIsBillingSelectedState] =
        useState<boolean>(false);
    const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
    const { role } = useSelector((state: any) => state.auth_store);
    const [orgId, setOrgId] = useState<string>();
    const [isEditable, setIsEditable] = useState<boolean>();
    const [isFormDisabled, setIsFormDisabled] = useState(false);
    const [multiUploadLoading, setMultiUploadLoading] = useState(false);
    // billing details for edit in super admin only
    const [editBillingName, setEditBillingName] = useState<string>('');
    const [editBillingState, setEditBillingState] = useState<string>('');
    const [editBillingCity, setEditBillingCity] = useState<string>('');
    const [editBillingAddress1, setEditBillingAddress1] = useState<string>('');
    const [editBillingAddress2, setEditBillingAddress2] = useState<string>('');
    const [editBillingPostalCode, setEditBillingPostalCode] =
        useState<string>('');
    const [editBillingGstNumber, setEditBillingGstNumber] =
        useState<string>('');
    // END billing details for edit in super admin only

    const [workingHours, setWorkingHours] = useState<any>({
        Mon: [],
        Tue: [],
        Wed: [],
        Thu: [],
        Fri: [],
        Sat: [],
        Sun: [],
    });

    const handleAmenityChange = (checkedValues: string[]) => {
        setSelectedAmenities(checkedValues);
        form.setFieldsValue({ amenities: checkedValues });
    };

    // console.log('role-------------', role);

    const handleImageUpload = (file: any) => {
        setLoading(true);
        dispatch(UploadImage({ file: file.file }))
            .then((res: any) => {
                setImageUrl(res?.payload?.res?.data?.data);
            })
            .finally(() => setLoading(false));
    };

    const handleMultipleUpload = async ({ file, onSuccess, onError }: any) => {
        setMultiUploadLoading(true);
        try {
            // console.log('Files-----------', file);
            const newImageUrls = [...imageUrls];
            const files = file instanceof FileList ? Array.from(file) : [file];
            const uploadPromises = files.map((singleFile) =>
                dispatch(UploadImage({ file: singleFile })).then((res: any) => {
                    return res?.payload?.res?.data?.data;
                })
            );
            const urls = await Promise.all(uploadPromises);
            newImageUrls.push(...urls);
            setImageUrls((prevImageUrls) => [...prevImageUrls, ...urls]);
            onSuccess('Upload successful');
        } catch (error) {
            onError(error);
        } finally {
            setMultiUploadLoading(false);
        }
    };

    const debouncedRequest = useDebounce((callback) => callback(), 200);

    const OrganizationOptions = store.organizationList?.map((item: any) => ({
        value: item._id,
        label: item.organizationName,
        id: item._id,
    }));

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    const uploadButtonMutliple = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {multiUploadLoading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    const CountryOptions = countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const BillingStateptions = countryList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const handleStateChange = (value: string) => {
        setSelectedState(value);
        setIsSelectedState(true);
        form.setFieldValue('city', null);
        dispatch(CityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    const handleBillingStateChange = (value: string) => {
        setBillingSelectedState(value);
        setIsBillingSelectedState(true);
        form.setFieldValue('billingCity', null);
        dispatch(BillingCityList({ stateId: value, page: 1, pageSize: 50 }));
    };

    const CityOptions = cityList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const BillingCityOptions = billingCityList?.map((item: any) => ({
        value: item._id,
        label: item.name,
        id: item._id,
    }));

    const onFinish: FormProps['onFinish'] = (values) => {
        console.log('Success:', values);
        startLoader();

        const payload: any = {
            facilityName: values.facilityName,
            address: {
                state: values.state,
                city: values.city,
                addressLine1: values.addressLine1,
                postalCode: Number(values.postalCode),
            },
            billingDetails: {
                billingName: values.billingName || editBillingName,
                addressLine1: values.billingAddress1 || editBillingAddress1,
                addressLine2: values.billingAddress2 || editBillingAddress2,
                state: values.billingState || editBillingState,
                city: values.billingCity || editBillingCity,
                postalCode: values.billingPostalCode || editBillingPostalCode,
                gstNumber: values.gstnumber || editBillingGstNumber,
            },
            profilePicture: imageUrl,
            workingHours: manipulateWorkingHours(workingHours),
        };
        console.log('Payload is:', payload);
        if (imageUrls?.length > 0) {
            payload.gallery = imageUrls;
        }

        if (role === RoleType.SUPER_ADMIN) {
            if (organizationId) payload.organizationId = organizationId;
        }
        if (values.addressLine2)
            payload.address.addressLine2 = values.addressLine2;

        if (role === RoleType.ORGANIZATION) {
            if (values.contactName) payload.contactName = values.contactName;
            if (values.email) payload.email = values.email;
            if (values.mobile) payload.mobile = values.mobile;
            if (values.description) payload.description = values.description;
            if (selectedAmenities && selectedAmenities?.length > 0)
                payload.amenities = selectedAmenities;
        }
        if (updateDetails === 'true') {
            payload.facilityId = facilityId;
            payload.organizationId = orgId;
        }
        // console.log('Data---------', payload);

        if (!updateDetails && role === RoleType.SUPER_ADMIN) {
            const { workingHours, ...updatedPayload } = payload;

            console.log('payload is', updatedPayload);

            dispatch(CreateFacelityOnBoarding(updatedPayload))
                .unwrap()
                .then((res: any) => {
                    // console.log('Res----------', res);
                    if (res?.status === 200 || res?.status === 201) {
                        setLocation(`/organization-profile/${organizationId}`);
                    }
                })
                .finally(endLoader);
        } else if (updateDetails === 'true') {
            dispatch(UpdateFacilityOnboard({ reqData: payload }))
                .unwrap()
                .then((res: any) => {
                    // console.log('Res----------', res);
                    if (
                        role === RoleType.ORGANIZATION &&
                        (res?.status === 200 || res?.status === 201)
                    ) {
                        setLocation('/facilities-location');
                    } else {
                        setLocation(
                            `/organization-profile/${organizationId || orgId}`
                        );
                    }
                })
                .finally(endLoader);
        }
    };

    const handleImageDelete = (index: number) => {
        setImageUrls((prevUrls) => prevUrls.filter((_, i) => i !== index));
    };

    const toggleEditable = () => {
        setIsEditable((prev) => !prev);
    };

    const getTitle = () => {
        if (view === 'true') {
            return isEditable ? 'Edit Branch1' : 'View Branch1';
        } else if (updateDetails === 'true') {
            return 'Edit Branch';
        } else {
            return 'Add a Branch';
        }
    };

    useEffect(() => {
        if (citySearchText) {
            debouncedRequest(() => {
                dispatch(
                    CityList({
                        stateId: selectedState,
                        page: 1,
                        pageSize: 50,
                        search: citySearchText,
                    })
                );
            });
        }
    }, [citySearchText]);

    useEffect(() => {
        if (billingCitySearchText) {
            debouncedRequest(() => {
                dispatch(
                    BillingCityList({
                        stateId: BillingSelectedState,
                        page: 1,
                        pageSize: 50,
                        search: billingCitySearchText,
                    })
                );
            });
        }
    }, [billingCitySearchText]);
    useEffect(() => {
        if (store.facilityDetails && updateDetails === 'true') {
            const stateId = store.facilityDetails?.address?.state;
            const billingStateId = store.facilityDetails?.billingDetails?.state;
            const cityId = store.facilityDetails?.address?.city;
            const billingCityId = store.facilityDetails?.billingDetails?.city;

            setEditBillingName(
                store.facilityDetails?.billingDetails?.billingName
            );
            setEditBillingState(store.facilityDetails?.billingDetails?.state);
            setEditBillingCity(store.facilityDetails?.billingDetails?.city);
            setEditBillingAddress1(
                store.facilityDetails?.billingDetails?.addressLine1
            );
            setEditBillingAddress2(
                store.facilityDetails?.billingDetails?.addressLine2
            );
            setEditBillingPostalCode(
                store.facilityDetails?.billingDetails?.postalCode
            );
            setEditBillingGstNumber(
                store.facilityDetails?.billingDetails?.gstNumber
            );

            if (stateId) {
                dispatch(CityList({ page: 1, pageSize: 50, cityId: cityId }));
            }
            if (billingStateId) {
                dispatch(
                    BillingCityList({
                        page: 1,
                        pageSize: 50,
                        cityId: billingCityId,
                    })
                );
            }

            const availability = store.facilityDetails.availability?.find(
                (item: any) => item.type === 'available'
            );

            if (availability && availability.workingHours) {
                const hours = {
                    Mon:
                        availability.workingHours.mon?.map((slot: any) => ({
                            from: dayjs(slot.from, 'HH:mm'),
                            to: dayjs(slot.to, 'HH:mm'),
                        })) || [],
                    Tue:
                        availability.workingHours.tue?.map((slot: any) => ({
                            from: dayjs(slot.from, 'HH:mm'),
                            to: dayjs(slot.to, 'HH:mm'),
                        })) || [],
                    Wed:
                        availability.workingHours.wed?.map((slot: any) => ({
                            from: dayjs(slot.from, 'HH:mm'),
                            to: dayjs(slot.to, 'HH:mm'),
                        })) || [],
                    Thu:
                        availability.workingHours.thu?.map((slot: any) => ({
                            from: dayjs(slot.from, 'HH:mm'),
                            to: dayjs(slot.to, 'HH:mm'),
                        })) || [],
                    Fri:
                        availability.workingHours.fri?.map((slot: any) => ({
                            from: dayjs(slot.from, 'HH:mm'),
                            to: dayjs(slot.to, 'HH:mm'),
                        })) || [],
                    Sat:
                        availability.workingHours.sat?.map((slot: any) => ({
                            from: dayjs(slot.from, 'HH:mm'),
                            to: dayjs(slot.to, 'HH:mm'),
                        })) || [],
                    Sun:
                        availability.workingHours.sun?.map((slot: any) => ({
                            from: dayjs(slot.from, 'HH:mm'),
                            to: dayjs(slot.to, 'HH:mm'),
                        })) || [],
                };
                setWorkingHours(hours);
            }

            form.setFieldsValue({
                facilityName: store.facilityDetails?.facilityName,
                organizationId: store.facilityDetails?.organizationId,
                mobile: store.facilityDetails?.mobile,
                email: store.facilityDetails?.email,
                state: store.facilityDetails?.address?.state,
                city: store.facilityDetails?.address?.city,
                addressLine1: store.facilityDetails?.address?.addressLine1,
                postalCode: store.facilityDetails?.address?.postalCode,
                addressLine2: store.facilityDetails?.address?.addressLine2,
                contactName: store.facilityDetails?.contactName,
                description: store.facilityDetails?.description,
                billingName: store.facilityDetails?.billingDetails?.billingName,
                billingState: store.facilityDetails?.billingDetails?.state,
                billingCity: store.facilityDetails?.billingDetails?.city,
                billingAddress1:
                    store.facilityDetails?.billingDetails?.addressLine1,
                billingAddress2:
                    store.facilityDetails?.billingDetails?.addressLine2,
                billingPostalCode:
                    store.facilityDetails?.billingDetails?.postalCode,
                gstnumber: store.facilityDetails?.billingDetails?.gstNumber,
            });

            setImageUrl(store.facilityDetails?.profilePicture);
            setImageUrls(store.facilityDetails?.gallery);
            setOrgId(store.facilityDetails?.organizationId);
            setSelectedAmenities(store.facilityDetails?.amenities);
        }
    }, [store.facilityDetails]);

    useEffect(() => {
        if (updateDetails === 'true') {
            dispatch(FacilityDetails({ facilityId: facilityId }));
        }
    }, [updateDetails]);

    useEffect(() => {
        if (role === RoleType.SUPER_ADMIN) {
            if (organizationSearchText) {
                debouncedRequest(() => {
                    dispatch(
                        OrganizationList({
                            page: 1,
                            pageSize: 30,
                            search: organizationSearchText,
                        })
                    );
                });
            } else {
                dispatch(OrganizationList({ page: 1, pageSize: 30 }));
            }
        }
    }, [organizationSearchText]);

    useEffect(() => {
        if (view === 'true' && !isEditable) {
            setIsFormDisabled(true);
        } else {
            setIsFormDisabled(false);
        }
    }, [view, isEditable]);

    useEffect(() => {
        dispatch(CountryList({ page: 1, pageSize: 50 }));
        if (role === RoleType.ORGANIZATION) {
            dispatch(AmenityGroupList());
        }
    }, []);

    useEffect(() => {
        if (store.workingHours) {
            console.log({ ...store.workingHours });
            const data = manipulateWorkingHoursIntoMoment(store.workingHours);
            console.log({ data });
            setWorkingHours(data);
        } else {
            setWorkingHours({
                Mon: [{ from: null, to: null }],
                Tue: [],
                Wed: [],
                Thu: [],
                Fri: [],
                Sat: [],
                Sun: [],
            });
        }
    }, [store.workingHours]);

    console.log({ workingHours });

    return (
        <ConfigProvider
            theme={{
                components: {
                    Typography: {
                        titleMarginBottom: 0,
                        titleMarginTop: 0,
                    },
                    Input: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                    Select: {
                        colorPrimary: '#E6EBF1',
                        colorPrimaryActive: '#E6EBF1',
                        colorPrimaryHover: '#E6EBF1',
                        borderRadius: 4,
                    },
                },
                token: {
                    borderRadius: 4,
                },
            }}
        >
            <div className="flex items-center justify-between pb-12 lg:pe-5">
                <div className="flex items-center gap-4  @sm:mb-10 ">
                    <img
                        src="/icons/back.svg"
                        alt="edit"
                        className="h-[10px] cursor-pointer"
                        onClick={goBack}
                    />
                    <Title className="text-[#1A3353]" level={4}>
                        {/* {(view ===  'true' && isEdittable === false)
                        ? 'View Branch'
                        : updateDetails === 'true'
                        ? 'Edit Branch'
                        : 'Add a Branch'} */}
                        {getTitle()}
                    </Title>
                </div>
                {view === 'true' && (
                    <div onClick={() => toggleEditable()}>
                        <img
                            src="/icons/common/edit.svg"
                            alt="edit"
                            className="ms-auto h-[20px] cursor-pointer"
                        />
                    </div>
                )}
            </div>

            <Row className="justify-around @sm:gap-10 ">
                <Col
                    className="rounded-lg border p-5  lg:p-10  @sm:w-full"
                    lg={15}
                >
                    <Form
                        name="gymCreate"
                        layout="vertical"
                        size="large"
                        form={form}
                        initialValues={{
                            amenities: [],
                        }}
                        onFinish={onFinish}
                        autoComplete="off"
                        disabled={isFormDisabled}
                    >
                        {role === RoleType.SUPER_ADMIN && (
                            <Form.Item
                                label="Organization Name"
                                name="Organization Name"
                                rules={[
                                    {
                                        required: false,
                                        message: 'Please select organization',
                                    },
                                ]}
                            >
                                <Select
                                    className=""
                                    showSearch
                                    disabled
                                    placeholder="Select Organization"
                                    defaultValue={
                                        organizationId ? organizationId : ''
                                    }
                                    onSearch={(value) =>
                                        setOrganizationSearchText(value)
                                    }
                                    filterOption={(input, option) =>
                                        String(option?.label ?? '')
                                            ?.toLowerCase()
                                            .includes(input.toLowerCase())
                                    }
                                    options={OrganizationOptions}
                                    onChange={() => console.log('Value')}
                                />
                            </Form.Item>
                        )}
                        <Form.Item
                            label="Branch Name"
                            name="facilityName"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter a branch name',
                                },
                            ]}
                        >
                            <Input placeholder="Enter branch name" />
                        </Form.Item>

                        {role && role === RoleType.ORGANIZATION && (
                            <>
                                <Form.Item
                                    label="Phone Number"
                                    name="mobile"
                                    rules={[
                                        {
                                            required: true,
                                            message:
                                                'Please enter mobile number!',
                                        },
                                    ]}
                                >
                                    {/* <PhoneInput enableSearch /> */}
                                    <Input
                                        type="text"
                                        maxLength={10}
                                        placeholder="Enter Phone Number"
                                        onInput={(e: any) => {
                                            e.target.value =
                                                e.target.value.replace(
                                                    /[^0-9]/g,
                                                    ''
                                                );
                                        }}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label="Email"
                                    name="email"
                                    rules={[
                                        {
                                            type: 'email',
                                            required: true,
                                            message:
                                                'Please enter right email!',
                                        },
                                    ]}
                                >
                                    <Input placeholder="Enter Email" />
                                </Form.Item>
                            </>
                        )}

                        <Form.Item
                            label="Branch Address"
                            name="addressLine1"
                            rules={[
                                {
                                    required: true,
                                    message:
                                        'Please enter your bussiness address!',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Address" />
                        </Form.Item>
                        <Form.Item
                            label="Address Line 2"
                            name="addressLine2"
                            rules={[
                                {
                                    required: false,
                                    message: 'Please enter  address line 2!',
                                },
                            ]}
                        >
                            <Input placeholder="Enter Address" />
                        </Form.Item>
                        <Form.Item
                            label="State"
                            name="state"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select state!',
                                },
                            ]}
                        >
                            <Select
                                showSearch
                                placeholder="Select state"
                                filterOption={(input, option) =>
                                    String(option?.label ?? '')
                                        ?.toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={CountryOptions}
                                onChange={handleStateChange}
                            />
                        </Form.Item>
                        <Form.Item
                            label="City"
                            name="city"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select city!',
                                },
                            ]}
                        >
                            <Select
                                showSearch
                                disabled={!isSelectedState}
                                onSearch={(value) => setCitySearchText(value)}
                                placeholder="Select city"
                                filterOption={(input, option) =>
                                    String(option?.label ?? '')
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                }
                                options={CityOptions}
                            />
                        </Form.Item>
                        <Form.Item
                            label="Postal Code"
                            name="postalCode"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please enter your postal code!',
                                },
                                {
                                    pattern: /^[0-9]{6}$/,
                                    message:
                                        'Postal Code must be exactly 6 digits and contain only numbers',
                                },
                            ]}
                        >
                            {/* <PhoneInput enableSearch /> */}
                            <Input
                                maxLength={6}
                                placeholder="Enter postal code"
                            />
                        </Form.Item>
                        {role && role === RoleType.ORGANIZATION && (
                            <>
                                <Form.Item
                                    label="Contact Name"
                                    name="contactName"
                                    rules={[
                                        {
                                            required: false,
                                            message:
                                                'Please enter your contact name!',
                                        },
                                    ]}
                                >
                                    <Input placeholder="Enter contact name" />
                                </Form.Item>
                                <Form.Item
                                    label="Description"
                                    name="description"
                                    rules={[
                                        {
                                            required: false,
                                            message:
                                                'Please enter description !',
                                        },
                                    ]}
                                >
                                    <ReactQuill
                                        theme="snow"
                                        // className="h-[20vh] rounded-lg"
                                    />
                                </Form.Item>
                                <Form.Item
                                    name="amenities"
                                    labelCol={{ span: 24 }}
                                    rules={[
                                        {
                                            required: false,
                                            message: 'Please select amenities!',
                                        },
                                    ]}
                                >
                                    <ConfigProvider
                                        theme={{
                                            token: {
                                                colorPrimary: '#8143D1',
                                            },
                                            components: {
                                                Collapse: {
                                                    contentBg: '#fff',
                                                    headerBg: '#fff',
                                                },
                                            },
                                        }}
                                    >
                                        <Collapse
                                            // bordered={false}
                                            className="w-full rounded-2xl"
                                            defaultActiveKey={['1']}
                                        >
                                            <Panel
                                                header={
                                                    <p className="rounded-2xl text-2xl font-medium text-[#1A3353]">
                                                        Add Amenities
                                                    </p>
                                                }
                                                key="1"
                                            >
                                                <div className="  ">
                                                    <Checkbox.Group
                                                        className="grid lg:grid-cols-3 @sm:grid-cols-1 @sm:gap-4"
                                                        value={
                                                            selectedAmenities
                                                        }
                                                        onChange={
                                                            handleAmenityChange
                                                        }
                                                    >
                                                        {amenitiesGroupList &&
                                                            [
                                                                ...amenitiesGroupList,
                                                            ]
                                                                ?.sort(
                                                                    (
                                                                        a: any,
                                                                        b: any
                                                                    ) =>
                                                                        a._id.localeCompare(
                                                                            b._id
                                                                        )
                                                                )
                                                                ?.map(
                                                                    (
                                                                        category: any,
                                                                        categoryIndex: any
                                                                    ) => (
                                                                        <div
                                                                            className=""
                                                                            key={
                                                                                categoryIndex +
                                                                                category._id
                                                                            }
                                                                        >
                                                                            <div className="text-xl  text-[#acabab]">
                                                                                {category._id
                                                                                    .replace(
                                                                                        / & /g,
                                                                                        ' & '
                                                                                    )
                                                                                    .toUpperCase()}
                                                                            </div>
                                                                            <div
                                                                                // style={{
                                                                                //     display:
                                                                                //         'grid',
                                                                                //     gridTemplateColumns:
                                                                                //         'repeat(auto-fill, minmax(200px, 1fr))',
                                                                                //     gap: '16px',
                                                                                //     maxHeight:
                                                                                //         '300px',
                                                                                //     overflowY:
                                                                                //         'auto',
                                                                                // }}
                                                                                className=" flex flex-col gap-2 pb-3 pt-2"
                                                                            >
                                                                                {category.list.map(
                                                                                    (
                                                                                        item: any,
                                                                                        itemIndex: any
                                                                                    ) => (
                                                                                        <div
                                                                                            key={
                                                                                                itemIndex +
                                                                                                category._id
                                                                                            }
                                                                                        >
                                                                                            <Checkbox
                                                                                                className=""
                                                                                                value={
                                                                                                    item._id
                                                                                                }
                                                                                            >
                                                                                                <p className="text-xl  text-[#455560]">
                                                                                                    {
                                                                                                        item.name
                                                                                                    }
                                                                                                </p>
                                                                                            </Checkbox>
                                                                                        </div>
                                                                                    )
                                                                                )}
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                )}
                                                    </Checkbox.Group>
                                                </div>
                                            </Panel>
                                        </Collapse>
                                    </ConfigProvider>
                                </Form.Item>
                                <Form.Item
                                    label=""
                                    name="workinghours"
                                    rules={[
                                        {
                                            required: false,
                                            message:
                                                'Please enter working hours !',
                                        },
                                    ]}
                                >
                                    <WorkingHours
                                        workingHours={workingHours}
                                        setWorkingHours={setWorkingHours}
                                    />
                                </Form.Item>

                                <ConfigProvider
                                    theme={{
                                        token: {
                                            colorPrimary: '#8143D1',
                                        },
                                        components: {
                                            Collapse: {
                                                contentBg: '#fff',
                                                headerBg: '#fff',
                                            },
                                        },
                                    }}
                                >
                                    <Collapse
                                        // bordered={false}
                                        className="w-full rounded-2xl"
                                        defaultActiveKey={['1']}
                                    >
                                        <Panel
                                            header={
                                                <p className="rounded-2xl text-2xl font-medium text-[#1A3353]">
                                                    Billing Details
                                                </p>
                                            }
                                            key="1"
                                        >
                                            <div className="flex flex-col ">
                                                <Form.Item
                                                    label="Billing Name"
                                                    name="billingName"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please enter a billing name',
                                                        },
                                                    ]}
                                                >
                                                    <Input placeholder="Enter billing name" />
                                                </Form.Item>
                                                <Form.Item
                                                    label="Billing Address 1"
                                                    name="billingAddress1"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please enter a billing address',
                                                        },
                                                    ]}
                                                >
                                                    <Input placeholder="Enter billing address" />
                                                </Form.Item>
                                                <Form.Item
                                                    label="Billing Address 2"
                                                    name="billingAddress2"
                                                    rules={[
                                                        {
                                                            required: false,
                                                            message:
                                                                'Please enter a billing address',
                                                        },
                                                    ]}
                                                >
                                                    <Input placeholder="Enter billing address" />
                                                </Form.Item>
                                                <Form.Item
                                                    label="State / Province"
                                                    name="billingState"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please enter a state or province',
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        showSearch
                                                        placeholder="Select state"
                                                        filterOption={(
                                                            input,
                                                            option
                                                        ) =>
                                                            String(
                                                                option?.label ??
                                                                    ''
                                                            )
                                                                ?.toLowerCase()
                                                                .includes(
                                                                    input.toLowerCase()
                                                                )
                                                        }
                                                        options={
                                                            BillingStateptions
                                                        }
                                                        onChange={
                                                            handleBillingStateChange
                                                        }
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    label="City"
                                                    name="billingCity"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please enter a city',
                                                        },
                                                    ]}
                                                >
                                                    <Select
                                                        showSearch
                                                        disabled={
                                                            !isBillingSelectedState
                                                        }
                                                        onSearch={(value) =>
                                                            setBillingCitySearchText(
                                                                value
                                                            )
                                                        }
                                                        placeholder="Select city"
                                                        filterOption={(
                                                            input,
                                                            option
                                                        ) =>
                                                            String(
                                                                option?.label ??
                                                                    ''
                                                            )
                                                                .toLowerCase()
                                                                .includes(
                                                                    input.toLowerCase()
                                                                )
                                                        }
                                                        options={
                                                            BillingCityOptions
                                                        }
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    label="Postal Code"
                                                    name="billingPostalCode"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please enter a postal code',
                                                        },
                                                        {
                                                            pattern:
                                                                /^[0-9]{6}$/,
                                                            message:
                                                                'Postal Code must be exactly 6 digits and contain only numbers',
                                                        },
                                                    ]}
                                                >
                                                    {/* <PhoneInput enableSearch /> */}
                                                    <Input
                                                        maxLength={6}
                                                        placeholder="Enter postal code"
                                                    />
                                                </Form.Item>
                                                <Form.Item
                                                    label="GST No."
                                                    name="gstnumber"
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Please enter GST No.',
                                                        },
                                                    ]}
                                                >
                                                    <Input placeholder="Enter GST No." />
                                                </Form.Item>
                                            </div>
                                        </Panel>
                                    </Collapse>
                                </ConfigProvider>
                            </>
                        )}
                        {((view === 'true' && isEditable) ||
                            view !== 'true') && (
                            <Form.Item className="mt-8 flex justify-end gap-8 @sm:hidden ">
                                <Button
                                    // loading={loader}
                                    onClick={() => goBack()}
                                    htmlType="button"
                                    className="w-[110px] border-[#1A3353] bg-[#fff]  text-black "
                                >
                                    <p> Cancel </p>
                                </Button>
                                <Button
                                    loading={loader}
                                    htmlType="submit"
                                    className="w-[110px]  bg-purpleLight  text-white lg:ms-7"
                                >
                                    <p> Save </p>
                                </Button>
                            </Form.Item>
                        )}
                    </Form>
                </Col>
                <Col
                    className="rounded-lg border lg:p-10 @sm:w-full @sm:p-5 "
                    lg={8}
                >
                    <div className="">
                        <Typography.Title level={5}>
                            <span className="text-primary ">
                                UPLOAD BRANCH BANNER
                            </span>
                        </Typography.Title>
                        <Upload
                            id="banner-upload"
                            name="avatar"
                            listType="picture-card"
                            className="avatar-uploader overflow-hidden lg:mt-8 @sm:mt-2"
                            showUploadList={false}
                            // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                            // beforeUpload={beforeUpload}
                            // onChange={handleChange}
                            disabled={isFormDisabled}
                            customRequest={handleImageUpload}
                        >
                            {imageUrl ? (
                                <div className="relative h-full w-full">
                                    <img
                                        src={imageUrl}
                                        className="object-center lg:object-cover @sm:rounded-3xl @sm:object-cover"
                                        alt="avatar"
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                        }}
                                    />
                                    {loading && (
                                        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/50">
                                            <LoadingOutlined
                                                style={{
                                                    fontSize: 24,
                                                    color: '#8143D1',
                                                }}
                                                spin
                                            />
                                        </div>
                                    )}
                                </div>
                            ) : (
                                uploadButton
                            )}
                        </Upload>
                    </div>
                    {role && role === RoleType.ORGANIZATION && (
                        <>
                            <div className="mt-10 ">
                                <Typography.Title level={5}>
                                    <span className="text-primary">
                                        UPLOAD BRANCH PHOTOS (8)
                                    </span>
                                </Typography.Title>
                                <Upload
                                    name="avatar"
                                    listType="picture-card"
                                    className="avatar-uploader lg:mt-8  lg:w-[50%] @sm:mt-2"
                                    showUploadList={false}
                                    // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                                    // beforeUpload={beforeUpload}
                                    // onChange={handleMultipleUpload}
                                    customRequest={handleMultipleUpload}
                                    multiple={true}
                                    disabled={isFormDisabled}
                                >
                                    {imageUrls?.length >= 8
                                        ? null
                                        : uploadButtonMutliple}
                                </Upload>
                                <div className="mt-4 grid grid-cols-4 gap-4">
                                    {imageUrls?.map((url, index) => (
                                        <div
                                            key={index}
                                            className="relative h-24 w-full"
                                        >
                                            <img
                                                src={url}
                                                alt={`uploaded-${index}`}
                                                className="h-full w-full rounded object-cover"
                                            />
                                            <div
                                                className="absolute right-0 top-0 flex h-6 w-6 cursor-pointer items-center justify-center rounded-full bg-white p-1"
                                                onClick={() =>
                                                    handleImageDelete(index)
                                                }
                                            >
                                                <img
                                                    src="/icons/common/delete.svg"
                                                    alt="delete"
                                                    className="h-[15px]"
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </>
                    )}

                    <Form.Item className="mt-8 flex justify-center  lg:hidden ">
                        <Button
                            onClick={() => goBack()}
                            htmlType="button"
                            className="me-6 w-[110px] border-[#1A3353]  bg-[#fff]  text-[#1A3353] "
                        >
                            Cancel
                        </Button>
                        <Button
                            loading={loader}
                            htmlType="submit"
                            className=" w-[110px] bg-purpleLight text-white"
                        >
                            Save
                        </Button>
                    </Form.Item>
                </Col>
            </Row>
        </ConfigProvider>
    );
};

export default CreateGym;
