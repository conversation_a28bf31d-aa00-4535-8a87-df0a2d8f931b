import React, { useEffect, useState } from 'react';
import {
    Modal,
    Button,
    Form,
    DatePicker,
    TimePicker,
    ConfigProvider,
    Input,
    Checkbox,
    Select,
    Table,
} from 'antd';
import DenominationModal from '../payment pages/payment components/denomination-modal';
import dayjs, { Dayjs } from 'dayjs';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import {
    CreateReconsiliation,
    GetReconsiliation,
} from '~/redux/actions/purchased-action';
import { RoleType } from '~/types/enums';
import {
    FacilitiesList,
    GetFacilityListByStaffId,
} from '~/redux/actions/facility-action';
import { useLoader } from '~/hooks/useLoader';
import FullLoader from '~/components/library/loader/full-loader';
import Paragraph from 'antd/es/typography/Paragraph';
import { ZoutReportExport } from '~/redux/actions/report.action';
import Alertify from '~/services/alertify';
import DownloadReportModal from './fileDownload.modal';

interface ReconciliationModalProps {
    visible: boolean;
    onClose: () => void;
}

// Sample data (Modify according to your store values)
const data = [
    {
        key: '1',
        method: 'Check',
        sales: 7000,
        collected: 0,
        overUnder: -7000,
    },
    {
        key: '2',
        method: 'Visa',
        sales: 1700,
        collected: 0,
        overUnder: -1700,
    },
    {
        key: '3',
        method: 'Phone/Pe/UPI',
        sales: 9400,
        collected: 0,
        overUnder: -9400,
    },
];

const ReconciliationModal: React.FC<ReconciliationModalProps> = ({
    visible,
    onClose,
}) => {
    const [isDenominationVisible, setIsDenominationVisible] = useState(false);
    const [selectedLocation, setSelectedLocation] = useState(null);
    const [denominationData, setDenominationData] = useState<
        Record<number, number>
    >({});

    const [loader, startLoader, endLoader] = useLoader();
    const [zOutLoader, startZOutLoader, endZOutLoader] = useLoader();
  const [downloadLoader,startDownloadLoader,stopDownloadLoader] = useLoader();


    const [overallTotal, setOverallTotal] = useState(0);
    const [note, setNote] = useState<string>('');
    const [startingCash, setStartingCash] = useState(0);
    const [cashSales, setCashSales] = useState(0);
    const [pettyCash, setPettyCash] = useState(0);
    const [leaveCash, setLeaveCash] = useState(0);
    const [cashDeposit, setCashDeposit] = useState(0);
    const [drawerCash, setDrawerCash] = useState(0);
    const [cashOverUnder, setCashOverUnder] = useState(0);
    const [totalCash, setTotalCash] = useState(0);
    const [finalTotal, setFinalTotal] = useState(0);
    const [downlaodReportModal, setReportDownloadModal] = useState<boolean>(false)
    const [reconciliationId, setReconciliationId] = useState<string>('')
    const [form] = Form.useForm();
    const dispatch = useAppDispatch();

    const [salesData, setSalesData] = useState([]);

    const store = useAppSelector((state) => ({
        reconciliationData: state.purchased_store.reconciliationData,
        role: state.auth_store.role,
        user: state.auth_store.user,
        facilityList: state.facility_store.facilityList,
        facilityListByStaffId: state.facility_store.facilityListByStaffId,
    }));

    console.log('Store---------', store.reconciliationData);

    const LocationOptionByStaff = store.facilityListByStaffId?.map(
        (item: any) => ({
            value: item._id,
            label: item.name,
            id: item._id,
        })
    );

    const FacilityOptions = store.facilityList?.map((item: any) => ({
        value: item._id,
        label: item.facilityName,
        id: item._id,
    }));

    useEffect(() => {
        console.log(store.role,'role')
        if (visible) {
            if (store.role === RoleType.TRAINER)
                dispatch(GetFacilityListByStaffId({ staffId: store.user._id }))
                    .unwrap()
                    .then((res: any) => {
                        console.log(res,"res----->>>>>>>>>>>>>>>>>>------",res?.data?.data?.[0]?._id)
                        setSelectedLocation(res?.data?.data?.[0]?._id);
                        form.setFieldsValue({
                            location: res?.data?.data?.list?.[0]?._id,
                        });
                    });
            else
                dispatch(FacilitiesList({ page: 1, pageSize: 30 }))
                    .unwrap()
                    .then((res: any) => {
                        console.log('Res----------', res);
                        setSelectedLocation(res?.data?.data?.list?.[0]?._id);
                        form.setFieldsValue({
                            location: res?.data?.data?.list?.[0]?._id,
                        });
                    });
        }
    }, [visible]);

    useEffect(() => {
        setStartingCash(store.reconciliationData?.leaveAmount || 0);
        setCashSales(store.reconciliationData?.cashSalse || 0);
        if (store.reconciliationData?.createdAt) {
            const createdAt = dayjs(store.reconciliationData.createdAt);
            form.setFieldsValue({
                fromDate: createdAt,
                fromTime: createdAt,
                toDate: dayjs(),
                toTime: dayjs(),
            });
        }
    }, [store.reconciliationData]);

    useEffect(() => {
        console.log(store.reconciliationData?.cashSales)
        const cashSales = store.reconciliationData?.cashSales || 0;
        console.log(startingCash, cashSales, pettyCash, "sahdfakshfkhaskfh")
        const calculatedTotalCash = startingCash + cashSales - pettyCash;
        setTotalCash(calculatedTotalCash);

        // if (overallTotal === 0) {
        //     setDrawerCash(calculatedTotalCash);
        // }
    }, [startingCash, cashSales, pettyCash]);
    useEffect(() => {
        console.log(store.reconciliationData?.cashSales)
        const cashSales = store.reconciliationData?.cashSales || 0;
        console.log(startingCash, cashSales, pettyCash, "sfewwefwefwe")
        const calculatedTotalCash = startingCash + cashSales - pettyCash;
        setTotalCash(calculatedTotalCash);
        console.log(calculatedTotalCash, "calculatedTotalCash")
        // if (overallTotal === 0) {
        //     setDrawerCash(calculatedTotalCash);
        // }
    }, [store.reconciliationData?.cashSales]);

    useEffect(() => {
        const overUnder = drawerCash - totalCash
        console.log(overUnder, 'over under');
        setCashOverUnder(overUnder);
    }, [drawerCash, leaveCash, totalCash]);
    useEffect(() => {
        const overUnder = drawerCash - totalCash;

        setCashOverUnder(overUnder);
    }, []);
    const handleOpenDenomination = () => {
        setIsDenominationVisible(true);
    };

    const handleCloseDenomination = () => {
        setIsDenominationVisible(false);
        setDenominationData({});
    };

    const handleDenominationChange = (newData: Record<number, number>) => {

        const updatedData: Record<number, number> = { ...denominationData };

        for (const [key, value] of Object.entries(newData)) {
            const parsedKey = parseInt(key);
            updatedData[parsedKey] = (updatedData[parsedKey] || 0) + value;
        }

        setDenominationData(updatedData);

        const totalFromDenomination = Object.entries(updatedData).reduce(
            (total, [key, value]) => {
                return total + parseInt(key) * value;
            },
            0
        );

        console.log(totalFromDenomination, 'total from denomination');
        setDrawerCash(totalFromDenomination);
    };



    const handleCalculateDeposit = () => {
        console.log(cashDeposit, "cash deposit")
        const newCashOverUnder = drawerCash - leaveCash;
        console.log(newCashOverUnder, "new cash over under")
        // const newDrawerCash = drawerCash;
        // const actualDrawerCash = newDrawerCash + cashDeposit
        // console.log(actualDrawerCash, "actual drawer cash")
        // setD
        // const newOverUnder = newDrawerCash - leaveCash;
        setCashDeposit(newCashOverUnder);
        // const final = newOverUnder + cashDeposit;
        // setOverallTotal(actualDrawerCash);
        // setDrawerCash(actualDrawerCash);

        // setFinalTotal(final);
        // setCashDeposit(0);

    };

    const handleClose = () => {
        onClose();
        form.resetFields();
        setIsDenominationVisible(false);
        setDenominationData({});
        setOverallTotal(0);
        setStartingCash(0);
        setCashSales(0);
        setPettyCash(0);
        setLeaveCash(0);
        setCashDeposit(0);
        setDrawerCash(0);
        setCashOverUnder(0);
        setTotalCash(0);
        setFinalTotal(0);
        setNote('');
        setSalesData([]);
    };


    // Calculate final total for over/under adjustments
    // useEffect(() => {
    //     setFinalTotal(Math.abs(cashOverUnder));
    // }, [cashOverUnder]);

    useEffect(() => {
        console.log(1111111111111111, visible,selectedLocation)
        startLoader();
        if (visible && selectedLocation) {
            const payload: any = {
                facilityId: selectedLocation,
            };
            dispatch(GetReconsiliation(payload))
                .unwrap()
                .then(() => { })
                .finally(endLoader);
        }
    }, [visible, selectedLocation]);
    const downnLoadReport = (format: 'pdf' | 'xlsx') => {
        startDownloadLoader()
        dispatch(ZoutReportExport({
            facilityIds: [selectedLocation],
            responseType: format === 'pdf' ? 'pdf' : 'stream',
            zOutId: reconciliationId,
        }))
            .unwrap()
            .then((res: ArrayBuffer) => {
                const blob = new Blob([res], {
                    type: format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                const fileFormat = format === 'pdf' ? 'pdf' : 'xlsx'
                link.setAttribute('download', `z-out_${new Date().toISOString()}.${fileFormat}`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                Alertify.success('Z-Out report downloaded successfully!');
                setReportDownloadModal(false)
                window.URL.revokeObjectURL(url); // Clean up the URL object
                  handleClose();
            })
            .catch((error: any) => {
                console.error('Export failed:', error);
            })
            .finally(() => {
                stopDownloadLoader();
            });
    };

    const HandleZOut = () => {
        startZOutLoader();
        const transformedSales = salesData.map((item: any) => ({
            method: item.shortId,
            total: item.sales,
            collected: item.collected,
        }));

        const payload: any = {
            facilityId: selectedLocation,
            leaveAmount: leaveCash || 0,
            startingAmount: startingCash || 0,
            pettyAmount: pettyCash || 0,
            drawerAmount: drawerCash || 0,
            depositAmount: cashDeposit || 0,
            denominations: Object.entries(denominationData)?.map(
                ([value, count]) => ({
                    value: parseInt(value),
                    count: count,
                })
            ),
            cashSales: cashSales || 0,
            overUnder: cashOverUnder || 0,
            onlineOverUnder: finalTotal || 0,
            otherPayments: transformedSales,
        };
        if (note) {
            payload.notes = note;
        }
        dispatch(CreateReconsiliation(payload))
            .unwrap()
            .then((res: any) => {
                console.log('RAs-----------, res', res.data.data._id);
                const status = res?.payload?.status ?? res?.status;
                if (status === 200 || status === 201) {
                    console.log('Data Success');
                    setReportDownloadModal(true)
                    setReconciliationId(res.data.data._id)
                    // handleClose();

                }
            })
            .catch(() => { })
            .finally(endZOutLoader);

    };

    useEffect(() => {
        if (store.reconciliationData?.otherSales) {
            const formattedSales = store.reconciliationData?.otherSales?.map(
                (sale: any, index: number) => ({
                    key: `other-${index}`,
                    method: sale.method,
                    sales: sale.total,
                    collected: 0,
                    overUnder: 0 - Number(sale.total),
                    shortId: sale.shortId,
                })
            );

            setSalesData(formattedSales);
        }
    }, [store.reconciliationData]);

    const updateFinalTotal = () => {
        const totalOverUnder = salesData.reduce(
            (acc, item: any) => acc + item.overUnder,
            0
        );
        setFinalTotal(totalOverUnder);
    };

    const handleCollectedChange = (value: number, recordKey: string) => {
        setSalesData((prev: any) =>
            prev?.map((row: any) =>
                row.key === recordKey
                    ? { ...row, collected: value, overUnder: value - row.sales }
                    : row
            )
        );

        // updateFinalTotal();
    };

    useEffect(() => {
        updateFinalTotal();
    }, [salesData, cashOverUnder]);

    const columns = [
        {
            title: 'Method',
            dataIndex: 'method',
            key: 'method',
            truncate: true,
        },
        {
            title: 'Sales',
            width: 70,
            dataIndex: 'sales',
            key: 'sales',
            render: (text: number) => `₹ ${text}`,
        },
        {
            title: 'Collected',
            dataIndex: 'collected',
            key: 'collected',
            render: (text: number, record: any) => (
                <Input
                    type="text"
                    value={text}
                    onInput={(e: any) => {
                        e.target.value = e.target.value.replace(/[^0-9]/g, '');
                    }}
                    onChange={(e) =>
                        handleCollectedChange(
                            Number(e.target.value) || 0,
                            record.key
                        )
                    }
                    className="h-8 w-full"
                />
            ),
        },
        {
            title: 'Over/Under',
            dataIndex: 'overUnder',
            key: 'overUnder',
            align: 'center',
            render: (text: number) => (
                <span
                    className={
                        text < 0
                            ? 'text-center text-red-500'
                            : 'text-center text-green-500'
                    }
                >
                    ₹ {text}
                </span>
            ),
        },
    ];

    return (<>
        <Modal
            title="Z-0ut Register Reconciliation"
            open={visible}
            centered
            onCancel={handleClose}
            footer={false}
            className="w-[65%]"
        >
            {loader ? (
                <div className="min-h-[60vh]">
                    <FullLoader state={true} />
                </div>
            ) : (
                <>
                    <div className="flex   flex-row gap-3 ">
                        <div className="flex w-[55%] flex-col">
                            <p className="pb-3 text-xl text-[#455560]">
                                Z-Out Details
                            </p>
                            <div className="flex flex-col gap-2 rounded-xl border border-black border-opacity-20 px-3 pb-10 pt-3">
                                <p className="text-medium text-xl font-medium text-[#455560]">
                                    Register
                                </p>
                                {/* <p className="text-medium text-xl font-medium text-[#455560]">
                            Facility
                        </p> */}
                                <ConfigProvider
                                    theme={{
                                        components: {
                                            DatePicker: {
                                                cellWidth: 36,
                                            },
                                        },
                                    }}
                                >
                                    <Form
                                        form={form}
                                        initialValues={{ remember: true }}
                                        layout="horizontal"
                                    >
                                        <div className="flex flex-row items-center   ">
                                            <Paragraph className="ant-form-item-label mb-0 w-[15%]">
                                                <label>Facility</label>
                                            </Paragraph>
                                            <Form.Item
                                                // label={
                                                //     <p className="text-left">
                                                //         Facility
                                                //     </p>
                                                // }
                                                name="location"
                                                className="organizationFilter w-[85%]"
                                                rules={[
                                                    {
                                                        required: false,
                                                        message:
                                                            'Please select location',
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    showSearch
                                                    allowClear
                                                    // defaultValue={selectedLocation}
                                                    onChange={(value) => {
                                                        setSelectedLocation(
                                                            value
                                                        );
                                                    }}
                                                    placeholder="Select facility"
                                                    filterOption={(
                                                        input,
                                                        option
                                                    ) =>
                                                        String(
                                                            option?.label ?? ''
                                                        )
                                                            .toLowerCase()
                                                            .includes(
                                                                input.toLowerCase()
                                                            )
                                                    }
                                                    options={
                                                        store.role ===
                                                            RoleType.TRAINER
                                                            ? LocationOptionByStaff
                                                            : FacilityOptions
                                                    }
                                                />
                                            </Form.Item>
                                        </div>
                                        <div className="flex flex-row items-center  py-5 ">
                                            <Paragraph className="ant-form-item-label mb-0 w-[15%] ">
                                                <label>From</label>
                                            </Paragraph>
                                            <Form.Item
                                                name={'fromDate'}
                                                className="organizationFilter w-[42.5%] "
                                            >
                                                <DatePicker
                                                    // className="w-[100%]"
                                                    disabled
                                                    format="DD/MM/YYYY"
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                name={'fromTime'}
                                                label=""
                                                className=" organizationFilter w-[42.5%]"
                                            >
                                                <TimePicker
                                                    disabled
                                                    format={'HH:mm'}
                                                />
                                            </Form.Item>
                                        </div>
                                        <div className="flex flex-row items-center pb-5">
                                            <Paragraph className="ant-form-item-label mb-0 w-[15%] ">
                                                <label>To</label>
                                            </Paragraph>
                                            <Form.Item
                                                name={'toDate'}
                                                className="organizationFilter w-[42.5%] "
                                            >
                                                <DatePicker
                                                    disabled
                                                    format="DD/MM/YYYY"
                                                />
                                            </Form.Item>
                                            <Form.Item
                                                name={'toTime'}
                                                label=""
                                                className="organizationFilter w-[42.5%] "
                                            >
                                                <TimePicker
                                                    disabled
                                                    format={'HH:mm'}
                                                />
                                            </Form.Item>
                                        </div>
                                    </Form>
                                </ConfigProvider>
                                <p className="text-medium text-xl font-medium text-[#455560]">
                                    Cash Reconciliation
                                </p>
                                <div className="text-medium flex w-[100%] flex-row   ps-3 text-xl text-[#455560]">
                                    <p className="me-10 w-[30%] ">
                                        Starting Cash
                                    </p>
                                    <div className="w-[20%]  text-left">
                                        <p>
                                            ₹{' '}
                                            {
                                                store.reconciliationData
                                                    ?.leaveAmount
                                            }
                                        </p>
                                    </div>
                                    <div></div>
                                </div>
                                <div className="text-medium flex w-[100%] flex-row   ps-3 text-xl text-[#455560]">
                                    <p className="me-10 w-[30%] ">Cash Sales</p>
                                    <div className="w-[20%]  text-left">
                                        <p>
                                            ₹{' '}
                                            {
                                                store.reconciliationData
                                                    ?.cashSales
                                            }
                                        </p>
                                    </div>
                                    <div></div>
                                </div>
                                <div className="text-medium flex w-[100%] flex-row items-center border-b border-black border-opacity-20   pb-3 ps-3 text-xl text-[#455560]">
                                    <p className="me-10 w-[30%] ">
                                        Petty Cash Out (-)
                                    </p>
                                    <div className="flex items-center gap-1  text-left">
                                        ₹
                                        <Input
                                            type="text"
                                            className="h-10 w-[55%]"
                                            value={pettyCash}
                                            onChange={(e) =>
                                                setPettyCash(
                                                    Number(e.target.value) || 0
                                                )
                                            }
                                            onInput={(e: any) => {
                                                e.target.value =
                                                    e.target.value.replace(
                                                        /[^0-9]/g,
                                                        ''
                                                    );
                                            }}
                                        />
                                    </div>
                                    <div></div>
                                </div>
                                <div className="text-medium flex w-[100%] flex-row  pb-6 ps-3 text-xl text-[#455560]">
                                    <p className="me-10 w-[30%] ">Total</p>
                                    <div className="w-[20%]  text-left">
                                        <p>₹ {totalCash}</p>
                                    </div>
                                    <div></div>
                                </div>
                                <div className="text-medium flex w-[100%] flex-row items-center   ps-3 text-xl text-[#455560]">
                                    <p className="w-[30%]   ">Drawer Cash</p>
                                    <div className="w-[38%]  ps-10 text-left">
                                        <p className="">₹ {overallTotal}</p>
                                    </div>
                                    <div>
                                        <Button
                                            onClick={handleOpenDenomination}
                                            className="h-8 w-[70px] border border-[#1a3353] text-[#1a3353]"
                                        >
                                            Count
                                        </Button>
                                    </div>
                                </div>
                                <div className="text-medium flex w-[100%] flex-row   ps-3 text-xl text-[#455560]">
                                    <p className="me-10 w-[30%] ">Leave Cash</p>
                                    <div className="flex  items-center gap-1  text-left">
                                        ₹
                                        <Input
                                            type="text"
                                            className="h-10 w-[55%]"
                                            value={leaveCash}
                                            onChange={(e) =>
                                                setLeaveCash(
                                                    Number(e.target.value) || 0
                                                )
                                            }
                                            onInput={(e: any) => {
                                                e.target.value =
                                                    e.target.value.replace(
                                                        /[^0-9]/g,
                                                        ''
                                                    );
                                            }}
                                        />
                                    </div>
                                    <div></div>
                                </div>
                                <div className="text-medium flex w-[100%] flex-row  border-b border-black border-opacity-20 pb-3  ps-3 text-xl text-[#455560]">
                                    <p className="me-10 w-[30%] ">
                                        Cash Over/Under
                                    </p>
                                    <div className="w-[20%]  text-left">
                                        <p
                                            className={`font-medium ${cashOverUnder < 0
                                                ? 'text-red-500'
                                                : 'text-green-500'
                                                }`}
                                        >
                                            ₹ {cashOverUnder}
                                        </p>
                                    </div>
                                    <div></div>
                                </div>
                                <div className="text-medium flex w-[100%] flex-row ps-3  pt-5 text-xl text-[#455560]">
                                    <p className=" w-[30%] ">Cash Deposit</p>
                                    <div className="flex w-[38%] items-center gap-1  ps-10 text-left">
                                        ₹
                                        <Input
                                            type="text"
                                            value={cashDeposit}
                                            onChange={(e) =>
                                                setCashDeposit(
                                                    Number(e.target.value) || 0
                                                )
                                            }
                                            className="h-10 w-[65%]"
                                            onInput={(e: any) => {
                                                e.target.value =
                                                    e.target.value.replace(
                                                        /[^0-9]/g,
                                                        ''
                                                    );
                                            }}
                                            disabled
                                        />
                                    </div>
                                    <div>
                                        <Button
                                            onClick={handleCalculateDeposit}
                                            className="h-8 w-[70px] border border-[#1a3353] text-[#1a3353]"
                                        >
                                            Calculate
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="flex w-[45%] flex-col gap-2 p-3">
                            <p className="text-xl text-[#455560]">
                                For each payment method required click on the
                                highlighted Collected value and enter the actual
                                amount collected. Over/under adjustments will
                                automatically be created for the valves
                                indicated.
                            </p>
                            <div className=" w-[100%] rounded-3xl">
                                <Table
                                    columns={columns}
                                    dataSource={salesData}
                                    pagination={false}
                                    // bordered
                                    className="border border-gray-300"
                                />
                            </div>
                            {/* <div className="flex flex-row items-center justify-between gap-3 py-3">
                                <p className="text-medium text-xl  text-[#455560]">
                                    Notes
                                </p>
                                <Input
                                    value={note}
                                    onChange={(e) => setNote(e.target.value)}
                                    className="h-14"
                                />
                            </div> */}
                            <div className="text-medium flex w-[100%] flex-row justify-between  py-5 pe-10       text-xl text-[#455560]">
                                <p className="  font-medium ">
                                    Other Over/Under:
                                </p>

                                <p
                                    className={`font-medium ${finalTotal <= 0
                                        ? 'text-red-500'
                                        : 'text-green-500'
                                        }`}
                                >
                                    ₹ {finalTotal}
                                </p>
                                {/* <div className="flex flex-row items-center">
                                    <Checkbox className=" text-xl text-[#455560]">
                                        Create Over/Under Adjustments
                                    </Checkbox>
                                </div> */}
                            </div>
                        </div>
                    </div>
                    <div className="flex justify-end gap-5  ">
                        <Button
                            onClick={handleClose}
                            className="rounded-lg border border-[#1A3353]        px-10 "
                        >
                            Cancel
                        </Button>

                        <Button
                            loading={zOutLoader}
                            onClick={HandleZOut}
                            className="rounded-lg bg-purpleLight px-10  text-white"
                        >
                            Z-Out
                        </Button>
                    </div>
                </>
            )}

            <DenominationModal
                onDenominationChange={handleDenominationChange}
                visible={isDenominationVisible}
                onClose={handleCloseDenomination}
                setOverallTotal={setOverallTotal}
                overallTotal={overallTotal}
            />
        </Modal>
        <DownloadReportModal
            open={downlaodReportModal}
            loading={downloadLoader} 
            onCancel={() => {
                setReportDownloadModal(false);
                handleClose();
            }}
            onDownload={(format) => {
                downnLoadReport(format);
               
              
            }}
        />
    </>
    );
};

export default ReconciliationModal;
