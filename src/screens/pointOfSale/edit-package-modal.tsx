import { Button, Form, Input, Modal, Radio, Select } from 'antd';
import Paragraph from 'antd/es/typography/Paragraph';
import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import {
    CreateCustomPricingAPI,
    CustomPricingListAPI,
    CustomPricingUpdateAPI,
} from '~/redux/actions/pricing-actions';
import Alertify from '~/services/alertify';
import { getQueryParams } from '~/utils/getQueryParams';

const EditPackageModal = ({
    visible,
    handleClose,
    selectedFacilityId,
    isViewOnly,
    handleCustomCardClick = () => {},
    setSelectedData,
}: any) => {
    const [selectedValue, setSelectedValue] = useState('No');
    const [total, setTotal] = useState(0);
    const [submitLoader, startSubmitLoader, endSubmitLoader] = useLoader();
    const [loader, startLoader, endLoader] = useLoader();
    const [isEditable, setIsEditable] = useState(false);
    const [discountAmount, setDiscountAmount] = useState(0);
    const [posPrice, setPosPrice] = useState(0);

    const dispatch = useAppDispatch();
    const [_, setLocation] = useLocation();

    const [form] = Form.useForm();
    const store = useAppSelector((state) => ({
        customPackageDetails: state.pricing_store.customPricingDetails,
    }));

    const toggleEdit = () => {
        setIsEditable((prev) => !prev); // Toggle the state
    };

    const calculateTotal = (values: any) => {
        const unitPrice = Number(values.unitPrice || 0);
        const quantity = Number(values.quantity || 0);
        const discountType = values.discountType || 'Flat';
        const discountValue = Number(values.discountValue || 0);

        const subtotal = unitPrice * quantity;
        let discountAmountCalc = 0;

        if (discountType === 'Percentage') {
            discountAmountCalc = (subtotal * discountValue) / 100;
        } else {
            discountAmountCalc = discountValue;
        }

        const finalTotal = subtotal - discountAmountCalc;
        setTotal(finalTotal >= 0 ? finalTotal : 0);
        setDiscountAmount(discountAmountCalc >= 0 ? discountAmountCalc : 0);
        setPosPrice(unitPrice * quantity);
    };

    useEffect(() => {
        if (store.customPackageDetails) {
            form.setFieldsValue({
                description: store.customPackageDetails.name,
                quantity: store.customPackageDetails.quantity,
                unitPrice: store.customPackageDetails.unitPrice,
                discountType:
                    store.customPackageDetails.discount?.type || 'Flat',
                discountValue: store.customPackageDetails.discount?.value || 0,
                tax: store.customPackageDetails.tax || 0,
                hsn: store.customPackageDetails.hsnOrSacCode || '',
            });
            setSelectedValue(
                store.customPackageDetails.isTaxable ? 'Yes' : 'No'
            );
            calculateTotal({
                quantity: store.customPackageDetails.quantity,
                unitPrice: store.customPackageDetails.unitPrice,
                discountType:
                    store.customPackageDetails.discount?.type || 'Flat',
                discountValue: store.customPackageDetails.discount?.value || 0,
            });
        }
    }, [store.customPackageDetails, form]);

    useEffect(() => {
        form.resetFields();
        setSelectedValue('No');
        setTotal(0);
        const values = form.getFieldsValue();
        calculateTotal(values);
    }, [visible]);

    const createCustomPricing = async (values: any) => {
        const unitPrice = Number(values.unitPrice || 0);
        const quantity = Number(values.quantity || 0);
        const discountType = values.discountType || 'Flat';
        const discountValue = Number(values.discountValue || 0);

        let discountAmount = 0;
        const subtotal = unitPrice * quantity;

        if (discountType === 'Percentage') {
            discountAmount = (subtotal * discountValue) / 100;
        } else {
            discountAmount = discountValue;
        }

        const total = subtotal - discountAmount;

        const payload: any = {
            facilityId: selectedFacilityId,
            name: values.description,
            quantity: quantity,
            unitPrice: unitPrice,
            total: total >= 0 ? total : 0,
            discount: {
                type: discountType,
                value: discountValue,
            },
            isTaxable: selectedValue === 'Yes',
            tax: selectedValue === 'Yes' ? Number(values.tax || 0) : 0,
            hsnOrSacCode: selectedValue === 'Yes' ? values.hsn : '',
        };

        if (isViewOnly) {
            payload.packageId = store.customPackageDetails?._id;
        }

        try {
            startSubmitLoader();

            // If isViewOnly is true, dispatch CustomPricingUpdateAPI, else dispatch CreateCustomPricingAPI
            if (isViewOnly) {
                // Dispatch the update action if it's in view-only mode
                const res: any = await dispatch(
                    CustomPricingUpdateAPI(payload)
                );
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    handleClose();
                    const payload = {
                        facilityId: selectedFacilityId,
                    };
                    handleCustomCardClick(
                        {
                            ...store.customPackageDetails,
                            name: values.description,
                            quantity: values.quantity,
                            tax: selectedValue === 'Yes' ? values.tax : 0,
                        },
                        discountAmount,
                        posPrice
                    );
                    dispatch(CustomPricingListAPI(payload));
                }
            } else {
                // Dispatch the create action if it's editable
                const res: any = await dispatch(
                    CreateCustomPricingAPI(payload)
                );
                if (
                    res?.payload?.status === 200 ||
                    res?.payload?.status === 201
                ) {
                    handleCustomCardClick(
                        {
                            ...store.customPackageDetails,
                            name: values.description,
                            quantity: values.quantity,
                            tax: selectedValue === 'Yes' ? values.tax : 0,
                        },
                        discountAmount,
                        posPrice
                    );
                    handleClose();
                    const payload = {
                        facilityId: selectedFacilityId,
                    };

                    dispatch(CustomPricingListAPI(payload));
                }
            }

            setLocation('/point-of-sales');
        } catch (error) {
            console.error('Error creating or updating pricing:', error);
            Alertify.error(
                'Error creating or updating custom package. Please try again later.'
            );
        } finally {
            endSubmitLoader();
        }
    };

    const onValuesChange = (changedValues: any, allValues: any) => {
        if (changedValues?.isTaxable !== undefined) {
            setSelectedValue(changedValues.isTaxable);
        }

        calculateTotal(allValues);

        // Trigger revalidation when discount logic may depend on these fields
        if (
            changedValues.unitPrice !== undefined ||
            changedValues.quantity !== undefined ||
            changedValues.discountType !== undefined
        ) {
            form.validateFields(['discountValue']);
        }
    };

    const params = getQueryParams();
    const { view } = params;

    return (
        <Modal
            title={isViewOnly ? 'Edit Custom Package' : 'Create Custom Package'}
            open={visible}
            centered
            onCancel={handleClose}
            footer={false}
            className="w-[55%]"
        >
            <Form
                className="flex flex-col gap-8 pt-10 "
                name="PricingCustomPricing"
                layout="horizontal"
                size="large"
                form={form}
                initialValues={{
                    discountType: 'Flat',
                }}
                onFinish={createCustomPricing}
                autoComplete="off"
                onValuesChange={onValuesChange}
                // disabled={isViewOnly} // Disable all inputs when view === 'true'
            >
                {/* Description */}
                <div className="flex flex-row items-center">
                    <div className="w-[20%]">
                        <Paragraph className="ant-form-item-label mb-0">
                            <label>
                                Name
                                <span className="ms-1 text-2xl text-red-400">
                                    {' '}
                                    *
                                </span>
                            </label>
                        </Paragraph>
                    </div>
                    <Form.Item
                        name="description"
                        className="organizationFilter w-[80%]"
                        rules={[
                            {
                                required: true,
                                message: 'Please enter description',
                            },
                        ]}
                    >
                        <Input placeholder="Enter description" />
                    </Form.Item>
                </div>

                {/* Quantity */}
                <div className="flex flex-row items-center">
                    <div className="w-[20%]">
                        <Paragraph className="ant-form-item-label mb-0">
                            <label>
                                Quantity
                                <span className="ms-1 text-2xl text-red-400">
                                    {' '}
                                    *
                                </span>
                            </label>
                        </Paragraph>
                    </div>
                    <Form.Item
                        name="quantity"
                        className="organizationFilter w-[80%]"
                        rules={[
                            {
                                required: true,
                                message: 'Please enter quantity',
                            },
                        ]}
                    >
                        <Input
                            onInput={(e: any) => {
                                e.target.value = e.target.value.replace(
                                    /[^0-9]/g,
                                    ''
                                );
                            }}
                            type="text"
                            placeholder="Enter quantity"
                        />
                    </Form.Item>
                </div>

                {/* Unit Price */}
                <div className="flex flex-row items-center">
                    <div className={` w-[20%]`}>
                        <Paragraph className="ant-form-item-label mb-0">
                            <label>
                                Unit Price
                                <span className="ms-1 text-2xl text-red-400">
                                    {' '}
                                    *
                                </span>
                            </label>
                        </Paragraph>
                    </div>
                    <Form.Item
                        name="unitPrice"
                        className={`organizationFilter ${
                            isViewOnly ? 'w-[55%] ' : 'w-[80%]'
                        }`}
                        rules={[
                            {
                                required: true,
                                message: 'Please enter unit price',
                            },
                        ]}
                    >
                        <Input
                            disabled={isViewOnly === true && !isEditable} // Disable the input if not editable}
                            type="text"
                            className="w-full"
                            onInput={(e: any) => {
                                e.target.value = e.target.value.replace(
                                    /[^0-9]/g,
                                    ''
                                );
                            }}
                            placeholder="Enter unit price"
                        />
                    </Form.Item>
                    {isViewOnly === true && (
                        <Button
                            onClick={toggleEdit} // Toggle the editable state when clicked
                            className="ml-4 h-12 bg-purpleLight text-lg text-white"
                        >
                            Change
                        </Button>
                    )}
                </div>

                {/* Discount */}
                <div className="flex flex-row items-center">
                    <div className="w-[20%]">
                        <Paragraph className="ant-form-item-label mb-0">
                            <label>
                                Discount
                                <span className="ms-1 text-2xl text-red-400">
                                    {' '}
                                    *
                                </span>
                            </label>
                        </Paragraph>
                    </div>
                    <div className="flex w-[80%] items-center gap-4">
                        <Form.Item name="discountType" className="w-[40%]">
                            <Select
                                placeholder="Select Discount Type"
                                options={[
                                    { label: 'Flat', value: 'Flat' },
                                    {
                                        label: 'Percentage',
                                        value: 'Percentage',
                                    },
                                ]}
                            />
                        </Form.Item>
                        <Form.Item
                            name="discountValue"
                            className="w-[50%]"
                            rules={[
                                // {
                                //     required: true,
                                //     message: 'Please enter discount value',
                                // },
                                {
                                    validator: (_, value) => {
                                        const unitPrice = Number(
                                            form.getFieldValue('unitPrice') || 0
                                        );
                                        const quantity = Number(
                                            form.getFieldValue('quantity') || 0
                                        );
                                        const discountType =
                                            form.getFieldValue(
                                                'discountType'
                                            ) || 'Flat';
                                        const subtotal = unitPrice * quantity;
                                        const numericValue = Number(value);

                                        if (discountType === 'Flat') {
                                            if (numericValue > subtotal) {
                                                return Promise.reject(
                                                    new Error(
                                                        'Flat discount cannot exceed subtotal'
                                                    )
                                                );
                                            }
                                        } else if (
                                            discountType === 'Percentage'
                                        ) {
                                            if (numericValue > 100) {
                                                return Promise.reject(
                                                    new Error(
                                                        'Percentage discount cannot exceed 100%'
                                                    )
                                                );
                                            }
                                        }

                                        return Promise.resolve();
                                    },
                                },
                            ]}
                        >
                            <Input
                                type="text"
                                placeholder="Enter discount value"
                                onInput={(e: any) => {
                                    // Allow only digits and one decimal point
                                    e.target.value = e.target.value.replace(
                                        /[^0-9.]/g,
                                        ''
                                    );

                                    const parts = e.target.value.split('.');
                                    if (parts.length > 2) {
                                        e.target.value =
                                            parts[0] + '.' + parts[1];
                                    }
                                }}
                            />
                        </Form.Item>
                    </div>
                </div>

                {/* <div className="flex flex-row items-center">
                    <div className="w-[20%]">
                        <Paragraph className="ant-form-item-label mb-0">
                            <label>Discounted Value</label>
                        </Paragraph>
                    </div>
                    <p className="text-xl font-semibold text-[#1a3353]">
                        {posPrice.toFixed(2)}
                    </p>
                </div> */}

                {/* Total */}
                <div className="flex flex-row items-center">
                    <div className="w-[20%]">
                        <Paragraph className="ant-form-item-label mb-0">
                            <label>
                                Total
                                <span className="ms-1 text-2xl text-red-400">
                                    {' '}
                                    *
                                </span>
                            </label>
                        </Paragraph>
                    </div>
                    <p className="text-xl font-semibold text-[#1a3353]">
                        {total.toFixed(2)}
                    </p>
                </div>

                {/* Taxable */}
                <div className="flex flex-row items-center">
                    <div className="w-[20%]">
                        <Paragraph className="ant-form-item-label mb-0">
                            <label>
                                Taxable
                                <span className="ms-1 text-2xl text-red-400">
                                    {' '}
                                    *
                                </span>
                            </label>
                        </Paragraph>
                    </div>
                    <Form.Item className="organizationFilter">
                        <Radio.Group
                            value={selectedValue}
                            onChange={(e) => setSelectedValue(e.target.value)}
                        >
                            <Radio value="Yes"> Yes </Radio>
                            <Radio value="No"> No </Radio>
                        </Radio.Group>
                    </Form.Item>
                </div>

                {/* Tax and HSN if taxable */}
                {selectedValue === 'Yes' && (
                    <>
                        <div className="flex flex-row items-center">
                            <div className="w-[20%]">
                                <Paragraph className="ant-form-item-label mb-0">
                                    <label>
                                        Tax
                                        <span className="ms-1 text-2xl text-red-400">
                                            {' '}
                                            *
                                        </span>
                                    </label>
                                </Paragraph>
                            </div>
                            <Form.Item
                                name="tax"
                                className="organizationFilter w-[80%]"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please select tax!',
                                    },
                                ]}
                            >
                                <Radio.Group
                                    value={store.customPackageDetails?.tax || 0} // Set the tax value dynamically
                                    onChange={(e) =>
                                        form.setFieldsValue({
                                            tax: e.target.value,
                                        })
                                    }
                                >
                                    <Radio
                                        className="text-[#1a3353]"
                                        value={18}
                                    >
                                        GST 18%
                                    </Radio>
                                    <Radio
                                        className="text-[#1a3353]"
                                        value={12}
                                    >
                                        GST 12%
                                    </Radio>
                                    <Radio className="text-[#1a3353]" value={5}>
                                        GST 5%
                                    </Radio>
                                </Radio.Group>
                            </Form.Item>
                        </div>

                        <div className="flex flex-row items-center">
                            <div className="w-[20%]">
                                <Paragraph className="ant-form-item-label mb-0">
                                    <label>
                                        HSN / SAC
                                        <span className="ms-1 text-2xl text-red-400">
                                            {' '}
                                            *
                                        </span>
                                    </label>
                                </Paragraph>
                            </div>
                            <Form.Item
                                name="hsn"
                                className="organizationFilter w-[80%]"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Please enter HSN/SAC code',
                                    },
                                ]}
                            >
                                <Input placeholder="Enter HSN/SAC" />
                            </Form.Item>
                        </div>
                    </>
                )}

                {/* Buttons */}
                <div className="flex w-[85%] flex-row gap-5 lg:justify-end @sm:justify-center">
                    <Form.Item>
                        <Button
                            onClick={() => {
                                handleClose();
                                setIsEditable(false); // Reset the editable state when closing the modal
                            }}
                            className="h-12 w-32 border border-[#1A3353] text-xl"
                        >
                            Cancel
                        </Button>
                    </Form.Item>
                    <Form.Item>
                        <Button
                            htmlType="submit"
                            loading={submitLoader}
                            disabled={loader}
                            onClick={() => {
                                setIsEditable(false);
                            }}
                            className="h-12 w-32 bg-purpleLight text-xl text-white"
                        >
                            Ok
                        </Button>
                    </Form.Item>
                </div>
            </Form>
        </Modal>
    );
};

export default EditPackageModal;
function handleCustomCardClick(customPackageDetails: any) {
    throw new Error('Function not implemented.');
}
