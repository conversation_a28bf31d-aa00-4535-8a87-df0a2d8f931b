import { MoreOutlined } from '@ant-design/icons';
import { Menu, Dropdown, Pagination, Tag, Tooltip, Modal } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { render } from 'react-dom';
import { Link, useLocation } from 'wouter';
import { navigate } from 'wouter/use-location';
import CustomTable from '~/components/common/customTable';
import FullLoader from '~/components/library/loader/full-loader';
import CommonConfirmationModal from '~/components/modals/common-confirmation-modal';
import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { FacilitiesList } from '~/redux/actions/facility-action';
import {
    BookedSchedulingList,
    BookedSchedulingListV1,
    CancelScheduling,
    CheckInScheduling,
    PurchasePricingPackagesList,
} from '~/redux/actions/scheduling-action';
import { ServiceCategoryList } from '~/redux/actions/serviceCategoryAction';
import BookingModal from '~/screens/appointment/booking-modal';
import {
    ClassType,
    PERMISSIONS_ENUM,
    RoleType,
    SUBJECT_TYPE,
} from '~/types/enums';
import { formatDate } from '~/utils/formatDate';
import { getQueryParams } from '~/utils/getQueryParams';
import BookingSuspensionModal from './bookingSuspension-modal';
import SharePassModal from '~/screens/customers/share-pass-modal';
import Alertify from '~/services/alertify';
import { GetSettingActiveStatus } from '~/redux/actions/organization-action';
import { useSelector } from 'react-redux';
import { GetSettings } from '~/redux/actions/settings-actions';
import ClientBookingTable from '~/components/gym/client-booking';
import ClientBookingCheckInModal from '~/components/common/client-book-check-modal';
import SharePassTypeModal from '../customers/share-pass-type-modal';

function getStatus(record: any) {
    const now = new Date();
    const startDate = new Date(record.startDate);
    const endDate = new Date(record.endDate);
    if (now < startDate || now > endDate) {
        return 'Inactive';
    }

    if (record.suspensions && record.suspensions.length > 0) {
        const isCurrentlySuspended = record.suspensions.some(
            (suspension: any) => {
                const fromDate = new Date(suspension.fromDate);
                const suspensionEndDate = new Date(suspension.endDate);
                return now >= fromDate && now <= suspensionEndDate;
            }
        );

        if (isCurrentlySuspended) return 'Suspended';
    }
    if (record.remainingSessions < 1) return 'Inactive';
    if (!record.isActive) return 'Inactive';

    return 'Active';
}

const toCamelCase = (str: string): string => {
    return str
        .toLowerCase()
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
};

const columns = [
    {
        title: 'Customer ID',
        dataIndex: 'customerId',
        hidden: true,
        // onCell: () => ({ style: { minWidth: 120, width: 120 } }),
    },
    {
        title: 'Client Name',
        dataIndex: 'clientName',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },
    {
        title: 'Membership ID',
        dataIndex: 'membershipId',
        align: 'center',
        render: (membershipId: string) => {
            return membershipId && membershipId.trim() !== '' ? (
                membershipId
            ) : (
                <div className="text-center">-</div>
            );
        },
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },
    {
        title: 'Package Name',
        dataIndex: 'packageName',
        ellipses: true,
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
    },
    {
        title: 'Date',
        dataIndex: '',
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
        render: (record: any) => {
            return formatDate(record?.date);
        },
    },
    {
        title: 'Time',
        dataIndex: 'createdAt',
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
        render: (text: any) => {
            const time = dayjs(text).format('hh:mm A');
            return time;
        },
    },
    {
        title: 'Phone Number',
        dataIndex: 'mobile',
        // width: 80,
        // ellipses: true,
    },
    {
        title: 'Remaining Sessions',
        dataIndex: 'remainingSessions',
        align: 'center',
        render: (text: any, record: any) => {
            const status = getStatus(record);

            if (status === 'Inactive') return '0';

            if (
                typeof text === 'string' &&
                text.toLowerCase().includes('day pass')
            ) {
                return toCamelCase(text); // Use camel case instead of uppercase
            }

            if (text === 9999) {
                return 'Unlimited';
            }

            return text;
        },
    },

    {
        title: 'Location',
        dataIndex: 'location',
        // onCell: () => ({ style: { minWidth: 180, width: 180 } }),
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategory',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },
    {
        title: 'Status',
        dataIndex: '',
        render: (record: any) => {
            const status = getStatus(record);
            const statusColors = {
                Active: 'green',
                Inactive: 'purple',
                Suspended: 'red',
            };

            if (status === 'Suspended' && record.suspensions.length > 0) {
                return (
                    <Tooltip title={`${record.suspensions[0].notes}`}>
                        <Tag color={statusColors[status]}>{status}</Tag>
                    </Tooltip>
                );
            }

            return <Tag color={statusColors[status]}>{status}</Tag>;
        },
    },
    // {
    //     title: 'Payment Status',
    //     dataIndex: 'paymentStatus',
    //     // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    // },
];

const bookingStatus: any = {
    booked: 'Booked',
    'checked-in': 'Checked In',
    canceled: 'Canceled',
};

const columns2 = [
    // {
    //     title: 'Customer ID',
    //     dataIndex: 'customerId',
    //     visible: false,
    //     // onCell: () => ({ style: { minWidth: 120, width: 120 } }),
    // },
    {
        title: 'Client Name',
        dataIndex: 'clientName',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },
    {
        title: 'Package Name',
        dataIndex: 'packageName',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },
    {
        title: 'Date',
        dataIndex: 'date',
        // width: 150,
    },
    {
        title: 'Start Time',
        dataIndex: 'startTime',
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
    },
    {
        title: 'End Time',
        dataIndex: 'endTime',
        // onCell: () => ({ style: { minWidth: 100, width: 100 } }),
    },
    {
        title: 'Phone',
        dataIndex: 'clientPhone',
        // width: 250,
    },
    {
        title: 'Location',
        dataIndex: 'location',
        // onCell: () => ({ style: { minWidth: 120, width: 120 } }),
    },
    {
        title: 'Service Category',
        dataIndex: 'serviceCategory',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    },

    {
        title: 'Booking Status',
        dataIndex: 'bookingStatus',
        // onCell: () => ({ style: { minWidth: 200, width: 200 } }),
        render: (text: any) => {
            const statusClass =
                text === 'booked'
                    ? 'bg-green-100 bg-opacity-50 w-fit text-green-500 py-1 px-3 rounded'
                    : text === 'checked-in'
                    ? 'bg-purple-100 text-primary py-1 w-fit px-3 rounded'
                    : text === 'canceled'
                    ? 'bg-red-100 text-primary py-1 w-fit px-3 rounded'
                    : 'py-1 px-3';
            return <div className={statusClass}>{bookingStatus[text]}</div>;
        },
    },
    // {
    //     title: 'Payment Status',
    //     dataIndex: 'paymentStatus',
    //     onCell: () => ({ style: { minWidth: 200, width: 200 } }),
    // },
];

const BookingListing: React.FC = () => {
    const [addBookingModal, setAddBookingModal] = useState(false);
    const [activeKey, setActiveKey] = useState(1);
    const [openDropdownKey, setOpenDropdownKey] = useState<number | null>(null);
    const [selectedClients, setSelectedClients] = useState([]);
    const [selectedDateRange, setSelectedDateRange] = useState([]);
    const [selectedBookingStatus, setSelectedBookingStatus] = useState([]);
    const [isCheckIn, setIsCheckIn] = useState(false);
    const [scheduleData, setScheduleData] = useState<any>({});
    const [selectedServiceCategories, setSelectedServiceCategories] = useState(
        []
    );
    const [selectedCity, setSelectedCity] = useState([]);
    const [loader, startLoader, endLoader] = useLoader(true);
    const [filterLoader, startFilterLoader, endFilterLoader] = useLoader(false);
    const [_, setLocation] = useLocation();
    const params = getQueryParams();
    const [search, setSearch] = useState(params.search);
    const [confirmationModalVisible, setConfirmationModalVisible] =
        useState(false);
    const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
    const [isSuspensionModalVisible, setIsSuspensionModalVisible] =
        useState<boolean>(false);
    const [isSharePassModalVisible, setIsSharePassModalVisible] =
        useState<boolean>(false);

    const [openClientBookingModal, setOpenClientBookingModal] = useState(false);
    const [clientId, setClientId] = useState<string>('');

    const store = useAppSelector((state) => ({
        schedulingList: state.scheduling_store.schedulingListv1,
        schedulingCount: state.scheduling_store.schedulingCountV1,
        purchasedPriceList: state.scheduling_store.purchasePricingList,
        purchasedPriceListCount:
            state.scheduling_store.purchasePricingListCount,
        clientOnboarding: state.settings_store.clientOnboarding,
        role: state.auth_store.role,
    }));

    const dispatch = useAppDispatch();

    const pageParam = Number(params.page);
    const pageSizeParam = Number(params.pageSize);

    const [currentPage, setCurrentPage] = useState(
        !isNaN(pageParam) ? pageParam : 1
    );
    const [pageSizes, setPageSize] = useState(
        !isNaN(pageSizeParam) ? pageSizeParam : 10
    );

    // for share pass option(should be visible or not)
    const [sharePassOption, setSharePassOption] = useState(false);
    const [sharePassEnabled, setSharePassEnabled] = useState(false);
    const debouncedRequest = useDebounce((callback) => callback(), 300);
    const { all_permissions_for_role } = useSelector(
        (state: any) => state.permission_store
    );
    const hasBookingWritePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_SCHEDULE_BOOKING
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingReadPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_SCHEDULE_READ
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingUpdatePermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_UPDATE_BOOKING
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingCheckInPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_CHECKIN
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const hasBookingCancelPermission = useMemo(() => {
        return all_permissions_for_role?.some((module: any) =>
            module.subjects?.some(
                (subject: any) =>
                    subject.type === SUBJECT_TYPE.SCHEDULING_BOOKING &&
                    subject.actions?.some((action: any) =>
                        action.permissions?.some(
                            (permission: any) =>
                                permission.type ===
                                PERMISSIONS_ENUM.SCHEDULING_CANCEL
                        )
                    )
            )
        );
    }, [all_permissions_for_role]);
    const handleClose = () => {
        dispatch(
            BookedSchedulingListV1({
                page: 1,
                search: search || undefined,
                pageSize: 10,
                facilityId: selectedCity,
                serviceCategory: selectedServiceCategories,
                startDate: selectedDateRange[0],
                endDate: selectedDateRange[1],
                classType: 'bookings',
            })
        );
        setIsModalVisible(false);
        setScheduleData(null);
        setIsSuspensionModalVisible(false);
    };

    const getList = () => {
        if (activeKey === 2) {
            if (
                hasBookingReadPermission ||
                store.role === RoleType.ORGANIZATION
            ) {
                debouncedRequest(() => {
                    dispatch(
                        BookedSchedulingListV1({
                            page: currentPage,
                            search: search || undefined,
                            pageSize: pageSizes,
                            facilityId: selectedCity,
                            serviceCategory: selectedServiceCategories,
                            ...(selectedBookingStatus.length && {
                                scheduleStatus: selectedBookingStatus,
                            }),
                            startDate: selectedDateRange[0],
                            endDate: selectedDateRange[1],
                            classType: 'bookings',
                        })
                    )
                        .unwrap()
                        .then(() => {})
                        .finally(endFilterLoader);
                });
            } else {
                Alertify.error(
                    "Sorry, you don't have the necessary permissions to perform this action"
                );
            }
        } else if (activeKey === 1) {
            const payload = {
                page: currentPage,
                pageSize: pageSizes,
                ...(search && { search: search }),
                ...(selectedCity?.length > 0 && {
                    facilityIds: selectedCity,
                }),
                ...(selectedServiceCategories?.length > 0 && {
                    serviceCategoryIds: selectedServiceCategories,
                }),
                ...(selectedDateRange?.length > 0 && {
                    startDate: selectedDateRange[0],
                    endDate: selectedDateRange[1],
                }),
            };
            debouncedRequest(() => {
                if (
                    hasBookingReadPermission ||
                    store.role === RoleType.ORGANIZATION
                ) {
                    dispatch(
                        PurchasePricingPackagesList({
                            reqData: payload,
                            classType: 'bookings',
                        })
                    )
                        .unwrap()
                        .then(() => {})
                        .finally(endFilterLoader);
                } else {
                    Alertify.error(
                        "Sorry, you don't have the necessary permissions to perform this action"
                    );
                }
            });
        }
    };

    useEffect(() => {
        if (
            search ||
            currentPage > 1 ||
            selectedCity.length ||
            selectedServiceCategories.length ||
            (selectedDateRange[0] && selectedDateRange[1]) ||
            selectedBookingStatus.length
        )
            startFilterLoader();
        getList();
        endLoader();
    }, [
        search,
        currentPage,
        pageSizes,
        selectedCity,
        selectedServiceCategories,
        selectedDateRange,
        activeKey,
        selectedBookingStatus,
        isSuspensionModalVisible,
    ]);

    useEffect(() => {
        dispatch(FacilitiesList({ page: 1, pageSize: 10 })).unwrap();
        dispatch(GetSettings({})).unwrap();
        dispatch(
            ServiceCategoryList({
                page: 1,
                pageSize: 10,
                classType: 'bookings',
            })
        ).unwrap();
        dispatch(
            GetSettingActiveStatus({
                settingKey: 'subsettings_class_scheduling_sharepass',
            })
        )
            .unwrap()
            .then((response: any) => {
                // console.log(
                //     'The status response is::::::',
                //     response?.data?.data?.isActive
                // );
                setSharePassOption(response?.data?.data?.isActive);
                setSharePassEnabled(response?.data?.data?.isEnabled);
            })
            .catch((error: any) =>
                Alertify.error('Error in fetching setting status')
            );
    }, []);

    function paginate(page: number, pageSize: number) {
        setCurrentPage(page);
        setPageSize(pageSize);
        navigate(`?page=${page}&pageSize=${pageSize}`, { replace: true });
    }

    function handleSearch(value: string) {
        setSearch(value);
        setCurrentPage(pageParam || 1);
        setPageSize(pageSizeParam || 10);
    }

    const openConfirmationModal = (record: any, isCheckIn: boolean = false) => {
        setIsCheckIn(isCheckIn);
        setScheduleData(record);
        setConfirmationModalVisible(true);
    };

    const handleSharePassClose = (refresh = false) => {
        if (refresh) getList();
        setIsSharePassModalVisible(false);
    };

    const actionColumn = [
        ...(hasBookingUpdatePermission || store.role === RoleType.ORGANIZATION
            ? [
                  {
                      title: 'Action',
                      dataIndex: '',
                      key: 'action',
                      render: (record: any) => {
                          const isDisabled = getStatus(record) === 'Inactive';
                          const menu = (
                              <Menu>
                                  {(hasBookingWritePermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item
                                          className={` ${
                                              isDisabled
                                                  ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                  : 'text-[#455560]'
                                          }`}
                                          onClick={() => {
                                              setScheduleData({
                                                  ...record,
                                                  isEdit: false,
                                              });
                                              setIsModalVisible(true);
                                          }}
                                          key="1"
                                          disabled={isDisabled}
                                      >
                                          Add Booking
                                      </Menu.Item>
                                  )}
                                  {!record.isSuspended ? (
                                      <>
                                          <Menu.Item
                                              className={` ${
                                                  isDisabled
                                                      ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                      : 'text-[#455560]'
                                              }`}
                                              onClick={() => {
                                                  setScheduleData({
                                                      ...record,
                                                      isEdit: false,
                                                  });
                                                  setIsSuspensionModalVisible(
                                                      true
                                                  );
                                              }}
                                              key="2"
                                              disabled={isDisabled}
                                          >
                                              Suspension
                                          </Menu.Item>
                                      </>
                                  ) : (
                                      <>
                                          <Menu.Item
                                              className={` ${
                                                  isDisabled
                                                      ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                      : 'text-[#455560]'
                                              }`}
                                              onClick={() => {
                                                  setScheduleData({
                                                      ...record,
                                                      isEdit: false,
                                                  });
                                                  setIsSuspensionModalVisible(
                                                      true
                                                  );
                                              }}
                                              key="3"
                                              disabled={isDisabled}
                                          >
                                              Resume Suspension
                                          </Menu.Item>
                                      </>
                                  )}
                                  {/* {sharePassOption && sharePassEnabled && ( */}
                                  {sharePassEnabled &&
                                      store.clientOnboarding?.sharePass && (
                                          <Menu.Item
                                              className={` ${
                                                  isDisabled
                                                      ? 'cursor-not-allowed bg-[#f5f5f5]'
                                                      : 'text-[#455560]'
                                              }`}
                                              onClick={() => {
                                                  setScheduleData({
                                                      ...record,
                                                  });
                                                  if (
                                                      ![
                                                          'multiple',
                                                          'day_pass',
                                                      ].includes(
                                                          record.sessionType
                                                      )
                                                  )
                                                      Alertify.error(
                                                          'Share pass can only be done when session type is multiple'
                                                      );
                                                  else
                                                      setIsSharePassModalVisible(
                                                          true
                                                      );
                                              }}
                                              key="share-pass"
                                              disabled={isDisabled}
                                          >
                                              Share Pass
                                          </Menu.Item>
                                      )}
                                  <Menu.Item
                                      className={`text-[#455560]`}
                                      key="check-in"
                                      //   disabled={isDisabled}
                                      onClick={() => {
                                          setOpenClientBookingModal(true);
                                          setClientId(record.clientId);
                                      }}
                                  >
                                      Check-In History
                                  </Menu.Item>
                              </Menu>
                          );

                          return (
                              <Dropdown
                                  overlay={menu}
                                  trigger={['click']}
                                  visible={openDropdownKey === record.key}
                                  onOpenChange={(visible) => {
                                      setOpenDropdownKey(
                                          visible ? record.key : null
                                      );
                                  }}
                              >
                                  <MoreOutlined
                                      style={{
                                          fontSize: '20px',
                                          cursor: 'pointer',
                                      }}
                                  />
                              </Dropdown>
                          );
                      },
                  },
              ]
            : []),
    ];

    const actionColumn2 = [
        ...(hasBookingUpdatePermission ||
        hasBookingCheckInPermission ||
        hasBookingCancelPermission ||
        store.role === RoleType.ORGANIZATION
            ? [
                  {
                      title: 'Action',
                      dataIndex: '',
                      key: 'action',
                      render: (record: any) => {
                          const menu = (
                              <Menu>
                                  {(hasBookingUpdatePermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item
                                          onClick={() => {
                                              setScheduleData({
                                                  ...record,
                                                  isEdit: true,
                                              });
                                              setIsModalVisible(true);
                                          }}
                                          key="1"
                                      >
                                          Edit
                                      </Menu.Item>
                                  )}
                                  {(hasBookingCheckInPermission ||
                                      store.role === RoleType.ORGANIZATION) &&
                                      record.bookingStatus !== 'checked-in' && (
                                          <Menu.Item
                                              onClick={() =>
                                                  openConfirmationModal(
                                                      record,
                                                      true
                                                  )
                                              }
                                              key="4"
                                          >
                                              Check-In
                                          </Menu.Item>
                                      )}
                                  {(hasBookingCancelPermission ||
                                      store.role === RoleType.ORGANIZATION) && (
                                      <Menu.Item
                                          onClick={() =>
                                              openConfirmationModal(record)
                                          }
                                          key="5"
                                      >
                                          Cancel
                                      </Menu.Item>
                                  )}
                              </Menu>
                          );

                          return (
                              <Dropdown
                                  overlay={menu}
                                  trigger={['click']}
                                  visible={openDropdownKey === record.key}
                                  onOpenChange={(visible) => {
                                      setOpenDropdownKey(
                                          visible ? record.key : null
                                      );
                                  }}
                              >
                                  <MoreOutlined
                                      style={{
                                          fontSize: '20px',
                                          cursor: 'pointer',
                                      }}
                                  />
                              </Dropdown>
                          );
                      },
                  },
              ]
            : []),
    ];

    const combinedColumns = [...columns, ...actionColumn];
    const combinedColumns2 = [...columns2, ...actionColumn2];

    console.log('scheduleData--------', scheduleData);

    return loader ? (
        <FullLoader state={true} />
    ) : (
        <>
            <div className="">
                <CustomTable
                    className="min-w-min"
                    columns={combinedColumns}
                    columns2={combinedColumns2}
                    dataSource1={store.purchasedPriceList?.map((item: any) => ({
                        ...item,
                        membershipId: item.membershipId || '-',
                    }))}
                    dataSource2={store.schedulingList}
                    // addNewModal={true}
                    // openModal={() => setAddBookingModal(true)}
                    // addNewTitle="Create Booking"
                    hasBookingWritePermission={hasBookingWritePermission}
                    heading="Booking"
                    onSearch={handleSearch}
                    showBooking={true}
                    showPackages={true}
                    showDateRange={true}
                    loading={filterLoader}
                    search={search}
                    showStaffLocation={true}
                    showClient={true}
                    showServiceCategory={true}
                    showBookingStatus={true}
                    showSearch={true}
                    showTabs={true}
                    showClearButton={true}
                    {...{
                        selectedCity,
                        setSelectedCity,
                        selectedClients,
                        setSelectedClients,
                        selectedBookingStatus,
                        setSelectedBookingStatus,
                        selectedServiceCategories,
                        setSelectedServiceCategories,
                        selectedDateRange,
                        setSelectedDateRange,
                        setIsModalVisible,
                        setActiveKey,
                        activeKey,
                    }}
                />
                <div className="b flex justify-center py-10">
                    <Pagination
                        current={currentPage}
                        total={
                            activeKey === 1
                                ? store.purchasedPriceListCount
                                : store.schedulingCount
                        }
                        pageSize={pageSizes}
                        pageSizeOptions={['10', '20', '50']}
                        onChange={paginate}
                        hideOnSinglePage
                    />
                </div>
            </div>
            {confirmationModalVisible && (
                <CommonConfirmationModal
                    visible={confirmationModalVisible}
                    onConfirm={() => {
                        isCheckIn
                            ? dispatch(
                                  CheckInScheduling({
                                      scheduleId: scheduleData._id,
                                  })
                              )
                            : dispatch(
                                  CancelScheduling({
                                      scheduleId: scheduleData._id,
                                  })
                              );
                        setIsCheckIn(false);
                        setConfirmationModalVisible(false);
                        setScheduleData(null);
                    }}
                    onCancel={() => {
                        setIsCheckIn(false);
                        setConfirmationModalVisible(false);
                        setScheduleData(null);
                    }}
                    message={
                        isCheckIn
                            ? 'Are you sure you want to check-in?'
                            : 'Are you sure you want to cancel the booking?'
                    }
                />
            )}
            {isModalVisible && (
                <BookingModal
                    visible={isModalVisible}
                    onClose={handleClose}
                    tabValue={'bookings'}
                    scheduleId={scheduleData?.isEdit ? scheduleData?._id : null}
                    clientId={scheduleData?.clientId}
                    facilityId={scheduleData?.facilityId}
                    purchaseId={activeKey === 1 ? scheduleData?._id : null}
                    isEdit={scheduleData?.isEdit || false}
                />
            )}
            {isSuspensionModalVisible && (
                <BookingSuspensionModal
                    visible={isSuspensionModalVisible}
                    onClose={handleClose}
                    purchaseId={scheduleData?._id}
                    purchaseData={scheduleData}
                />
            )}
            {openClientBookingModal && (
                <Modal
                    open={openClientBookingModal}
                    onCancel={() => setOpenClientBookingModal(false)}
                    footer={null}
                    width={900}
                    title="Bookings & Check-In History"
                >
                    <ClientBookingCheckInModal clientId={clientId} />
                </Modal>
            )}

            {isSharePassModalVisible && (
                <SharePassTypeModal
                    visible={isSharePassModalVisible}
                    onClose={handleSharePassClose}
                    clientId={scheduleData?.clientId}
                    clientName={scheduleData?.clientName}
                    purchaseId={scheduleData?._id}
                />
            )}
        </>
    );
};

export default BookingListing;
