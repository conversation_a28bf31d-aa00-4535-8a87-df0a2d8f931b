import React, { useEffect, useState } from 'react';
import {
    startOfWeek,
    addDays,
    format,
    isSameDay,
    subWeeks,
    addWeeks,
} from 'date-fns';
import { <PERSON><PERSON>, Spin } from 'antd';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { getTrainerAvailability } from '~/redux/actions/widget/widget.action';
import { useLoader } from '~/hooks/useLoader';
import TrialBookingFlow from './TrialBookingFLow/TrialBookingFLow';


const PersonalAppointmentTab = ({ branding, widgetId }: { branding: any, widgetId: string },) => {
    const today = new Date();
    const dispatch = useAppDispatch();
    const [currentWeekStart, setCurrentWeekStart] = useState(startOfWeek(today, { weekStartsOn: 1 }));
    const [selectedDate, setSelectedDate] = useState(today);
    const [scheduleData, setScheduleData] = useState<any[]>([]);
    const [loader, startLoader, endLoader] = useLoader()
    const [selectedSlot, setSelectedSlot] = useState<any | null>(null);


    const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i));

    const goToPreviousWeek = () => {
        const newStart = subWeeks(currentWeekStart, 1);
        setCurrentWeekStart(newStart);
        setSelectedDate(newStart);
    };

    const goToNextWeek = () => {
        const newStart = addWeeks(currentWeekStart, 1);
        setCurrentWeekStart(newStart);
        setSelectedDate(newStart);
    };

    useEffect(() => {
        startLoader();
        const formattedDate = format(selectedDate, 'yyyy-MM-dd');
        dispatch(getTrainerAvailability({ date: formattedDate }))
            .unwrap()
            .then((res: any) => {
                const flattened = res.data.trainers.flatMap((trainer: any) =>
                    trainer.timeSlots
                        .filter((slot: any) => slot.availabilityStatus === 'available')
                        .flatMap((slot: any) =>
                            slot.payrates.map((rate: any) => ({
                                trainer: trainer.userName,
                                trainerId: trainer.userId,
                                image: trainer.profilePicture,
                                location: trainer.facilityName,
                                date: trainer.date,
                                from: slot.from,
                                to: slot.to,
                                status: slot.availabilityStatus,
                                privacy: slot.privacy,
                                classType: slot.classType,
                                subtypeName: rate.subtypeName,
                                durationInMinutes: rate.durationInMinutes,
                                serviceName: rate.serviceName,
                                payRateId: rate._id,
                                pricing: rate.pricing,
                                serviceCategory: rate.serviceId,
                                subTypeId:rate.appointmentType
                            }))

                        )
                );

                setScheduleData(flattened);
            })
            .catch((err) => {
                console.error('Error fetching trainer availability:', err);
                setScheduleData([]);
            }).finally(() => {
                endLoader()
            })
    }, [selectedDate]);

    if (selectedSlot) {
        return (
            <TrialBookingFlow
                slot={selectedSlot}
                widgetId={widgetId}
                onBack={() => setSelectedSlot(null)} // back button resets flow
            />
        );
    }

    return (
        <div style={{ fontFamily: branding.fontFamily, padding: '16px' }}>
            {/* Week Navigation */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                <span style={{ color: '#888', cursor: 'pointer' }} onClick={goToPreviousWeek}>
                    ← PREV WEEK
                </span>
                <div style={{ display: 'flex', flex: 1, justifyContent: 'space-around' }}>
                    {weekDays.map((date, idx) => {
                        const isSelected = isSameDay(date, selectedDate);
                        return (
                            <div
                                key={idx}
                                onClick={() => setSelectedDate(date)}
                                style={{
                                    textAlign: 'center',
                                    cursor: 'pointer',
                                    borderBottom: isSelected ? `2px solid ${branding.primaryColor}` : '1px solid #eee',
                                    paddingBottom: 4,
                                    minWidth: 50,
                                }}
                            >
                                <div style={{ fontSize: 11, color: '#999' }}>{format(date, 'EEE').toUpperCase()}</div>
                                <div style={{ fontSize: 14 }}>{format(date, 'd MMM')}</div>
                            </div>
                        );
                    })}
                </div>
                <span style={{ color: '#888', cursor: 'pointer' }} onClick={goToNextWeek}>
                    NEXT WEEK →
                </span>
            </div>

            {/* Schedule List */}
            <Spin spinning={loader}>
                <div style={{ borderTop: '1px solid #eee' }}>
                    {scheduleData.length === 0 ? (
                        <div style={{ textAlign: 'center', color: '#999', padding: '24px 0' }}>
                            No sessions for this day.
                        </div>
                    ) : (
                        scheduleData.map((item, index) => (
                            <div
                                key={index}
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    padding: '16px 0',
                                    borderBottom: '1px solid #eee',
                                }}
                            >
                                {/* Time */}
                                <div style={{ width: 140 }}>
                                    <div style={{ fontWeight: 600 }}>{item.from} - {item.to}</div>
                                    <div style={{ fontSize: 12, color: '#777' }}>{item.durationInMinutes}-Min</div>
                                </div>

                                {/* Avatar */}
                                <img
                                    src={item.image || 'https://staginghop.hkstest.uk/assets/Profile_icon.png'}
                                    alt="avatar"
                                    style={{
                                        width: 48,
                                        height: 48,
                                        borderRadius: '50%',
                                        objectFit: 'cover',
                                        marginRight: 12,
                                    }}
                                />

                                {/* Details */}
                                <div style={{ flex: 1 }}>
                                    <div style={{ fontWeight: 600 }}>{item.subtypeName}</div>
                                    <div style={{ fontSize: 12, color: '#555' }}>{item.trainer}</div>
                                </div>

                                {/* Location */}
                                <div style={{ width: 100, fontSize: 12, color: '#555' }}>
                                    {item.location}
                                </div>

                                {/* Book Now */}
                                <Button
                                    type="default"
                                    style={{
                                        borderColor: '#000',
                                        color: '#000',
                                        borderRadius: 6,
                                        padding: '4px 12px',
                                    }}
                                    onClick={() => setSelectedSlot(item)}
                                >
                                    Book Now
                                </Button>
                            </div>
                        ))
                    )}
                </div>
            </Spin>

        </div>
    );
};

export default PersonalAppointmentTab;
