// TrialBookingFlow/SubtypeDetailComponent.tsx
import React from 'react';
import { Button } from 'antd';

const SubtypeDetailComponent = ({
  slot,
  widgetId,
  onBack,
  onSignupClick,
}: {
  slot: any;
  widgetId: string;
  onBack: () => void;
  onSignupClick: () => void;
}) => {
  if (!slot) return null;
console.log(slot,"slotttting")
  const hasTrialPricing = slot.pricing?.some((p: any) => p.isTrialPricing);

  return (
    <div style={{ padding: 24, fontFamily: "'Poppins', sans-serif" }}>
      <Button
        type="link"
        onClick={onBack}
        style={{ marginBottom: 16, padding: 0 }}
      >
        ← Back
      </Button>

      <div
        style={{
          border: '1px solid #eee',
          borderRadius: 10,
          padding: 24,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
          maxWidth: 600,
          margin: '0 auto',
          background: '#fff',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
          <img
            src={slot.image || 'https://staginghop.hkstest.uk/assets/Profile_icon.png'}
            alt="Trainer"
            style={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              objectFit: 'cover',
              marginRight: 20,
            }}
          />
          <div>
            <h2 style={{ margin: 0 }}>{slot.subtypeName}</h2>
            <p style={{ margin: '4px 0', color: '#555' }}>{slot.trainer}</p>
            <p style={{ margin: 0, fontSize: 12, color: '#888' }}>
              {slot.durationInMinutes}-Min
            </p>
          </div>
        </div>

        <div style={{ fontSize: 14, color: '#333' }}>
          <p><strong>Time:</strong> {slot.from} – {slot.to}</p>
          <p><strong>Location:</strong> {slot.location}</p>
          <p><strong>Status:</strong> {slot.status}</p>
        </div>

        {hasTrialPricing && <Button
          type="primary"
          style={{ marginTop: 24, width: '100%' }}
          onClick={onSignupClick}
        >
          {hasTrialPricing ? 'Book Trial' : 'Book Now'}
        </Button>}

        <Button
          type="default"
          style={{ marginTop: 12, width: '100%' }}
          onClick={() => alert('Login flow coming soon')}
        >
          Login
        </Button>
      </div>
    </div>
  );
};

export default SubtypeDetailComponent;
