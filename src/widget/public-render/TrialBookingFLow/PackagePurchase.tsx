import React from 'react';
import { <PERSON><PERSON>, Card, Typography } from 'antd';
const { Title, Paragraph } = Typography;

const PackagePurchase = ({
  slot,
  widgetId,
  user,
  onBack,
  onPackageSelect,
}: {
  slot: any;
  widgetId: string;
  user: any;
  onBack: () => void;
  onPackageSelect: (pkg: any) => void;
}) => {
  const trialPackages = slot.pricing?.filter((p: any) => p.isTrialPricing);

  return (
    <div style={{ padding: 24, fontFamily: 'Poppins, sans-serif', maxWidth: 700, margin: '0 auto' }}>
      <Button type="link" onClick={onBack} style={{ marginBottom: 16 }}>
        ← Back to Signup
      </Button>

      <Title level={3}>Choose a Trial Package</Title>
      <Paragraph>Select one of the following trial packages to continue.</Paragraph>

      {trialPackages?.map((pkg: any) => (
        <Card
          key={pkg._id}
          title={pkg.name}
          bordered
          style={{ marginBottom: 16 }}
          actions={[
            <Button type="primary" onClick={() => onPackageSelect(pkg)} block>
              Proceed to Pay ₹{pkg.price} + {pkg.tax}% GST
            </Button>,
          ]}
        >
          <Paragraph><strong>Sessions:</strong> {pkg.sessionCount}</Paragraph>
          <Paragraph><strong>Validity:</strong> {pkg.expiredInDays} {pkg.durationUnit}</Paragraph>
          <Paragraph><strong>Description:</strong> {pkg.description || 'No description available.'}</Paragraph>
        </Card>
      ))}
    </div>
  );
};

export default PackagePurchase;
