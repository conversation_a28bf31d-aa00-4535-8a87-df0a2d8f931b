// TrialBookingFlow/TrialBookingFlow.tsx
import React, { useState } from 'react';
import SubtypeDetailComponent from './SubtypeDetailComponent';
import SignupComponent from './signup';
import PackagePurchase from './PackagePurchase';
import RazorpayPayment from './RazorpayPayment';
import AppointmentSummary from './AppointmentSummary';

const TrialBookingFlow = ({ slot, widgetId, onBack }: { slot: any; widgetId: string; onBack: () => void }) => {
  const [step, setStep] = useState<'detail' | 'signup' | 'package' | 'payment' | 'booking'>('detail');
  const [userInfo, setUserInfo] = useState<any>(null);
  const [selectedPackage, setSelectedPackage] = useState<any>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  if (step === 'detail') {
    return (
      <SubtypeDetailComponent
        slot={slot}
        widgetId={widgetId}
        onBack={onBack}
        onSignupClick={() => setStep('signup')}
      />
    );
  }

  if (step === 'signup') {
    return (
      <SignupComponent
        slot={slot}
        widgetId={widgetId}
        onBack={() => setStep('detail')}
        onSignupSuccess={(user: any) => {
          setUserInfo(user);
          setStep('package');
        }}
      />
    );
  }

  if (step === 'package') {
    return (
      <PackagePurchase
        slot={slot}
        widgetId={widgetId}
        user={userInfo}
        onBack={() => setStep('signup')}
        onPackageSelect={(pkg: any) => {
          setSelectedPackage(pkg);
          setStep('payment');
        }}
      />
    );
  }

  if (step === 'payment') {
    return (
      <RazorpayPayment
        slot={slot}
        selectedPackage={selectedPackage}
        user={userInfo}
        widgetId={widgetId}
        onPaymentSuccess={() => setStep('booking')}
        onBack={() => setStep('package')}
      />
    );
  }

  if (step === 'booking') {
    return (
      <AppointmentSummary
        slot={slot}
        userInfo={userInfo}
         widgetId={widgetId}
        selectedPackage={selectedPackage}
        onBack={() => setStep('payment')}
         onResetFlow={onBack}  
      />
    );
  }

  return null;
};

export default TrialBookingFlow;
