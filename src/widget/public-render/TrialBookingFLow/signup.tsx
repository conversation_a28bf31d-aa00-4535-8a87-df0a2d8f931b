import React, { useMemo } from 'react';
import { Button, Checkbox, Form, Input, message } from 'antd';
import { useLoader } from '~/hooks/useLoader';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { EmailOrMobileValidation, registerClient } from '~/redux/actions/widget/widget.action';
import debounce from 'lodash/debounce';

const SignupComponent = ({
  slot,
  onBack,
  widgetId,
  onSignupSuccess,
}: {
  slot: any;
  onBack: () => void;
  widgetId: string;
  onSignupSuccess: (user: any) => void;
}) => {
  const dispatch = useAppDispatch();
  const [loader, startLoader, endLoader] = useLoader();
  const [form] = Form.useForm();

  const validationCache = useMemo(() => new Map<string, boolean>(), []);

  const checkAvailability = async (value: string, isEmail: boolean): Promise<boolean> => {
    if (!value) return true;
    if (validationCache.has(value)) return validationCache.get(value)!;
    try {
      const res = await dispatch(EmailOrMobileValidation({ value })).unwrap();
      const isAvailable = !res?.res?.data;
      validationCache.set(value, isAvailable);
      return isAvailable;
    } catch (err) {
      return true; // fail-safe
    }
  };

  const handleSignup = async () => {
    try {
      const values = await form.validateFields();
      startLoader();

      // ✅ Check email & phone availability manually
      const [emailAvailable, phoneAvailable] = await Promise.all([
        checkAvailability(values.email, true),
        checkAvailability(values.phone, false),
      ]);

      if (!emailAvailable) {
        form.setFields([
          {
            name: 'email',
            errors: ['Email is already in use.'],
          },
        ]);
        endLoader();
        return;
      }

      if (!phoneAvailable) {
        form.setFields([
          {
            name: 'phone',
            errors: ['Mobile number is already in use.'],
          },
        ]);
        endLoader();
        return;
      }

      const payload = {
        type: 'mobile',
        firstName: values.firstName,
        lastName: values.lastName,
        mobile: values.phone,
        organizationId: widgetId,
        isUserAcceptTerms: values.isUserAcceptTerms,
        countryCode: '+91',
        email: values.email,
      };

      const result = await dispatch(registerClient(payload)).unwrap();
      if (result.data.data.user) {
        message.success('Registration successful');
        onSignupSuccess(result.data.data.user);
      }
    } catch (error: any) {
      console.error('Signup error:', error?.message || error);
      if (!error.errorFields) message.error(error?.message || 'Signup failed');
    } finally {
      endLoader();
    }
  };

  return (
    <div
      style={{
        padding: 24,
        fontFamily: "'Poppins', sans-serif",
        maxWidth: 600,
        margin: '0 auto',
      }}
    >
      <Button type="link" onClick={onBack} style={{ marginBottom: 16, padding: 0 }}>
        ← Back to Details
      </Button>

      <h2 style={{ marginBottom: 24 }}>Sign up to Book Trial</h2>

      <Form form={form} layout="vertical">
        <Form.Item
          label="First Name"
          name="firstName"
          rules={[{ required: true, message: 'Please enter your first name' }]}
        >
          <Input placeholder="First name" />
        </Form.Item>

        <Form.Item label="Last Name" name="lastName">
          <Input placeholder="Last name" />
        </Form.Item>

        <Form.Item
          label="Email"
          name="email"
          rules={[
            { type: 'email', message: 'Please enter a valid email' },
            { required: true, message: 'Email is required' },
          ]}
        >
          <Input placeholder="Enter your email" />
        </Form.Item>

        <Form.Item
          label="Phone"
          name="phone"
          rules={[{ required: true, message: 'Please enter your phone number' }]}
        >
          <Input placeholder="Enter your phone number" />
        </Form.Item>

        <Form.Item
          name="isUserAcceptTerms"
          valuePropName="checked"
          rules={[
            {
              validator: (_, value) =>
                value ? Promise.resolve() : Promise.reject(new Error('Please accept terms and conditions')),
            },
          ]}
        >
          <Checkbox>I accept the terms and conditions</Checkbox>
        </Form.Item>

        <Button type="primary" htmlType="button" onClick={handleSignup} loading={loader} block>
          Sign up
        </Button>
      </Form>
    </div>
  );
};

export default SignupComponent;
