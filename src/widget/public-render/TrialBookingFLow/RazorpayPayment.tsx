import { Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { createRazorpayOrder, verifyRazorpayPayment } from '~/redux/actions/widget/widget.action';

declare global {
  interface Window {
    Razorpay: any;
  }
}

const RazorpayPayment = ({
  slot,
  selectedPackage,
  user,
  widgetId,
  onPaymentSuccess,
  onBack,
}: {
  slot: any;
  selectedPackage: any;
  user: any;
  widgetId: string;
  onPaymentSuccess: () => void;
  onBack: () => void;
}) => {
  const dispatch = useAppDispatch();
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);

  // Load Razorpay script
  useEffect(() => {
    const loadRazorpayScript = () => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.async = true;
      script.onload = () => setIsScriptLoaded(true);
      document.body.appendChild(script);
    };

    if (!window.Razorpay) {
      loadRazorpayScript();
    } else {
      setIsScriptLoaded(true);
    }
  }, []);

  const initiatePayment = async () => {
    setIsLoading(true);
    try {
      // Step 1: Request backend to create order
      const res: any = await dispatch(createRazorpayOrder({
        amount: selectedPackage.price,
        packageId: selectedPackage._id,
        widgetId,
        userId: user._id,
      })).unwrap();

      const { orderId, razorpayKey } = res.data || res;

      if (!orderId || !razorpayKey) throw new Error('Order creation failed');

      // Step 2: Configure Razorpay checkout
      const options = {
        key: razorpayKey,
        amount: selectedPackage.price * 100, // Convert to paise
        currency: 'INR',
        name: 'Your Brand Name',
        description: selectedPackage.name,
        order_id: orderId,
        handler: async (response: any) => {
          setIsVerifying(true);
          try {
            const verifyResult: any = await dispatch(verifyRazorpayPayment({
              ...response,
              userId: user._id,
              packageId: selectedPackage._id,
              widgetId,
              amount: selectedPackage.price,
              date: slot.date,
            })).unwrap();

            if (verifyResult?.data.success) {
              onPaymentSuccess();
            } else {
              alert('Payment verification failed. Please contact support.');
            }
          } catch (err) {
            console.error('Verification error', err);
            alert('Payment verification failed due to server error.');
          } finally {
            setIsVerifying(false);
          }
        },
        prefill: {
          name: user.name,
          email: user.email,
          contact: user.phone,
        },
        notes: {
          widgetId,
          packageId: selectedPackage._id,
        },
        theme: {
          color: '#1A3353',
        },
      };

      const rzp = new window.Razorpay(options);
      rzp.open();
    } catch (error) {
      console.error('Payment error', error);
      alert('Payment failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isScriptLoaded) {
      initiatePayment();
    }
  }, [isScriptLoaded]);

  return (
    <div style={{ padding: 24 }}>
      <h2>
        {isVerifying ? 'Verifying payment...' : 'Processing payment...'}
      </h2>
      <p>
        {isVerifying
          ? 'Please wait while we confirm your payment.'
          : isLoading
            ? 'Please wait while the payment window opens.'
            : 'If the payment window doesn\'t open, please click the button below.'}
      </p>

      {!isLoading && isScriptLoaded && !isVerifying && (
        <Button onClick={initiatePayment} type="primary" style={{ marginBottom: 16 }}>
          Open Payment Window
        </Button>
      )}

      {!isVerifying && (
        <Button onClick={onBack} type="link">
          ← Back
        </Button>
      )}
    </div>
  );
};

export default RazorpayPayment;
