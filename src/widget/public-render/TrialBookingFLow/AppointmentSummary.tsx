import React, { useState, useEffect } from 'react';
import { Button, Select, message, Spin } from 'antd';
import { format, parse, addMinutes } from 'date-fns';
import { postApi } from '~/services/api-services';
import { useAppDispatch } from '~/hooks/redux-hooks';
import { bookpersonalAppointment, getTrainerSlot } from '~/redux/actions/widget/widget.action';

const AppointmentSummary = ({
  slot,
  userInfo,
  selectedPackage,
  widgetId,
  onBack,
  onResetFlow
}: {
  slot: any;
  userInfo: any;
  selectedPackage: any;
  widgetId: string;
  onBack: () => void;
  onResetFlow: () => void;

}) => {
  const [availableSlots, setAvailableSlots] = useState<any[]>([]);
  const [selectedFromTime, setSelectedFromTime] = useState<string | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [packageId, setPackageId] = useState(false)
  const dispatch = useAppDispatch();

  useEffect(() => {
    const fetchSlots = async () => {
      setLoading(true);
      const response: any = await dispatch(
        getTrainerSlot({
          trainerId: slot.trainerId,
          date: slot.date,
          serviceCategoryId: slot.serviceCategory,
          subtypeId: slot.subTypeId,
          organizationId: widgetId,
          userId: userInfo._id
        })
      );
      setLoading(false);
      if (response?.payload?.data?.timeSlots) {
        const filtered = response.payload.data.timeSlots.filter((s: any) => s.isAvailable);
        setAvailableSlots(filtered);
        setPackageId(response.payload.data.purchasePackage[0]._id)
      }
    };

    fetchSlots();
  }, [dispatch, slot.trainerId, slot.date, slot.serviceCategory, slot.subTypeId, widgetId]);

  const handleSelectTime = (fromTime: string) => {
    setSelectedFromTime(fromTime);
    const matched = availableSlots.find((s) => s.from === fromTime);
    setSelectedSlot(matched || null);
  };

  const handleConfirm = async () => {
    if (!selectedSlot) {
      message.error('Please select a time');
      return;
    }

    const payload = {
      clientId: userInfo._id,
      trainerId: slot.trainerId,
      pricingId: slot.pricing.find((p: any) => p.isTrialPricing)?._id,
      purchaseId: packageId,
      date: slot.date,
      from: selectedSlot.from,
      to: selectedSlot.to,
      "classType": "personalAppointment",
      "dateRange": "Single",
      serviceCategory: slot.serviceCategory,
      subType: slot.subTypeId,
      "duration": slot.durationInMinutes,
      "checkIn": false,
      organizationId: widgetId
    };

    try {
      const response = await dispatch(bookpersonalAppointment(payload)).unwrap();
      console.log(response, "response")
      if (response.status === 200) {
        message.success('Appointment confirmed!');
        setTimeout(() => {
          onResetFlow(); 
        }, 1000);
      }
    } catch (err) {
      message.error('Booking failed.');
    }
  };

  const selectedEnd = selectedSlot?.to;
  const duration = selectedSlot?.durationInMinutes || slot.durationInMinutes;

  return (
    <div style={{ maxWidth: 600, margin: '0 auto', padding: 24, fontFamily: 'Poppins' }}>
      <h3 style={{ textAlign: 'center', marginBottom: 16 }}>
        {format(new Date(slot.date), 'EEE MMM dd yyyy')}
      </h3>
      <h2 style={{ textAlign: 'center', marginBottom: 0 }}>{slot.subtypeName}</h2>
      <p style={{ textAlign: 'center' }}>
        {selectedFromTime} – {selectedEnd || '--'} ({duration} Mins)
      </p>

      <hr style={{ margin: '24px 0' }} />

      <div style={{ marginBottom: 24 }}>
        <strong>STAFF</strong>
        <div style={{ display: 'flex', alignItems: 'center', marginTop: 8 }}>
          <img
            src={slot.image || 'https://staginghop.hkstest.uk/assets/Profile_icon.png'}
            alt="trainer"
            style={{ width: 40, height: 40, borderRadius: '50%', marginRight: 12 }}
          />
          <span>{slot.trainer}</span>
        </div>
      </div>

      <div style={{ marginBottom: 24 }}>
        <strong>LOCATION</strong>
        <div style={{ marginTop: 4 }}>{slot.location}</div>
      </div>

      <div style={{ marginBottom: 24 }}>
        <strong>DETAILS</strong>
        <div style={{ marginTop: 8 }}>
          <label>Start Time</label>
          <Select
            value={selectedFromTime}
            onChange={handleSelectTime}
            style={{ width: '100%', marginTop: 4 }}
            placeholder="Select time"
            loading={loading}
          >
            {availableSlots.map((s) => (
              <Select.Option key={s.from} value={s.from}>
                {format(parse(s.from, 'HH:mm', new Date()), 'hh:mm a')}
              </Select.Option>
            ))}
          </Select>
          <div style={{ marginTop: 8 }}>
            End time: <strong>{selectedEnd || '--'}</strong>
          </div>
          <div>Appointment Length: {duration} Mins</div>
        </div>
      </div>

      <Button type="primary" block onClick={handleConfirm} disabled={!selectedSlot}>
        Book Appointment
      </Button>
    </div>
  );
};

export default AppointmentSummary;
