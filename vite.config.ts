// vite.config.ts
import { defineConfig } from 'vite';
import fs from 'fs/promises';
import path, { resolve } from 'path';
import react from '@vitejs/plugin-react';
import eslint from 'vite-plugin-eslint';
import removeConsole from 'vite-plugin-remove-console';
import svgr from 'vite-plugin-svgr';
import typescript from '@rollup/plugin-typescript';

export default defineConfig({
  plugins: [
    react(),
    svgr({ exportAsDefault: true }),
    eslint(),
    removeConsole(),
    typescript({ tsconfig: './tsconfig.json' }),
  ],
  server: {
    port: 3000,
    open: true,
    host: true,
  },
  resolve: {
    alias: [{ find: '~', replacement: path.resolve(__dirname, 'src') }],
  },
  build: {
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'), // Main app
        widget: resolve(__dirname, 'src/widget/public-render/index.tsx'), // Widget entry
      },
      output: {
        entryFileNames: (chunk) =>
          chunk.name === 'widget' ? 'widgets/render.js' : '[name].js',
      },
    },
    outDir: 'dist',
    emptyOutDir: true,
  },
});
